import { Component, OnInit } from '@angular/core';
import { TenantsService } from './services/tenants.service';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { EditTenantModuleComponent } from './edit-tenant-module/edit-tenant-module.component';

@Component({
  selector: 'app-tenants',
  templateUrl: './tenants.component.html',
  styleUrl: './tenants.component.scss'
})
export class TenantsComponent implements OnInit {

  tenantsList: [];
  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;
  langDirection: 'ltr' | 'rtl' = 'ltr';
  constructor(private tenants: TenantsService, private translate: TranslateService, private modalService: NzModalService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDirection(this.lang)

    })

  }
  ngOnInit(): void {
    this.getTenants();
  }
  sortFnName = (a: any, b: any) => {
    return a.name.localeCompare(b.name)
  }

  sortFnEmail = (a: any, b: any) => {
    return a.name.localeCompare(b.name)
  }
  sortFnNumber = (a: any, b: any) => {
    return a.hotLine - b.hotLine
  }
  sortFnNActive = (a: any, b: any) => {
    return a.isActive - b.isActive
  }

  getTenants() {
    this.tenants.getTenants().subscribe(res => {

      this.tenantsList = res.data;

    })
  }
  changeActivation(data: any) {
    data = { "Id": data.tenantId, "IsActive": !data.isActive }
    this.tenants.changeActivation(data).subscribe(res => {
      this.getTenants();

    })
  }

  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  editTenantModule(data:any): void {
    this.modalService.create({
      nzContent: EditTenantModuleComponent,
      nzDirection: this.langDirection,
      nzData:data
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getTenants();
      }
    })
  }

}
