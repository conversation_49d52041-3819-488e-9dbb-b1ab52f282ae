import { Component, inject } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { UsersService } from '../services/users.service';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";
import {branch} from "../../../core/models";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-edit-user',
  templateUrl: './edit-user.component.html',
  styleUrl: './edit-user.component.scss'
})
export class EditUserComponent {
  branchList: branch[] = []
  form: FormGroup;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private user: UsersService, private fb: FormBuilder, private translate: TranslateService, private messageService: MessageService, private branches : BranchService) {
    this.form = this.fb.group({
      id: [this.nzModalData?.data.userId],
      userName: [this.nzModalData?.data.userName, Validators.required],
      email: [this.nzModalData?.data.email, [Validators.required, Validators.email]],
      phoneNumber: [this.nzModalData?.data.phoneNumber],
      branchId: [this.nzModalData?.data.branch, Validators.required],
    });
  }

  ngOnInit(): void {
    this.getAllBranches();
  }

  getAllBranches () {
    this.branches.getBranches().subscribe(res => {
      this.branchList = res.data;
    })
  }

  // controlling identity
  get branchControl(): FormControl {
    return this.form.get('branchId') as FormControl;
  }

  onSelectedBranch(selected: any): void {
    this.form.patchValue({
      branchId: selected.id,
    });
  }

  destroyModal(): void {
    this.modal.destroy();
  }
  createFormData() {
    let newData = new FormData();
    Object.entries(this.form.getRawValue()).forEach(([key, value]: any) => {
      newData.append(key, value);
    });
    return newData;
  }

  onSubmit() {
    if (this.form.valid) {
      let data = this.createFormData();
      this.user.updateUsersData(data).subscribe(res => {
        if (res.message == "User updated successfully") {
          this.translate.get('TOAST.USER_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else if (res.message == "No data changed") {
          this.translate.get('TOAST.NO_CHANGES_HAPPENED').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
