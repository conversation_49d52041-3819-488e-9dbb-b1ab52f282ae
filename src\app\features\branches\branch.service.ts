import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../core/models';
import { branch } from '../../core/models/branch';
import { BehaviorSubject, Observable, tap } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class BranchService {
  private selectedBranchId = new BehaviorSubject<string>('');
  selectedBranchId$ = this.selectedBranchId.asObservable();

  private branchListSubject = new BehaviorSubject<branch[]>([]);
  public branchList$ = this.branchListSubject.asObservable();

  constructor(private httpClient: HttpClient) {}

  setBranchId(id: string | undefined) {
    if (id === 'All' || id == null) {
      this.selectedBranchId.next('');
    } else {
      this.selectedBranchId.next(id);
    }
    console.log('Selected branch:', this.selectedBranchId.getValue());
  }

  getCurrentBranchId(): string {
    return this.selectedBranchId.getValue() || '';
  }

  // This one loads and updates the BehaviorSubject
  loadBranches(): void {
    this.httpClient.get<IBaseResponse<branch[]>>('/Branches').subscribe(res => {
      this.branchListSubject.next(res.data);
    });
  }

  // Single-use getter (no state update)
  getBranches(): Observable<IBaseResponse<branch[]>> {
    return this.httpClient.get<IBaseResponse<branch[]>>('/Branches');
  }

  getBranchById(id: string) {
    return this.httpClient.get<IBaseResponse<branch>>(`/Branches/${id}`);
  }

  addBranch(data: any): Observable<IBaseResponse<any>> {
    return this.httpClient.post<IBaseResponse<any>>(`/Branches`, data).pipe(
      tap(() => this.loadBranches())
    );
  }

  deleteBranch(id: number): Observable<IBaseResponse<any>> {
    return this.httpClient.delete<IBaseResponse<any>>(`/Branches/${id}`).pipe(
      tap(() => this.loadBranches())
    );
  }

  editBranch(id: number, data: any): Observable<IBaseResponse<any>> {
    return this.httpClient.put<IBaseResponse<any>>(`/Branches/${id}`, data).pipe(
      tap(() => this.loadBranches())
    );
  }
}
