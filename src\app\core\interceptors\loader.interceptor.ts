import {Injectable, Renderer2, RendererFactory2} from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import {Observable} from 'rxjs';
import {ClientHeaders} from '../enums/client-headers.enum';
import {finalize} from 'rxjs/operators';
import { NgxSpinnerService } from 'ngx-spinner';

@Injectable()
export class LoaderInterceptor implements HttpInterceptor {

  constructor(private spinner: NgxSpinnerService) {

  }

  intercept(request: HttpRequest<unknown>, next: HttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    if (request.headers.has(ClientHeaders.ignoreSplashScreen)) {
      return next.handle(request);
    }
    this.spinner.show();
    
    return next.handle(request).pipe(finalize(() => {
        this.spinner.hide();
    }));
  }
}
