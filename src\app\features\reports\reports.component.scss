/* <PERSON><PERSON> Styles */
.custom-raised-btn {
  padding: 2rem 2.5rem;
  font-size: 1.9rem;
  font-weight: 500;
  text-align: center;
  color: #007bff;
  background-color: #ffffff;
  border: none;
  border-radius: 0.8rem;
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
  0px 2px 2px 0px rgba(0, 0, 0, 0.14),
  0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.2s, background-color 0.2s;
}

.custom-raised-btn:hover {
  background-color: #f7f8fd;
  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
  0px 8px 10px 1px rgba(0, 0, 0, 0.14),
  0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}
