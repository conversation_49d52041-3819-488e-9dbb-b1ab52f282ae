<div class="erp-card erp-shadow-end">
  <div class="row my-3 px-3 align-items-center">
    <!-- Search Input -->
    <div class="col-12 col-md-4 mb-2 mb-md-0">
      <input type="text" id="search" class="form-control"
             placeholder="{{ 'SHARED.TYPEHERETOSEARCH' | translate }}"
             name="search" [(ngModel)]="searchValue">
    </div>

    <!-- Action Buttons -->
    <div class="col-12 col-md-8 text-end d-flex justify-content-md-end gap-2 flex-wrap">
      <button *ngIf="selectedCost" (click)="deleteCost()" class="btn btn-danger text-white">
        {{ 'COST.DELETE' | translate }}
      </button>

      <button *ngIf="selectedCost" (click)="updateCostModal()" class="btn btn-primary">
        {{ 'COST.UPDATE' | translate }}
      </button>

      <button *ngIf="shouldShowAddButton()" (click)="addCostModal()" class="btn btn-primary">
        + {{ 'COST.ADDCOST' | translate }}
      </button>
    </div>
  </div>


  <nz-tree #nzTreeComponent [nzData]="costList | treeFilters: {costCenterNumber: searchValue, costCenterName:searchValue}" [nzCheckStrictly]="true" [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
    <ng-template #nzTreeTemplate let-node let-origin="origin">
        <div class="d-flex align-items-end">
            <input type="checkbox" [checked]="origin.checked" id={{origin.id}} name={{origin.id}} class="mb-3"
                (click)="mycheck($event,origin)">
            <nz-table #basicTable [nzData]="['']" [nzShowPagination]="false" nzTableLayout="fixed">
                <thead *ngIf="costList[0].id == origin.id">
                    <tr>
                        <th nzColumnKey="name">{{'COST.COSTNUMBER'|translate}}</th>
                        <th nzColumnKey="name">{{'COST.COSTCENTERNAME'|translate}}</th>
                        <th nzColumnKey="name">{{'COST.ACCOUNTOPENINGDATE'|translate}}</th>

                        <th nzColumnKey="name">{{'COST.COMMENTS'|translate}}</th>


                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{origin.originalData.costCenterNumber}}</td>
                        <td>{{origin.originalData.costCenterName}}</td>
                        <td><span *ngIf="!showARDate">{{origin.originalData.openingBalanceDate|date:'fullDate'}}</span>
                            <span *ngIf="showARDate"> {{origin.originalData.arabicAccountOpeningDate|
                                arabicNumerals}}</span></td>
                        <td>{{origin.originalData.notes}}</td>

                    </tr>
                </tbody>
            </nz-table>
        </div>
    </ng-template>

    <div #pdfTable id="pdfTable" class="pdf-container">
      <!-- PDF Header -->
      <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
        <h2>{{ 'PDF.COSTSREPORTS' | translate }}</h2>
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
          <div>
            {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
          </div>
          <div>
            {{ 'PDF.REF' | translate }}: {{ currentRef }}
          </div>
        </div>
      </div>

      <!-- PDF Table -->
      <table class="table table-bordered pdf-table">
        <thead>
        <tr class="text-blue-500">
          <th>{{'COST.COSTNUMBER'|translate}}</th>
          <th>{{'COST.COSTCENTERNAME'|translate}}</th>
          <th>{{'COST.ACCOUNTOPENINGDATE'|translate}}</th>
          <th>{{'COST.COMMENTS'|translate}}</th>
          <th>{{ 'ACCOUNT.LEVEL' | translate }}</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let cost of flattenedCost">
            <td>{{ cost.costCenterNumber }}</td>
          <td>{{ lang == "en" ? cost.costCenterName : cost.costCenterName }}</td>
          <td>{{ cost.openingBalanceDate | date:'yyyy-MM-dd' }}</td>
          <td>{{ cost.notes }}</td>
          <td>{{ cost.level }}</td>
        </tr>
        </tbody>
      </table>

    </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
