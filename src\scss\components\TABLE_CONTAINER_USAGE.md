# Universal Table Container Usage Guide

## Overview
The `table-container` class provides a unified, modern table styling system that can be applied to any component for consistent UI across the application.

## Quick Start

### 1. Add the CSS Class
Simply add the `table-container` class to your component's main wrapper:

```html
<div class="table-container">
  <!-- Your content here -->
</div>
```

### 2. Use the Predefined Structure
Follow this HTML structure for optimal results:

```html
<div class="table-container">
  <!-- Breadcrumb (optional) -->
  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
  
  <!-- Page Title -->
  <h3 class="page-title">{{ "PAGE.TITLE" | translate }}</h3>
  
  <!-- Form Section (optional) -->
  <div class="form-section">
    <form [formGroup]="form">
      <div class="form-row">
        <label class="form-label">{{ "LABEL" | translate }}</label>
        <!-- Form controls -->
      </div>
    </form>
    <div class="submit-section">
      <button class="btn-submit">{{ "SUBMIT" | translate }}</button>
    </div>
  </div>
  
  <!-- Table Controls -->
  <div class="table-controls">
    <div class="controls-row">
      <div class="show-entries">
        <span>{{ "SHARED.SHOW" | translate }}</span>
        <select class="form-select" [(ngModel)]="pageSize">
          <option value="5">5</option>
          <option value="10">10</option>
          <option value="50">50</option>
        </select>
      </div>
      <div class="search-input">
        <input type="text" class="form-control" 
               placeholder="{{ 'SHARED.TYPEHERETOSEARCH' | translate }}">
      </div>
      <button class="add-button">+ {{ "ADD_NEW" | translate }}</button>
    </div>
  </div>
  
  <!-- Table Section -->
  <div class="table-section">
    <nz-table [nzData]="data" [nzPageSize]="pageSize">
      <thead>
        <tr>
          <th>{{ "COLUMN1" | translate }}</th>
          <th>{{ "COLUMN2" | translate }}</th>
          <th>{{ "ACTIONS" | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of basicTable.data">
          <td>{{ item.name }}</td>
          <td>{{ item.value }}</td>
          <td>
            <div class="action-dropdown">
              <span class="dropdown-trigger" data-bs-toggle="dropdown">
                <!-- Three dots icon -->
              </span>
              <ul class="dropdown-menu">
                <li><button class="erp-btn erp-btn-primary">Edit</button></li>
                <li><button class="erp-btn erp-btn-gray">Delete</button></li>
              </ul>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
  
  <!-- Export Section -->
  <div class="export-section">
    <app-export-pdf-button [tableElement]="pdfTable"></app-export-pdf-button>
  </div>
</div>
```

## Features Included

### ✅ Responsive Design
- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly controls

### ✅ RTL/LTR Support
- Automatic text direction handling
- Proper layout for Arabic and English

### ✅ Enhanced Form Controls
- Styled multiselect dropdowns
- Enhanced date pickers
- Consistent input styling

### ✅ Modern Table Styling
- Gradient headers
- Hover effects
- Alternating row colors
- Enhanced status badges

### ✅ Action Components
- Styled dropdown menus
- Enhanced buttons with animations
- Status badges with gradients

### ✅ Loading & Empty States
- Consistent loading indicators
- Empty state messaging
- Error state handling

## Component Integration

### TypeScript Setup
```typescript
import { MenuItem } from 'primeng/api';

export class YourComponent {
  breadcrumbItems: MenuItem[] = [];
  pageSize: number = 10;
  searchValue: string = '';
  
  ngOnInit() {
    this.setupBreadcrumb();
  }
  
  setupBreadcrumb() {
    this.translate.get(['MENU.ITEM', 'PAGE.TITLE'])
      .subscribe((translations) => {
        this.breadcrumbItems = [
          { label: translations['MENU.ITEM'], routerLink: '/menu' },
          { label: translations['PAGE.TITLE'] }
        ];
      });
  }
}
```

### SCSS Setup
```scss
// Your component SCSS file
.your-component-container {
  // All styling is handled by 'table-container' class
  // Add component-specific overrides here if needed
}
```

## Status Badges
Use these classes for consistent status indicators:

```html
<span class="status-badge active">Active</span>
<span class="status-badge inactive">Inactive</span>
```

## Table Content Styling
Special classes for enhanced table content:

```html
<!-- Account information -->
<div class="account-info">
  <span class="account-number">12345</span>
  <span class="account-name">Account Name</span>
</div>

<!-- Amounts -->
<span class="amount debit">1,234.56</span>
<span class="amount credit">1,234.56</span>

<!-- Document types -->
<span class="document-type">Invoice</span>

<!-- Dates -->
<span class="date">2024-01-15</span>
```

## Examples
- ✅ Users Component: `src/app/features/users/`
- ✅ All Final Reports: `src/app/features/reports/all-final-reports/`

## Migration Guide
To migrate existing components:

1. Replace your wrapper class with `table-container`
2. Update HTML structure to match the predefined classes
3. Remove component-specific table styling
4. Test responsive behavior and RTL support

## Support
For questions or issues with the table container system, refer to:
- Main styles: `src/scss/components/_table.scss`
- Example implementations in users and reports components
