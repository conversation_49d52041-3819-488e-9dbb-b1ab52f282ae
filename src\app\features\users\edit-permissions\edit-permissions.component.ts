import { Component, inject } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { UsersService } from '../services/users.service';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-edit-permissions',
  templateUrl: './edit-permissions.component.html',
  styleUrl: './edit-permissions.component.scss'
})
export class EditPermissionsComponent {
  isChecked = true
  userPermissions: any;
  PermissionsList: any;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private user: UsersService, private translate: TranslateService, private messageService: MessageService) {
    this.getPermissionsList();

  }

  getPermissionsList() {
    this.user.getPermissions().subscribe(res => {
      this.PermissionsList = res.data.modulePartitions;
      this.getUserPermissions();
    })
  }
  getUserPermissions() {

    this.user.getUserPermissions(this.nzModalData.id).subscribe(res => {
      this.userPermissions = res.data
      for (let i = 0; i < this.PermissionsList.length; i++) {
        for (let j = 0; j < this.PermissionsList[i].permissionsLis.length; j++) {
          this.PermissionsList[i].permissionsLis[j].isChecked = false
          if (this.userPermissions.includes(this.PermissionsList[i].permissionsLis[j].id)) {
            this.PermissionsList[i].permissionsLis[j].isChecked = true
          }
        }
      }
    })
  }
  updatePermissions(PermissionsList: any) {
    let updatedPermissions = []

    for (let i = 0; i < PermissionsList.length; i++) {
      for (let j = 0; j < this.PermissionsList[i].permissionsLis.length; j++) {
        if (this.PermissionsList[i].permissionsLis[j].isChecked) {
          updatedPermissions.push(this.PermissionsList[i].permissionsLis[j].id)
        }
      }

    }
    let data = {
      apiPermissionIds: updatedPermissions,
      userId: this.nzModalData.id
    }

    this.user.updateUserPermissions(data).subscribe(res => {
      if (res.message == "User Permission Is Updated") {
        this.translate.get('TOAST.USER_PERMISSION_UPDATED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.modal.destroy();
    })
  }
  destroyModal(): void {
    this.modal.destroy();
  }
}
