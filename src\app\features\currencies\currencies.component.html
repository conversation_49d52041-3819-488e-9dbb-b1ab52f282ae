<div class="erp-card erp-shadow-end">
    <div class="my-3 row px-3">
        <div class="col-sm-12 col-md-6">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    {{'SHARED.SHOW' |translate}}
                </div>
                <div class="mx-3">
                    <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-3">
            <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                name="search" [(ngModel)]="searchValue">
        </div>
        <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end"><button (click)="addNewCurrency()"
                class="btn btn-primary">+
                {{'CURRENCIES.ADDNEWCURRENCY' |translate}}</button></div>
    </div>

    <nz-table #basicTable #sortTable [nzPageSize]="pageSize"
        [nzData]="currenciesList | tablefilters: {currencyCode: searchValue, currencyNameEn:searchValue, currencyNameAr:searchValue
        , currencyNickNameEn:searchValue, currencyNickNameAr:searchValue, conversionRate:searchValue , notes:searchValue}" nzTableLayout="fixed">
        <thead>
            <tr>
                <th nzColumnKey="name" [nzSortFn]="sortFnCurrencyCode">{{'CURRENCIES.CURRECNYCODE'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnCrEN">{{'CURRENCIES.CURRENCYNAMEEN'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnCrAR">{{'CURRENCIES.CURRECENCYNAMEAR'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFncrnicknameen">{{'CURRENCIES.CURRENCYNICKNAMEEN'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFncrnicknamear">{{'CURRENCIES.CURRENCYNICKNAMEAR'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnConversionRate">{{'CURRENCIES.CONVERSIONRATE'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNActive">{{'CURRENCIES.STATUS'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNotes">{{'CURRENCIES.NOTES'|translate}}</th>
                <th nzColumnKey="name">{{'CURRENCIES.ACTIONS'|translate}}</th>
            </tr>
        </thead>
        <tbody>

            <tr *ngFor="let data of basicTable.data">
                <td>{{ data.currencyCode }}</td>
                <td>{{ data.currencyNameEn }}</td>
                <td>{{ data.currencyNameAr}}</td>
                <td>{{ data.currencyNickNameEn}}</td>
                <td>{{ data.currencyNickNameAr}}</td>
                <td>{{ data.conversionRate}}</td>
                <td> <button *ngIf=" data.isActive" class="btn btn-danger btn-sm text-white" disabled>
                        {{'SHARED.ACTIVE'|translate}}</button>
                    <button *ngIf="!data.isActive" class="btn btn-primary btn-sm text-white"
                        disabled>{{'SHARED.INACTIVE'|translate}}</button>
                </td>
                <td>{{ data.notes}}</td>
                <td><span data-bs-toggle="dropdown" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg"
                            width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical"
                            viewBox="0 0 16 16">
                            <path
                                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                        </svg></span>
                    <ul class="dropdown-menu">
                        <li class="p-2"><button (click)="EditCurrency(data)"
                                class="erp-btn erp-btn-primary">{{"SHARED.EDIT"|translate}} </button>
                        </li>
                        <li class="p-2"><button (click)="delete(data.id)"
                                class="erp-btn erp-btn-danger">{{"SHARED.DELETE"|translate}} </button>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </nz-table>

    <div #pdfTable id="pdfTable" class="pdf-container">
      <!-- PDF Header -->
      <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightgray; padding-bottom: 10px;">
        <h2>{{ 'PDF.CURRIENCYREPORTS' | translate }}</h2>
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
          <div>
            {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
          </div>
          <div>
            {{ 'PDF.REF' | translate }}: {{ currentRef }}
          </div>
        </div>
      </div>

      <!-- PDF Table -->
      <table class="table table-bordered pdf-table" data-columns="5">
        <thead>
        <tr>
          <th>{{'CURRENCIES.CURRECNYCODE'|translate}}</th>
          <th>{{'CURRENCIES.CURRENCYNAME'|translate}}</th>
          <th>{{'CURRENCIES.CONVERSIONRATE'|translate}}</th>
          <th>{{'CURRENCIES.STATUS'|translate}}</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let currency of flatCurrenciesList">
          <td>{{ currency.currencyCode }}</td>
          <td>{{ lang == "en" ? currency.currencyNameEn : currency.currencyNameAr }}</td>
          <td>{{ currency.conversionRate }}</td>
          <td> <button *ngIf=" currency.isActive" class="btn btn-danger btn-sm text-white" disabled>
            {{'SHARED.ACTIVE'|translate}}</button>
            <button *ngIf="!currency.isActive" class="btn btn-primary btn-sm text-white"
                    disabled>{{'SHARED.INACTIVE'|translate}}</button>
          </td>
        </tr>
        </tbody>
      </table>

    </div>
    <div class="row mt-3 text-center">
      <app-export-pdf-button
        [tableElement]="pdfTable"
      >
      </app-export-pdf-button>
    </div>
</div>
