<div *nzModalTitle> {{'USERS.TABLE.ADDUSER'|translate}}</div>
<form [formGroup]="form">
    <div class="container">
        <div class="form-group">
            <label>{{'ADDUSERFORM.USERNAME'|translate}}</label>
            <input type="text" id="userName" class="form-control" formControlName="userName" name="username">
            <ng-container *ngIf="form && form?.get('userName')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="form && form?.get('userName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ADDUSERFORM.EMAIL'|translate}}</label>
            <input type="text" id="email" class="form-control" formControlName="email" name="email">
            <ng-container *ngIf="form && form?.get('email')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="form && form?.get('email')?.hasError('required')">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
                <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('email')">
                    {{'ADDUSERFORM.INVALIDEMAIL'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ADDUSERFORM.PHONENUMBER'|translate}}</label>
            <input type="number" id="phoneNumber" class="form-control" formControlName="phoneNumber" name="phoneNumber">
        </div>

      <div class="form-group">
        <label>{{ 'SIDEBAR.BRANCHES'|translate }}</label>
        <app-reusable-dropdown
          [options]="branchList"
          [displayKey]="'branch_Name'"
          [valueKey]="'branchId'"
          [formControl]="branchControl"
          [selectedId]="form?.get('branchId')?.value"
          (selectedValue)="onSelectedBranch($event)">
        </app-reusable-dropdown>
        <div *ngIf="form.get('branchId')?.invalid && (form.get('branchId')?.dirty || form.get('branchId')?.touched)">
          <small class="text-danger" *ngIf="form.get('branchId')?.errors?.['required']">
            Phone number is required.
          </small>
        </div>
      </div>



    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="onSubmit()">{{'SHARED.UPDATE'|translate}}</button>
    </div>
</form>
