import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../core/models';
import {BranchService} from "../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class StakeholdersService {

  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }
  getRegions() {
    return this.httpClient.get<IBaseResponse<any>>(`/Regions`);
  }
  getCountries() {
    return this.httpClient.get<IBaseResponse<any>>(`/Countries`);
  }
  getCities() {
    return this.httpClient.get<IBaseResponse<any>>(`/Cities`);
  }
  getTowns() {
    return this.httpClient.get<IBaseResponse<any>>(`/Towns`);
  }
  addDelegate(body: any) {
    if (this.branchId) {
      body.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Delegates`, body);
  }
  getDelegate() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Delegates');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Delegates?branchId=${this.branchId}`);
    }
  }
  getDelegateById(id: any) {
    return this.httpClient.get<IBaseResponse<any>>(`/Delegates/${id}`);
  }
  editDelegate(id: any, body: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Delegates/${id}`, body);
  }
  deleteDelegate(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Delegates/${id}`);
  }

  addSupplier(body: any) {
    if (this.branchId) {
      body.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Suppliers`, body);
  }
  getSupplier() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Suppliers');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Suppliers?branchId=${this.branchId}`);
    }
  }
  getSupplierById(id: any) {
    return this.httpClient.get<IBaseResponse<any>>(`/Suppliers/${id}`);
  }
  editSupplier(id: any, body: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Suppliers/${id}`, body);
  }
  deleteSupplier(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Suppliers/${id}`);
  }

  addClient(body: any) {
    if (this.branchId) {
      body.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Clients`, body);
  }
  getClient() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Clients');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Clients?branchId=${this.branchId}`);
    }
  }
  getClientById(id: any) {
    return this.httpClient.get<IBaseResponse<any>>(`/Clients/${id}`);
  }
  editClient(id: any, body: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Clients/${id}`, body);
  }
  deleteClient(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Clients/${id}`);
  }
}
