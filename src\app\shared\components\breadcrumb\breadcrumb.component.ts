import {Component, Input, ViewEncapsulation} from '@angular/core';
import {MenuItem} from "primeng/api";

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrl: './breadcrumb.component.scss',
  encapsulation: ViewEncapsulation.None,
})
export class BreadcrumbComponent {
  @Input() items: MenuItem[] = [];
  ngOnInit() {
    if (this.items.length > 0) {
      this.items[this.items.length - 1];
    }
  }
}
