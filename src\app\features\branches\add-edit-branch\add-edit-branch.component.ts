import { Component, inject } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { BranchService } from '../branch.service';
import { IdentityService } from '../../identity/identity.service';

@Component({
  selector: 'app-add-edit-branch',
  templateUrl: './add-edit-branch.component.html',
  styleUrl: './add-edit-branch.component.scss'
})
export class AddEditBranchComponent {
  form: FormGroup;
  data: any;
  isUpdate: boolean = false;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  identityList: any;
  constructor(private modal: NzModalRef, private fb: FormBuilder, private branch: BranchService, private identity: IdentityService) {

  }
  ngOnInit(): void {
    this.createForm();
    this.getIdentities();
  }
  addorEditBranch() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      if (!this.isUpdate) {

        this.branch.addBranch(data).subscribe(res => {
          this.modal.destroy(res);
        })
      } else {
        this.branch.editBranch(data.id, data).subscribe(res => {
          this.modal.destroy(res);
        })
      }
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  createForm() {
    this.data = this.nzModalData;
    if (this.data) {
      this.isUpdate = true;
    }
    this.form = this.fb.group({
      id: [this.data?.id],
      branch_Name: [this.data?.branch_Name],
      branch_Name_latin: [this.data?.branch_Name_latin],
      adress: [this.data?.adress || ''],
      telephone: [this.data?.telephone || ''],
      fax: [this.data?.fax || ''],
      mobile: [this.data?.mobile || ''],
      email: [this.data?.email || ''],
      contact_Name: [this.data?.contact_Name || ''],
      account_No: [this.data?.account_No || ''],
      vat_Reg: [this.data?.vat_Reg || ''],
      commercial_Register: [this.data?.commercial_Register || ''],
      country_Code: [this.data?.country_Code || ''],
      city: [this.data?.city || ''],
      region: [this.data?.region || ''],
      district: [this.data?.district || ''],
      street: [this.data?.street || ''],
      postalCode: [this.data?.postalCode || ''],
      building_No: [this.data?.building_No || ''],
      additionalStreetAdress: [this.data?.additionalStreetAdress || ''],
      identity_No: [this.data?.identity_No || null],
      identityTypeId: [this.data?.identityTypeId || ''],

    });
  }

  getIdentities() {
    this.identity.getIdentity().subscribe(res => {
      this.identityList = res.data;
    })
  }
  destroyModal(): void {
    this.modal.destroy();
  }

  // controlling identity
  get identityControl(): FormControl {
    return this.form.get('identity_No') as FormControl;
  }

  onSelectedIdentity(selected: any): void {
    console.log(selected)

    this.form.patchValue({
      identityTypeId: selected.id,
      identity_No: selected.identity_Code
    });
  }
}
