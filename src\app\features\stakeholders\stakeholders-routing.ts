import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SuppliersComponent } from './suppliers/suppliers.component';
import { ClientsComponent } from './clients/clients.component';
import { DelegatesComponent } from './delegates/delegates.component';
import { AddEditDelegateComponent } from './delegates/add-edit-delegate/add-edit-delegate.component';
import { AddEditClientComponent } from './clients/add-edit-client/add-edit-client.component';
import { AddEditSupplierComponent } from './suppliers/add-edit-supplier/add-edit-supplier.component';


const routes: Routes = [{
    path: 'suppliers', component: SuppliersComponent
},
{ path: 'clients', component: ClientsComponent },
{
    path: 'delegates', component: DelegatesComponent,
},
{
    path: 'add-delegate', component: AddEditDelegateComponent,
},
{
    path: 'edit-delegate/:id', component: AddEditDelegateComponent,
},
{
    path: 'add-supplier', component: AddEditSupplierComponent,
},
{
    path: 'edit-supplier/:id', component: AddEditSupplierComponent,
},
{
    path: 'add-client', component: AddEditClientComponent,
},
{
    path: 'edit-client/:id', component: AddEditClientComponent,
}];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})

export class StakeholdersRoutingModule { }
