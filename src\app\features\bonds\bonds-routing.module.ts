import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AddEditNotePayableComponent } from './add-edit-note-payable/add-edit-note-payable.component';
import { JournalEntriesComponent } from './journal-entries/journal-entries.component';
import { DebitNoteComponent } from './debit-note/debit-note.component';
import { CreditNoteComponent } from './credit-note/credit-note.component';
import { CashReceiptComponent } from './cash-receipt/cash-receipt.component';
import {AddJournalEntiresComponent} from "./journal-entries/add-journal-entires/add-journal-entires.component";
import {AllBondsComponent} from "./all-bonds/all-bonds.component";


const routes: Routes = [
  {
    path: 'note-payable', component: AddEditNotePayableComponent
  },
  {
    path: 'cash-receipt', component: CashReceiptComponent
  },
  {
    path: 'credit-note', component: CreditNoteComponent
  },
  {
    path: 'all-bonds', component: AllBondsComponent
  },
  {
    path: 'debit-note', component: DebitNoteComponent
  }
  , {
    path: 'add-journal-entries', component: AddJournalEntiresComponent
  },{
    path: 'journal-entries', component: JournalEntriesComponent
  },
  ];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class BondsRoutingModule { }

