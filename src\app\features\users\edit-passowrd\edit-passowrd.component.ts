import { Component, inject } from '@angular/core';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { UsersService } from '../services/users.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-edit-passowrd',
  templateUrl: './edit-passowrd.component.html',
  styleUrl: './edit-passowrd.component.scss'
})
export class EditPassowrdComponent {
  form: FormGroup;
  visible = false;
  inputType = 'password';
  visibleNewPassword = false;
  inputTypeNewPassword = 'password';
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private user: UsersService, private fb: FormBuilder, private translate: TranslateService, private messageService: MessageService) {
    this.form = this.fb.group({
      email: [this.nzModalData?.email || ''],
      newPassword: ['', [Validators.required, this.passwordValidator]],
      confirmPassword: ['', [Validators.required, this.passwordValidator]]

    })
  }

  updatePassword() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      this.user.updatePassword(data).subscribe(res => {
        if (res.message == "Password Updated successfully") {
          this.translate.get('TOAST.PASSWORD_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy();
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  destroyModal(): void {
    this.modal.destroy();
  }
  toggleVisibilityNewPassword() {
    if (this.visibleNewPassword) {
      this.inputTypeNewPassword = 'password';
      this.visibleNewPassword = false;
    } else {
      this.inputTypeNewPassword = 'text';
      this.visibleNewPassword = true;
    }
  }
  toggleVisibility() {
    if (this.visible) {
      this.inputType = 'password';
      this.visible = false;
    } else {
      this.inputType = 'text';
      this.visible = true;
    }
  }
  passwordValidator(control: FormControl): { [key: string]: boolean } | null {
    const value = control.value;
    if (!value) {
      return { required: true };
    }
    const hasMinLength = value.length >= 6;
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const errors: any = {};
    if (!hasMinLength) {
      errors.minLength = true;
    }
    if (!hasUpperCase) {
      errors.upperCase = true;
    }
    if (!hasLowerCase) {
      errors.lowerCase = true;
    }
    return Object.keys(errors).length ? errors : null;
  }
}
