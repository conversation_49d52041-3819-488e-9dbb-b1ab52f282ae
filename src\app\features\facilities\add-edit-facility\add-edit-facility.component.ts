import { Component, inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { FacilitiesService } from '../services/facilities.service';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-add-edit-facility',
  templateUrl: './add-edit-facility.component.html',
  styleUrl: './add-edit-facility.component.scss'
})
export class AddEditFacilityComponent {
  form: FormGroup;
  data: any;
  isUpdate: boolean = false;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private fb: FormBuilder, private facilities: FacilitiesService, private translate: TranslateService, private messageService: MessageService) {

  }
  ngOnInit(): void {
    this.createForm();
  }
  addorEditFacility() {
    if (this.form.valid) {
      let data = this.createFormData();
      if (!this.isUpdate) {

        this.facilities.addFacilities(data).subscribe(res => {
          if (res.message == "Facility created") {
            this.translate.get('TOAST.FACILITY_CREATED').subscribe((message) => {
              this.messageService.add({
                severity: 'success',
                summary: this.translate.instant('TOAST.SUCCESS'),
                detail: message,
                life: 3000,
              });
            });
          } else {
            this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
              this.messageService.add({
                severity: 'error',
                summary: this.translate.instant('TOAST.ERROR'),
                detail: message,
                life: 3000,
              });
            });
          }
          this.modal.destroy(res);
        })
      } else {
        this.facilities.updateFacility(this.nzModalData.id, data).subscribe(res => {
          if (res.message == "Facility updated") {
            this.translate.get('TOAST.FACILITY_UPDATED').subscribe((message) => {
              this.messageService.add({
                severity: 'success',
                summary: this.translate.instant('TOAST.SUCCESS'),
                detail: message,
                life: 3000,
              });
            });
          } else {
            this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
              this.messageService.add({
                severity: 'error',
                summary: this.translate.instant('TOAST.ERROR'),
                detail: message,
                life: 3000,
              });
            });
          }
          this.modal.destroy(res);
        })
      }
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  createForm() {
    this.data = this.nzModalData;
    if (this.data) {
      this.isUpdate = true;
    }
    this.form = this.fb.group({
      FacilityNameAr: [this.data?.facilityNameAr || '', Validators.required],
      FacilityNameEn: [this.data?.facilityNameEn || '', Validators.required],
      AdministratorName: [this.data?.administratorName || '', Validators.required],
      Email: [this.data?.email || '', Validators.required],
      Address: [this.data?.address || '', Validators.required],
      PostalCode: [this.data?.postalCode || '', Validators.required],
      PostalBox: [this.data?.postalBox],
      Phone: [this.data?.phone || '', Validators.required],

      FAX: [this.data?.fax || '', Validators.required],
      FirstMobile: [this.data?.firstMobile || '', Validators.required],
      SecondMobile: [this.data?.secondMobile || '', Validators.required],
      VatRegister: [this.data?.vatRegister],
      VATAccountNumber: [this.data?.vatAccountNumber],
      FileName: [this.data?.FileName],
      FacilityNumber: [this.data?.facilityNumber],
      FacilityLogo: [this.data?.FacilityLogo],
      Notes: [this.data?.notes || ''],
    });
  }
  createFormData() {
    let newData = new FormData();
    Object.entries(this.form.getRawValue()).forEach(([key, value]: any) => {
      newData.append(key, value);
    });
    return newData;
  }

  destroyModal(): void {
    this.modal.destroy();
  }
}
