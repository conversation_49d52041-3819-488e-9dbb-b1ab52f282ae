import {ChangeDetectorRef, Component, OnInit, ViewChild} from '@angular/core';
import { AddEditActivityComponent } from '../add-edit-activity/add-edit-activity.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ActivityService } from '../services/activity.service';
import { TranslateService } from '@ngx-translate/core';
import { NzTreeComponent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import {MessageService} from "primeng/api";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-activity',
  templateUrl: './activity.component.html',
  styleUrl: './activity.component.scss'
})
export class ActivityComponent implements OnInit {
  activityList: any;
  showARDate: boolean = false;
  @ViewChild('nzTreeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;
  selectedActivity: any;
  child: any;
  flattenedActivities: any[] = [];
  currentDate: Date = new Date()
  currentRef: string

  constructor(private modalService: NzModalService, private translate: TranslateService, private activity: ActivityService, private messageService: MessageService, private cd: ChangeDetectorRef, private branches: BranchService
  ) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getActivities()
      if (this.lang === 'ar') {
        this.showARDate = true;
      }
      else {
        this.showARDate = false;
      }
      this.getDirection(this.lang)

    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getActivities();
    })
  }

  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string = "en";
  langDirection: 'ltr' | 'rtl' = 'ltr';
  addActivityModal() {
    if (this.selectedActivity) {
      this.child = true
    }
    this.modalService.create({
      nzContent: AddEditActivityComponent,
      nzDirection: this.langDirection,
      nzData: { child: this.child, ...this.selectedActivity },
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedActivity = null;
        this.getActivities();
      }
    })
  }
  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';
    }
  }

  getActivities() {
    this.activity.getActivities().subscribe(res => {
      this.activityList = res.data;
      this.activityList.forEach((element: any) => {
        if (element.activityOpeningDate) {
          element.arabicActivityOpeningDate = new Date(element.activityOpeningDate).toLocaleDateString('ar-EG-u-nu-latn', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })
        }
      });
      this.activityList = this.transformToNzTreeNodeOptions(this.activityList)
      this.flattenedActivities = this.flattenActivities(res.data);
    })
  }

  mycheck(event: MouseEvent, origin: any) {
    event.stopPropagation();

    // Toggle the checked state of the clicked row
    origin.checked = !origin.checked;

    // Update the selected activity only if the row is checked
    this.selectedActivity = origin.checked ? origin : null;

    // Trigger manual change detection to update the UI
    this.cd.detectChanges();
  }

  uncheckAllNodes() {
    // Uncheck all nodes
    this.nzTreeComponent.getTreeNodes().forEach(node => this.uncheckNodeRecursively(node));

    // Reset the selectedActivity when no nodes are selected
    this.selectedActivity = null;
    this.cd.detectChanges();
  }

  uncheckNodeRecursively(node: any) {
    // Uncheck the node and its children
    node.origin.checked = false;
    if (node.children) {
      node.children.forEach((child: any) => this.uncheckNodeRecursively(child));
    }
  }


  updateActivityModal() {
    this.modalService.create({
      nzContent: AddEditActivityComponent,
      nzDirection: this.langDirection,
      nzData: this.selectedActivity,
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedActivity = null;
        this.getActivities();
      }
    })

  }
  deleteActivity() {

    this.activity.deleteActivity(this.selectedActivity.id).subscribe(res => {
      if (res.message == "Activity deleted") {
        this.translate.get('TOAST.ACTIVITY_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.selectedActivity = null;
      this.getActivities();
    })
  }
  transformToNzTreeNodeOptions(data: any): NzTreeNodeOptions[] {
    if (!data || !Array.isArray(data)) return [];

    return data.map((node: any) => ({
      key: node.id,
      title: node.activityName,
      id: node.id,
      activityNumber: node.activityNumber,
      email: node.email,
      checked: false,
      disabled: false,
      notes: node.notes,
      originalData: { ...node },
      children: this.transformToNzTreeNodeOptions(node.childActivitys || []) // Recursively map children
    }));
  }

  flattenActivities(activities: any[]): any[] {
    let result: any[] = [];
    activities.forEach((activity) => {
      result.push(activity);
      if (activity.childActivitys && activity.childActivitys.length > 0) {
        result = result.concat(this.flattenActivities(activity.childActivitys));
      }
    });
    return result;
  }

}
