import { Component } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {BehaviorSubject} from "rxjs";
import {BondsService} from "../../services/bonds.service";
import {CostService} from "../../../cost/services/cost.service";
import {ProjectsService} from "../../../projects/services/projects.service";
import {CurrencyService} from "../../../currencies/services/currency.service";
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import moment from "moment-hijri";
import {filterDetailedCostCenters, filterDetailedProjects} from "../../../../shared/utils/filters";
import {Router} from "@angular/router";

@Component({
  selector: 'app-add-journal-entires',
  templateUrl: './add-journal-entires.component.html',
  styleUrl: './add-journal-entires.component.scss'
})
export class AddJournalEntiresComponent {
  type: string = '1F1E5B71-5431-4BA5-A25F-50D7C93E1ED4'
  form: FormGroup;
  accountsList: any;
  projectList: any;
  journalEntries: any = [];
  pageSize: number = 5;
  showWarning: boolean = false;
  tempArr: any = [];
  converssionRateValue: any;
  diffBetweenCrDe: any;
  currencyList: any;
  costList: any;


  journalEntries$ = new BehaviorSubject<any>([]);

  lang: string = 'en'
  constructor(private fb: FormBuilder, private bonds: BondsService,private cost:CostService, private projects: ProjectsService,private currency:CurrencyService, private  translate: TranslateService, private messageService: MessageService, private router: Router) {
    this.createForm();
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
    })
  }
  ngOnInit(): void {

    this.getAccounts();
    this.getProjects();
    this.getCurrenciesList();
    this.getCostCenter();
  }

  dateHandler() {
    if (this.form?.get('movementDate')?.value) {
      let date = moment(this.form?.get('movementDate')?.value).format('iD iMMMM iYYYY')
      this.form.patchValue({
        movementHijriDate: date
      });
    }
  }


  createForm() {
    this.form = this.fb.group({
      amountDebtor: ['', Validators.minLength(0)],
      amountCreditor: ['', Validators.minLength(0)],
      accountNumber: [''],
      activityNumber: [null],
      projectNumber: [''],
      descriptionAr: [''],
      descriptionEn: [''],
      currencyId: [''],
      conversionFactor: [''],
      centerNumber: [''],
      movementDate: [''],
      movementHijriDate: ['']
    });
  }
  getAccounts() {
    this.bonds.getAccounts().subscribe(res => {
      this.accountsList = res.data;
    })
  }

  // controlling Account
  get accountNameControl(): FormControl {
    return this.form?.get('accountNumber') as FormControl;
  }

  onSelectedAccount(selected: any): void {
    this.form.patchValue({
      accountNumber: selected.accountNumber,
    });
  }

  updateConversionRate() {
    if (this.form?.get('currencyId')?.value) {

      this.currencyList.forEach((element: any) => {

        if (this.form?.get('currencyId')?.value == element.id)
          this.converssionRateValue = element.conversionRate
      });
      this.form.patchValue({
        conversionFactor: this.converssionRateValue
      });
    }
  }
  getCostCenter() {
    this.cost.getCost().subscribe(res => {
      this.costList = filterDetailedCostCenters(res.data);
    })
  }

  // selecting cost center
  onCostCenterSelected(selected: any): void {
    this.form.patchValue({
      centerNumber: selected.costCenterNumber.toString(),
    });
  }

  // controlling cost center
  get costCenterControl(): FormControl {
    return this.form?.get('centerNumber') as FormControl;
  }

  getCurrenciesList() {
    this.currency.getCurrencies().subscribe(res => {
      this.currencyList = res.data.filter((item: any) => item.isActive == true);
    })
  }

  // selecting currency
  onCurrencySelected(selected: any): void {
    this.form.patchValue({
      currencyId: selected.id,
    });
    this.updateConversionRate();
  }

  // controlling currency
  get currencyIdControl(): FormControl {
    return this.form?.get('currencyId') as FormControl;
  }

  disableDebt() {
    if (this.form.controls['amountCreditor'].value > 0) {
      this.form.controls['amountDebtor'].disable();
      this.form.controls['amountDebtor'].patchValue(0)
    } else {
      this.form.controls['amountDebtor'].enable();
    }
  }
  disableCredit() {

    if (this.form.controls['amountDebtor'].value > 0) {
      this.form.controls['amountCreditor'].patchValue(0)
      this.form.controls['amountCreditor'].disable();
    } else {
      this.form.controls['amountCreditor'].enable();
    }
  }


  getProjects() {
    this.projects.getProjects().subscribe(res => {
      this.projectList = filterDetailedProjects(res.data);
    })
  }

  // selecting project
  onProjectSelected(selected: any): void {
    console.log(selected)
    this.form.patchValue({
      projectNumber: selected.projectNumber.toString(),
    });
  }

  // controlling project
  get projectControl(): FormControl {
    return this.form?.get('projectNumber') as FormControl;
  }

  addRow() {
    this.tempArr.push(this.form.getRawValue());

    let newSequence = 0;
    this.tempArr.forEach((e: any) => {
      newSequence = newSequence + 1;
      e.sequence = newSequence;
    });

    const currentEntries = this.journalEntries$.getValue();
    const updatedEntries = [currentEntries, ...this.tempArr];
    this.journalEntries$.next(updatedEntries);
  }

  resetTable() {
    this.journalEntries$.next([]);
  }

  addReport() {
    let data = this.tempArr;
    this.bonds.addReport(data, '1f1e5b71-5431-4ba5-a25f-50d7c93e1ed4').subscribe(res => {
      if (res.message == "Reports created successfully") {
        this.createForm()
        this.resetTable()
        this.tempArr = []
        this.router.navigate(['/journal-entries'])
        this.translate.get('TOAST.REPORT_CREATED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        })
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        })
      }
    })
  }
}
