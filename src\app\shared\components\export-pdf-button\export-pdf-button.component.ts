import {
  AfterViewInit,
  Component,
  ElementRef,
  Input
} from '@angular/core';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import '../../../../assets/fonts/Cairo/Cairo-Regular-normal.js';

@Component({
  selector: 'app-export-pdf-button',
  templateUrl: './export-pdf-button.component.html',
  styleUrls: ['./export-pdf-button.component.scss']
})
export class ExportPdfButtonComponent implements AfterViewInit {
  @Input() tableElement!: ElementRef | HTMLDivElement;

  private readonly A4_WIDTH = 595.28;
  private readonly A4_HEIGHT = 841.89;
  private readonly MARGIN = 40;

  ngAfterViewInit() {
    if (!this.tableElement) {
      console.error('Table element reference is missing!');
    }
  }

  async exportToPDFHtml() {
    if (!this.tableElement) return;

    try {
      const element = this.tableElement instanceof ElementRef
        ? this.tableElement.nativeElement
        : this.tableElement;

      const clone = element.cloneNode(true) as HTMLElement;
      clone.id = 'pdfCloneTable';
      clone.style.width = 'auto';
      clone.style.maxWidth = `${this.A4_WIDTH - (this.MARGIN * 2)}pt`;
      clone.style.position = 'absolute';
      clone.style.top = '-9999px';
      clone.style.left = '-9999px';
      clone.style.direction = 'rtl'; // 👈 Force RTL direction
      clone.style.fontFamily = 'Cairo, sans-serif'; // 👈 Use Arabic font
      document.body.appendChild(clone);

      // Wait for custom fonts to load
      const cairoFont = new FontFace(
        'Cairo',
        'url(assets/fonts/Cairo/Cairo-Regular.ttf)'
      );
      await cairoFont.load();
      (document.fonts as any).add(cairoFont);
      await document.fonts.ready;

      const contentWidth = clone.offsetWidth;
      const contentHeight = clone.offsetHeight;
      const availableWidth = this.A4_WIDTH - (this.MARGIN * 2);
      const scale = Math.min(availableWidth / contentWidth, 2);

      const canvas = await html2canvas(clone, {
        scale: scale * 2,
        useCORS: true,
        logging: false,
        backgroundColor: '#fff',
        windowWidth: contentWidth,
        windowHeight: contentHeight
      });

      document.body.removeChild(clone);

      const finalWidth = this.A4_WIDTH;
      const finalHeight = Math.min(
        (canvas.height / canvas.width) * finalWidth,
        this.A4_HEIGHT - (this.MARGIN * 2)
      );

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'pt',
        format: 'a4'
      });

      // Register and use Arabic font
      pdf.setFont('Cairo-Regular');
      pdf.setFontSize(12);

      pdf.addImage(
        canvas.toDataURL('image/png'),
        'PNG',
        this.MARGIN,
        this.MARGIN,
        finalWidth - this.MARGIN * 2,
        finalHeight
      );

      const footerY = this.A4_HEIGHT - this.MARGIN / 2;
      pdf.setFontSize(10);
      pdf.setTextColor(100);

      const pdfUrl = pdf.output('bloburl');
      window.open(pdfUrl, '_blank');

    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  }
}
