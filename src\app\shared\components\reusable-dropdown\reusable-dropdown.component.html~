<form class="example-form">
  <mat-form-field appearance="fill"  class="mat-form-field">
    <input
      id="example-input"
      type="text"
      class="form-control mdc-text-field--filled"
      matInput
      [formControl]="myControl"
      [matAutocomplete]="auto"
    />
    <mat-autocomplete #auto="matAutocomplete">
      @for (option of filteredOptions | async; track option.id) {
        <mat-option [value]="option[displayKey]" (click)="onOptionSelected(option)"> {{ option[displayKey] }}</mat-option>
      }
    </mat-autocomplete>
  </mat-form-field>
</form>
