<div class="erp-card erp-shadow-end">
    <div class="row my-3 row px-3">
        <form [formGroup]="form">
            <P class="text-primary pt-3">{{'INVOICES.BASICDATA'|translate}}</P>
            <hr />
            <div class="row">

                <div class="col-md-4">
                    <div class="form-group">
                      <label>{{ 'INVOICES.PAYMENTTYPE'|translate }}</label>
                      <app-reusable-dropdown
                        [options]="paymentList"
                        [displayKey]="'name - code'"
                        [valueKey]="'id'"
                        [formControl]="paymentTypeIdControl"
                        [selectedId]="form?.get('paymentTypeId')?.value"
                        (selectedValue)="onPaymentSelected($event)">
                      </app-reusable-dropdown>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                      <label>{{ 'INVOICES.CURRENCY'|translate }}</label>
                      <app-reusable-dropdown
                        [options]="currencyList"
                        [displayKey]="lang == 'en' ? 'currencyNameEn - currencyCode' : lang == 'ar' ? 'currencyNameAr - currencyCode' : 'currencyNameAr - currencyCode'"
                        [valueKey]="'id'"
                        [formControl]="currencyIdControl"
                        [selectedId]="form?.get('currencyId')?.value"
                        (selectedValue)="onCurrencySelected($event)">
                      </app-reusable-dropdown>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'INVOICES.CONVERSIONRATE'|translate}}</label>
                        <input type="text" id="currencyRate" class="form-control" formControlName="currencyRate"
                            name="currencyRate">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'INVOICES.INVOICENUMBER'|translate}}</label>
                        <input type="text" id="code" class="form-control" formControlName="code" name="code">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.DATE'|translate}}</label>
                        <nz-date-picker formControlName="invoiceDate" class="datepicker"></nz-date-picker>
                    </div>
                    <div class="form-group">
                      <label>{{ 'INVOICES.DELEGATE'|translate }}</label>
                      <app-reusable-dropdown
                        [options]="dalegateList"
                        [displayKey]="'name'"
                        [valueKey]="'id'"
                        [formControl]="delegateIdControl"
                        [selectedId]="form?.get('delegateId')?.value"
                        (selectedValue)="onDelegateSelected($event)">
                      </app-reusable-dropdown>
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.TERMS'|translate}}</label>
                        <input type="text" id="terms" class="form-control" formControlName="terms" name="terms">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.NOTES'|translate}}</label>
                        <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label>{{'INVOICES.RECIPIENT'|translate}}</label>
                            <input type="text" id="reciever" class="form-control" formControlName="reciever"
                                name="reciever">
                        </div>

                    </div>

                </div>
                <div class="col-md-4">

                    <div class="form-group">
                      <label>{{ 'INVOICES.CLIENTNUMBER'|translate }}</label>
                      <app-reusable-dropdown
                        [options]="clientList"
                        [displayKey]="'name'"
                        [valueKey]="'id'"
                        [formControl]="clientIdControl"
                        [selectedId]="form?.get('clientId')?.value"
                        (selectedValue)="onClientSelected($event)">
                      </app-reusable-dropdown>
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.NAMEAR'|translate}}</label>
                        <input type="text" id="nameAr" class="form-control" formControlName="nameAr" name="nameAr">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.NAMEEN'|translate}}</label>
                        <input type="text" id="name" class="form-control" formControlName="name" name="name">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.RESPONSIBLE'|translate}}</label>
                        <input type="text" id="responsibleName" class="form-control" formControlName="responsibleName"
                            name="responsibleName">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.ADDRESS'|translate}}</label>
                        <input type="text" id="address" class="form-control" formControlName="address" name="address">
                    </div>

                  <div class="form-group">
                      <label>{{'INVOICES.PHONE'|translate}}</label>
                      <input type="text" id="phoneNumber" class="form-control" formControlName="phoneNumber"
                             name="phoneNumber">
                  </div>

                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'INVOICES.TOTALPRICE'|translate}}</label>
                        <input type="text" id="totalPrice" class="form-control" formControlName="totalPrice"
                            name="totalPrice">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.DISCOUNT'|translate}}</label>
                        <input type="text" id="descount" class="form-control" formControlName="descount"
                            name="descount">

                    </div>
                    <!-- <div class="form-group">
                        <label>{{'INVOICES.NET'|translate}}</label>

                        <input type="text" id="net" class="form-control" formControlName="net" name="net">
                    </div> -->
                    <div class="form-group">
                        <label>{{'INVOICES.TAXVALUE'|translate}}</label>
                        <input type="text" id="taxAmount" class="form-control" formControlName="taxAmount"
                            name="taxAmount">
                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.NETWORTH'|translate}}</label>
                        <!-- .. -->
                        <input type="text" id="finalAmount" class="form-control" formControlName="finalAmount"
                            name="finalAmount">

                    </div>
                    <div class="form-group">
                        <label>{{'INVOICES.TAX'|translate}}</label>
                        <input type="number" id="taxPer" class="form-control" formControlName="taxPer" name="taxPer">
                    </div>


                </div>

            </div>
          <button type="button" class="btn my-5 btn btn-primary" *ngIf="!showInvoiceItems" (click)="showSections('invoiceItems')">
            {{'INVOICES.ADDINVOICEITEMS'|translate}}
          </button>
          @if (showInvoiceItems === true) {
            <P class="text-primary pt-3">{{'INVOICES.ITEMS'|translate}}</P>
            <hr />
            <form [formGroup]="tempform">
              <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <label>{{ 'INVOICES.SALESACCOUNT'|translate }}</label>
                    <app-reusable-dropdown
                      [options]="salesAccountList"
                      [displayKey]="'name - accountNumber'"
                      [valueKey]="'id'"
                      [formControl]="salesAccountIdControl"
                      [selectedId]="form?.get('salesAccountId')?.value"
                      (selectedValue)="onSalesAccountSelected($event)">
                    </app-reusable-dropdown>

                  </div>
                  <div class="form-group">
                    <label>{{'INVOICES.NAMEAR'|translate}}</label>
                    <input type="text" id="tempNameAr" class="form-control" formControlName="tempNameAr"
                           name="tempNameAr">
                  </div>
                  <div class="form-group">
                    <label>{{'INVOICES.NAMEEN'|translate}}</label>
                    <input type="text" id="tempName" class="form-control" formControlName="tempName"
                           name="tempName">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label>{{'INVOICES.QUANTITY'|translate}}</label>
                        <input type="number" id="quantity" (change)="updateTempTotalPrice()"
                               class="form-control" formControlName="quantity" name="quantity">
                      </div>
                      <div class="form-group">
                        <label>{{'INVOICES.COST'|translate}}</label>
                        <input type="number" id="cost" (change)="updateTempTotalPrice()"
                               class="form-control" formControlName="cost" name="cost">
                      </div>
                      <div class="form-group">
                        <label>{{'INVOICES.TOTAL'|translate}}</label>
                        <input type="number" id="totalPrice" class="form-control"
                               formControlName="totalPrice" name="totalPrice">
                      </div>
                    </div>
                    <div class="col-md-6">

                      <div class="form-group">
                        <label>{{'INVOICES.DISCOUNT'|translate}}</label>
                        <input type="number" id="descountAmount" (change)="updateTempTotalPrice()"
                               class="form-control" formControlName="descountAmount" name="descountAmount">
                      </div>

                      <div class="form-group">
                        <label>{{'INVOICES.NET'|translate}}</label>
                        <input type="number" id="priceAfterDescount" class="form-control"
                               formControlName="priceAfterDescount" name="priceAfterDescount">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label>{{'INVOICES.TAX'|translate}}</label>
                        <input type="number" id="taxAmount" (change)="updateTempTotalPrice()"
                               class="form-control" formControlName="taxAmount" name="taxAmount">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="mt-5">
                        <label for="isPercintage"
                               class="form-label mx-3">{{'INVOICES.RATIO'|translate}}</label>
                        <input type="checkbox" (change)="updateTempTotalPrice()" class=" form-check-input"
                               formControlName="isPercintage" value="true">
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label>{{'INVOICES.TOTAL'|translate}}</label>
                        <input type="number" id="finalPrice" class="form-control"
                               formControlName="finalPrice" name="finalPrice">
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="form-group">
                        <label>{{'INVOICES.APPROXIMATION'|translate}}</label>
                        <input type="number" id="ratio" class="form-control" formControlName="ratio"
                               name="ratio">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label>{{'INVOICES.DESCRIPTIONEN'|translate}}</label>
                  <input type="text" id="latinDesc" class="form-control" formControlName="latinDesc"
                         name="latinDesc">
                </div>
                <div class="form-group">
                  <label>{{'INVOICES.DESCRIPTIONAR'|translate}}</label>
                  <input type="text" id="arabicDesc" class="form-control" formControlName="arabicDesc"
                         name="arabicDesc">
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>{{'INVOICES.WARRANTYDATE'|translate}}</label>
                      <nz-date-picker formControlName="insuranceDate" class="datepicker"></nz-date-picker>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>{{'INVOICES.ENDDATE'|translate}}</label>
                      <nz-date-picker formControlName="endDate" class="datepicker"></nz-date-picker>
                    </div>
                  </div>
                </div>
              </div>
              <div class="d-flex justify-content-end">
                <button class="btn btn-primary my-3" (click)="addRow()">{{'SHARED.ADDROW'|translate}}</button>
              </div>
            </form>
            <nz-table #basicTable [nzData]="invoiceLines" nzTableLayout="fixed">
              <thead>
              <tr>
                <th nzColumnKey="name">{{'INVOICES.SALESACCOUNT'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.QUANTITY'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.COST'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.TOTAL'|translate}}</th>

                <th nzColumnKey="name">{{'INVOICES.DISCOUNT'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.NET'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.TAX'|translate}}</th>

                <th nzColumnKey="name">%</th>

                <th nzColumnKey="name">{{'INVOICES.TOTAL'|translate}}</th>

                <th nzColumnKey="name">{{'INVOICES.APPROXIMATION'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.DESCRIPTIONEN'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.DESCRIPTIONAR'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.WARRANTYDATE'|translate}}</th>
                <th nzColumnKey="name">{{'INVOICES.ENDDATE'|translate}}</th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let data of basicTable.data">
                <td>{{ data.salesAccountId }}</td>
                <td>{{ data.quantity }}</td>
                <td>{{ data.cost }}</td>
                <td>{{ data.totalPrice }}</td>
                <td>{{ data.descountAmount }}</td>
                <td>{{ data.priceAfterDescount }}</td>
                <td>{{ data.taxAmount }}</td>
                <td>{{ data.isPercintage }}</td>
                <td>{{ data.finalPrice}}</td>
                <td>{{ data.ratio}}</td>
                <td>{{ data.latinDesc}}</td>
                <td>{{ data.arabicDesc}}</td>
                <td>{{ data.insuranceDate | date:'dd-MM-YYYY'}}</td>
                <td>{{ data.endDate | date:'dd-MM-YYYY'}}</td>
              </tr>
              </tbody>
            </nz-table>
          }

          <button type="button" class="btn my-5 btn btn-primary" *ngIf="showInvoiceItems && !showAccountData" (click)="showSections('accountData')">
            {{'INVOICES.ADDACCOUNTDATA'|translate}}
          </button>
          @if (showAccountData === true) {
            <P class="text-primary pt-3">{{'INVOICES.ACCOUNTDATA'|translate}}</P>
            <hr />
            <div class="form-group">
              <label>{{ 'INVOICES.DEBIT'|translate }}</label>
              <app-reusable-dropdown
                [options]="accountsList"
                [displayKey]="'accountName - accountNumber'"
                [valueKey]="'id'"
                [formControl]="DebitAccountIdControl"
                [selectedId]="form?.get('debitAccountId')?.value"
                (selectedValue)="onDebitAccountSelected($event)">
              </app-reusable-dropdown>

            </div>
            <div class="form-group">
              <label>{{ 'INVOICES.CREDIT'|translate }}</label>
              <app-reusable-dropdown
                [options]="accountsList"
                [displayKey]="'accountName - accountNumber'"
                [valueKey]="'id'"
                [formControl]="creditAccountIdControl"
                [selectedId]="form?.get('creditAccountId')?.value"
                (selectedValue)="onCreditAccountSelected($event)">
              </app-reusable-dropdown>
            </div>
            <div class="form-group">
              <label>{{ 'INVOICES.TAXACCOUNTNUMBER'|translate }}</label>
              <app-reusable-dropdown
                [options]="accountsList"
                [displayKey]="'accountName - accountNumber'"
                [valueKey]="'id'"
                [formControl]="taxAccountAccountIdControl"
                [selectedId]="form?.get('taxAccountAccountId')?.value"
                (selectedValue)="onTaxAccountSelected($event)">
              </app-reusable-dropdown>
            </div>
            <div class="form-group">
              <label>{{ 'INVOICES.COSTCENTER'|translate }}</label>
              <app-reusable-dropdown
                [options]="costList"
                [displayKey]="'costCenterName - costCenterNumber'"
                [valueKey]="'id'"
                [formControl]="costCenterIdControl"
                [selectedId]="form?.get('costCenterId')?.value"
                (selectedValue)="onCostCenterSelected($event)">
              </app-reusable-dropdown>
            </div>
            <div class="form-group">
              <label>{{ 'BONDS.PROJECT'|translate }}</label>
              <app-reusable-dropdown
                [options]="projectList"
                [displayKey]="'projectName - projectNumber'"
                [valueKey]="'id'"
                [formControl]="projectIdControl"
                [selectedId]="form?.get('projectId')?.value"
                (selectedValue)="onProjectSelected($event)">
              </app-reusable-dropdown>
            </div>
            <div class="form-group">
              <label>{{ 'INVOICES.ACTIVITY'|translate }}</label>
              <app-reusable-dropdown
                [options]="activityList"
                [displayKey]="'activityName - activityNumber'"
                [valueKey]="'id'"
                [formControl]="activityIdControl"
                [selectedId]="form?.get('activityId')?.value"
                (selectedValue)="onActivitySelected($event)">
              </app-reusable-dropdown>
            </div>
            <div class="d-flex justify-content-end">
              <button *ngIf="!id" class="btn btn-primary my-3" (click)="onSubmit()">
                {{'SHARED.ADD' |translate}}</button>
              <button *ngIf="id" class="btn btn-primary my-3" (click)="edit()">
                {{'SHARED.SAVE' |translate}}</button>
              <!-- <button class="btn btn-primary my-3" (click)="onSubmit()">{{'SHARED.SAVE'|translate}}</button> -->
            </div>
          }
        </form>
    </div>
</div>
