mat-form-field .example-input {
  max-width: 100%;
  width: 100% !important;
}

.example-form {
  width: 100% !important;
}

.mat-mdc-form-field {
  width: 100% !important;
}

::ng-deep .mat-mdc-form-field-infix {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}


.mdc-text-field--outlined .mat-mdc-form-field-infix,
.mdc-text-field--no-label .mat-mdc-form-field-infix {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}


.mat-mdc-input-element {
  width: 100% !important;
  box-sizing: border-box; /* Ensures padding doesn't affect the width */
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  height: 48px !important;
  border-radius: 13px;
  font-size: 14.4932px;
  padding-top: 0px !important; /* Remove top padding */
  padding-bottom: 0px !important; /* Remove bottom padding */
  border-top: none !important; /* Remove top border */
  border-bottom: none !important; /* Remove bottom border */
  border: 1px solid #dee2e6 !important;
}


.mat-mdc-form-field-infix {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

.mat-mdc-form-field-flex {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.mat-form-field:focus-within {
  background: #000 !important;
  padding: 0 !important;
  margin-bottom: 10px !important;
  border-radius: 13px;
}

/* Scoped override for a specific component */
:host {
  --mdc-filled-text-field-container-color: #fff; /* Replace with your desired color */
}

:host ::ng-deep .mat-mdc-form-field-focus-overlay {
  background-color: white !important;
}

:host ::ng-deep .mat-mdc-form-field-focus-overlay:hover {
  background-color: white !important;
}

:host ::ng-deep .mat-mdc-text-field-wrapper {
  padding: 0 !important;
}

.mat-mdc-form-field-infix {
  width: 100% !important;
}

.mat-mdc-input-element {
  width: 100% !important;
}

:host ::ng-deep .mat-mdc-form-field-focus-overlay:focus {
  background-color: white !important;
}

:host ::ng-deep .mat-mdc-form-field-subscript-wrapper {
  display: none !important;
}

:host ::ng-deep .mdc-line-ripple {
  display: none !important;
}


:root {
  --mdc-filled-text-field-container-color: #fff; /* Replace with your desired color */
}

.mat-form-field.mat-form-field-appearance-fill .mat-form-field-wrapper {
  background-color: #fff !important; /* Replace with your desired color */
}

.mat-form-field {
  padding: 0;
}

.mat-mdc-text-field-wrapper {
  background-color: #fff !important; /* Replace with your desired color */
}

.mdc-text-field--filled {
  /* Add a gray border */
  border: 1px solid #888; /* Replace with your desired gray color */

  /* Set all corners to be rounded */
  border-radius: 8px;

  /* Remove underline by ensuring padding matches */
  padding: 8px;
}

/* Optional: Adjust focus styles for better UX */
.mdc-text-field--filled:focus {
  border-color: #fff; /* Change the border color on focus */
  outline: none; /* Remove default outline */
}

/* Override the container shape variable globally */
:root {
  --mdc-filled-text-field-container-shape: 8px; /* Set a consistent shape globally */
}


.example-full-width {
  width: 100%;
}

.example-input {
  max-width: 100%;
  width: 100% !important;
}
.no-padding-input {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  height: auto !important; /* Ensure the height adjusts to content */
  width: 100% !important;
}

.no-padding-input .mat-mdc-input-element {
  padding: 0 !important; /* Remove internal padding */
  margin: 0 !important; /* Remove margin */
  height: auto !important;
  border: none !important; /* Remove border if you want */
}

