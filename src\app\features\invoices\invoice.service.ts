import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../core/models';
import {BranchService} from "../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class InvoiceService {

  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
    console.log(this.branchId)
  }
  addInvoice(body: any) {
    if (this.branchId) {
      body.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Invoice`, body);
  }
  getInvoice() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Invoice');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Invoice?branchId=${this.branchId}`);
    }
  }
  getInvoiceById(id: any) {
    return this.httpClient.get<IBaseResponse<any>>(`/Invoice/${id}`);
  }
  editInvoicet(id: any, body: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Invoice/${id}`, body);
  }
  deleteInvoice(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Invoice/${id}`);
  }
  getPaymentMethod(){
    return this.httpClient.get<IBaseResponse<any>>(`/PaymentMethods`);
  }
}
