import { Component, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { CurrencyService } from '../services/currency.service';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-add-currency',
  templateUrl: './add-currency.component.html',
  styleUrl: './add-currency.component.scss'
})
export class AddCurrencyComponent implements OnInit {
  form: FormGroup;
  data: any;
  isUpdate: boolean = false;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private fb: FormBuilder, private currency: CurrencyService, private translate: TranslateService, private messageService: MessageService) {

  }
  ngOnInit(): void {
    this.createForm();
  }
  addorEditCurrency() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      if(!this.isUpdate){

        this.currency.addCurrencies(data).subscribe(res => {
          if (res.message == "Currency created") {
            this.translate.get('TOAST.CURRENCY_CREATED').subscribe((message) => {
              this.messageService.add({
                severity: 'success',
                summary: this.translate.instant('TOAST.SUCCESS'),
                detail: message,
                life: 3000,
              });
            });
          } else {
            this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
              this.messageService.add({
                severity: 'error',
                summary: this.translate.instant('TOAST.ERROR'),
                detail: message,
                life: 3000,
              });
            });
          }
          this.modal.destroy(res);
        })
      }else{
        this.currency.updateCurrencies(data.id,data).subscribe(res => {
          this.modal.destroy(res);
        })
      }
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  createForm() {
    this.data = this.nzModalData;
    if (this.data) {
      this.isUpdate = true;
    }
    this.form = this.fb.group({
      id: [this.data?.id],
      currencyCode: [this.data?.currencyCode],
      isActive: [this.data?.isActive !== undefined ? this.data.isActive : true],
      currencyNameAr: [this.data?.currencyNameAr || '', Validators.required],
      currencyNameEn: [this.data?.currencyNameEn || '', Validators.required],
      currencyNickNameEn: [this.data?.currencyNickNameEn || '', Validators.required],
      currencyNickNameAr: [this.data?.currencyNickNameAr || '', Validators.required],
      currencyFrictionNameAr: [this.data?.currencyFrictionNameAr || '', Validators.required],
      currencyFrictionNameEn: [this.data?.currencyFrictionNameEn || '', Validators.required],
      notes: [this.data?.notes || ''],
      conversionRate: [this.data?.conversionRate || '', Validators.required],
    });
  }

  onCheckboxChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    this.form.get('isActive')?.setValue(checkbox.checked);
  }


  destroyModal(): void {
    this.modal.destroy();
  }
}
