import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { IBaseResponse, User } from '../../models';
import { HttpClient } from '@angular/common/http';
@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(private httpClient: HttpClient) { }
  private userSubject = new BehaviorSubject<any>(UserService.getUserFromLocalStorage());
  /**
   * [#1] An observable the observe the current user details
   */
  public watcher$ = this.userSubject.asObservable();
  private static getUserFromLocalStorage(): User {

    let user: User;
    try {
      const localStorageUser: any = localStorage.getItem('user');
      user = JSON.parse(localStorageUser) || {};
    } catch (error) {
      user = {};
    }
    return user;
  }
  public set(user: User) {
    this.userSubject.next(user);
    localStorage.setItem('user', JSON.stringify(user));

  }
  public get token(): string {
    return this.userSubject.getValue().token;
  }
  public get role(): string {
    return this.userSubject.getValue().role;
  }

  public delete() {

    this.userSubject.next({});
    localStorage.removeItem('user');
  }

  registerUser(data: any) {
    return this.httpClient.post<any>(`/Tenants/Register`, data)
  }
  getUserInfo() {
    return this.httpClient.get<IBaseResponse>(`/UsersProfiles`);
  }

}
