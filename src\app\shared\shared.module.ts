import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { IconsProviderModule } from '../icons-provider.module';
import { TranslateModule } from '@ngx-translate/core';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { LangDropdownComponent } from './components/lang-dropdown/lang-dropdown.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { TablefiltersPipe } from './pipes/search.pipe';
import { NzInputModule } from 'ng-zorro-antd/input';
import { ArabicNumeralsPipe } from './pipes/arabic-numerals.pipe';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { ExportPdfButtonComponent } from './components/export-pdf-button/export-pdf-button.component';
import {BreadcrumbModule} from "primeng/breadcrumb";
import { BreadcrumbComponent } from './components/breadcrumb/breadcrumb.component';
import { TreeFiltersPipe } from './pipes/tree-filters.pipe';
const Modules = [
  FormsModule,
  ReactiveFormsModule,
  IconsProviderModule,
  NzLayoutModule,
  NzMenuModule,
  NzDropDownModule,
  NzButtonModule,
  NzFormModule,
  NzSelectModule,
  NzTableModule,
  NzModalModule,
  NzInputModule,
  NzDatePickerModule,
  NzTreeModule,
  NzRadioModule
]

@NgModule({
    declarations: [
        LangDropdownComponent,
        TablefiltersPipe,
        ArabicNumeralsPipe,
        ExportPdfButtonComponent,
        BreadcrumbComponent,
        TreeFiltersPipe,
    ],
  imports: [
    CommonModule, ...Modules, TranslateModule, BreadcrumbModule

  ],
    exports: [
        ...Modules,
        TranslateModule,
        LangDropdownComponent,
        TablefiltersPipe,
        ArabicNumeralsPipe,
        ExportPdfButtonComponent,
        BreadcrumbComponent,
        TablefiltersPipe,
        TreeFiltersPipe
    ]
})
export class SharedModule { }
