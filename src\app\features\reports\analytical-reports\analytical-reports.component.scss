/* Large screen styles */
::ng-deep .custom-multiselect .p-multiselect-label {
  font-size: 16px;
  padding: 0;
}

::ng-deep .custom-multiselect .p-multiselect-item {
  font-size: 16px;
  padding: 0.5rem 1rem;
}

/* Make dropdown panel larger if needed */
::ng-deep .custom-multiselect .p-multiselect-panel {
  min-width: 250px;
}

/* Filter container with flex to align icon and input */
::ng-deep .custom-multiselect .p-multiselect-panel .p-multiselect-filter-container {
  display: flex;
  align-items: center; /* Vertically center both input and icon */
  padding: 0.5rem 1rem;
  gap: 0.5rem;
}

/* Input itself */
::ng-deep .custom-multiselect .p-multiselect-panel .p-multiselect-filter {
  flex: 1;
  height: 2.5rem;
  font-size: 16px;
  padding: 0.5rem 0.75rem;
  line-height: 1.5;
}

/* Search icon container */
::ng-deep .custom-multiselect .p-multiselect-panel .p-multiselect-filter-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 1.2rem;
  margin-top: 0 !important;
  padding-left: 0.5rem;
  padding-right: 0.25rem;

  svg {
    display: block;
    height: 1.1rem;
    width: 1.1rem;
  }
}

/* Small screen styles */
@media screen and (max-width: 600px) {
  ::ng-deep .custom-multiselect .p-multiselect-label {
    font-size: 12px;
  }

  ::ng-deep .custom-multiselect .p-multiselect-item {
    font-size: 12px;
  }

  ::ng-deep .custom-multiselect .p-multiselect-panel .p-multiselect-filter {
    font-size: 14px;
    height: 2.2rem;
  }

  ::ng-deep .custom-multiselect .p-multiselect-panel .p-multiselect-filter-icon {
    font-size: 1rem;

    svg {
      height: 1rem;
      width: 1rem;
    }
  }
}
