import { Component, OnInit } from '@angular/core';
import { AddEditSalesAccountComponent } from './add-edit-sales-account/add-edit-sales-account.component';
import { SalesAccountService } from './sales-account.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import {MessageService} from "primeng/api";
import {BranchService} from "../branches/branch.service";

@Component({
  selector: 'app-sales-account',
  templateUrl: './sales-account.component.html',
  styleUrl: './sales-account.component.scss'
})
export class SalesAccountComponent implements OnInit {
  salesAccountList: any;


  searchValue: string = '';
  pageSize: number = 5;
  lang: string;
  langDirection: 'ltr' | 'rtl' = 'ltr';
  flattenedSalesAccount: any[] = [];
  currentDate: Date = new Date()
  currentRef: string

  constructor(private salesAccount:SalesAccountService, private modalService: NzModalService, private translate: TranslateService, private messageService: MessageService, private branches: BranchService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'

    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getsalesaccount()
      this.getDirection(this.lang)

    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getsalesaccount();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }
  getsalesaccount() {

    this.salesAccount.getsalesaccount().subscribe(res => {
      this.salesAccountList = res.data;
      this.flattenedSalesAccount = this.flattenSalesAccount(res.data)
    })
  }


  delete(id: any) {
    this.salesAccount.deletesalesaccount(id).subscribe(res => {
      if (res.message == "Sales Account Deleted!") {
        this.translate.get('TOAST.SALES_ACCOUNT_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getsalesaccount();
    })
  }
  EditsalesAccount(data: any) {
    this.modalService.create({
      nzContent: AddEditSalesAccountComponent,
      nzDirection: this.langDirection,
      nzData: data
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getsalesaccount();
      }
    })
  }

  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  sortFnsalesAccountCode = (a: any, b: any) => {
    return a.salesAccountCode- b.salesAccountCode
  }

  sortFnCrEN = (a: any, b: any) => {
    return a.salesAccountNameEn.localeCompare(b.salesAccountNameEn)
  }



  sortFnCrAR = (a: any, b: any) => {
    return a.salesAccountNameAr.localeCompare(b.salesAccountNameAr)
  }
  sortFncrnicknameen = (a: any, b: any) => {
    return a.salesAccountNickNameEn.localeCompare(b.salesAccountNickNameEn)
  }
  sortFncrnicknamear = (a: any, b: any) => {
    return a.salesAccountNickNameAr.localeCompare(b.salesAccountNickNameAr)
  }
  sortFnConversionRate = (a: any, b: any) => {
    return a.conversionRate - b.conversionRate
  }
  sortFnNActive = (a: any, b: any) => {
    return a.isActive - b.isActive
  }
  sortFnNotes = (a: any, b: any) => {
    return a.notes.localeCompare(b.notes)
  }

  addNewsalesAccount(): void {
    this.modalService.create({
      nzContent: AddEditSalesAccountComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res?.message === "Sales Account created") {
        this.translate.get('TOAST.SALES_ACCOUNT_CREATED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else if (res?.message) {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getsalesaccount();
    });
  }

  flattenSalesAccount(salesAccounts: any[]): any[] {
    let result: any[] = [];
    salesAccounts.forEach((salesAccount) => {
      result.push(salesAccount);
    });
    return result;
  }

}
