<div *nzModalTitle> {{'USERS.TABLE.ADDUSER'|translate}}</div>
<form [formGroup]="regsiterForm">
    <div class="container">
        <div class="form-group">
            <label>{{'ADDUSERFORM.USERNAME'|translate}}</label>
            <input type="text" id="userName" class="form-control" formControlName="userName" name="username">
            <ng-container *ngIf="regsiterForm && regsiterForm?.get('userName')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="regsiterForm && regsiterForm?.get('userName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ADDUSERFORM.EMAIL'|translate}}</label>
            <input type="text" id="email" class="form-control" formControlName="email" name="email">
            <ng-container *ngIf="regsiterForm && regsiterForm?.get('email')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="regsiterForm && regsiterForm?.get('email')?.hasError('required')">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
                <p class="text-danger text-error" *ngIf="regsiterForm && regsiterForm?.get('email')?.hasError('email')">
                    {{'ADDUSERFORM.INVALIDEMAIL'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ADDUSERFORM.PHONENUMBER'|translate}}</label>
            <input type="text" id="phoneNumber" class="form-control" formControlName="phoneNumber" name="phoneNumber">
            <div *ngIf="regsiterForm.get('phoneNumber')?.invalid && (regsiterForm.get('phoneNumber')?.dirty || regsiterForm.get('phoneNumber')?.touched)">
              <small class="text-danger" *ngIf="regsiterForm.get('phoneNumber')?.errors?.['pattern']">
                Phone number must contain digits only.
              </small>
              <small class="text-danger" *ngIf="regsiterForm.get('phoneNumber')?.errors?.['required']">
                Phone number is required.
              </small>
            </div>
        </div>

        <div class="form-group">
          <label>{{ 'SIDEBAR.BRANCHES'|translate }}</label>
          <app-reusable-dropdown
            [options]="branchList"
            [displayKey]="'branch_Name'"
            [valueKey]="'branchId'"
            [formControl]="branchControl"
            [selectedId]="regsiterForm?.get('branchId')?.value"
            (selectedValue)="onSelectedBranch($event)">
          </app-reusable-dropdown>
          <div *ngIf="regsiterForm.get('branchId')?.invalid && (regsiterForm.get('branchId')?.dirty || regsiterForm.get('branchId')?.touched)">
            <small class="text-danger" *ngIf="regsiterForm.get('branchId')?.errors?.['required']">
              Phone number is required.
            </small>
          </div>
        </div>
        <div class="form-group">
            <label>{{'ADDUSERFORM.PASSWORD'|translate}}</label>
            <div class="input-group">
                <input [type]="inputType" id="password" class="form-control" formControlName="password" name="password">
                <span class="input-group-text" id="basic-addon2" (click)="toggleVisibility()">
                    <span nz-icon *ngIf="visible" nzType="eye" nzTheme="outline"></span>
                    <span nz-icon *ngIf="!visible" nzType="eye-invisible" nzTheme="outline"></span>
                </span>
            </div>
            <ng-container *ngIf="regsiterForm && regsiterForm?.get('password')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="regsiterForm && regsiterForm?.get('password')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>



    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="onSubmit()">{{'USERS.TABLE.ADDUSER'|translate}}</button>
    </div>

</form>
