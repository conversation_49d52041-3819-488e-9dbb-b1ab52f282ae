import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StakeholdersComponent } from './stakeholders.component';
import { AddEditStakeholdersComponent } from './add-edit-stakeholders/add-edit-stakeholders.component';
import { DelegatesComponent } from './delegates/delegates.component';
import { ClientsComponent } from './clients/clients.component';
import { SuppliersComponent } from './suppliers/suppliers.component';
import { SharedModule } from '../../shared/shared.module';
import { StakeholdersRoutingModule } from './stakeholders-routing';
import { AddEditDelegateComponent } from './delegates/add-edit-delegate/add-edit-delegate.component';
import { AddEditSupplierComponent } from './suppliers/add-edit-supplier/add-edit-supplier.component';
import { AddEditClientComponent } from './clients/add-edit-client/add-edit-client.component';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    StakeholdersComponent,
    AddEditStakeholdersComponent,
    DelegatesComponent,
    ClientsComponent,
    SuppliersComponent,
    AddEditDelegateComponent,
    AddEditSupplierComponent,
    AddEditClientComponent
  ],
    imports: [
        CommonModule,
        SharedModule,
        StakeholdersRoutingModule,
        ReusableDropdownComponent
    ]
})
export class StakeholdersModule { }
