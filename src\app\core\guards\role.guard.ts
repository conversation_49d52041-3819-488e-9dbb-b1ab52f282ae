import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { RoleService } from '../services/role/role.service';


@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(private roleService: RoleService, private router: Router) {}

  canActivate(route: any): boolean {
    const expectedRole = route.data.expectedRole;
    if (this.roleService.hasRole(expectedRole)) {
      
      return true;
    } else {
      
      this.router.navigate(['/login']);
      return false;
    }
  }
}
