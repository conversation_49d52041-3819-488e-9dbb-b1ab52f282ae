<div class="dropdown">
    <button class="btn btn-secondary dropdown-p" type="button" data-bs-toggle="dropdown" aria-expanded="false">
        <span *ngIf="lang == 'ar'"><img src="assets/images/sa.svg" width="20" height="15"> AR</span>
        <span *ngIf="lang == 'en'"> <img src="assets/images/en.svg" width="20" height="15"> EN</span>
    </button>
    <ul class="dropdown-menu dropdown-center">
        <li *ngIf="lang == 'en'" (click)="switchLang('ar')"> <a >
            <img src="assets/images/sa.svg" width="20" height="15"> AR </a>
        </li>
        <li *ngIf="lang == 'ar'" (click)="switchLang('en')"><a >
                <img src="assets/images/en.svg" width="20" height="15"> EN </a></li>
        
    </ul>
</div>