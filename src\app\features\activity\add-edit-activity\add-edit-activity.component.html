<div *nzModalTitle><span *ngIf="!isUpdate">{{'ACTIVITY.ADDACTIVITY'|translate}}</span> <span
    *ngIf="isUpdate">{{'ACTIVITY.UPDATEACTIVITY'|translate}}</span></div>
<form [formGroup]="form">
<div class="container">
    <div class="row">
        <!-- <div class="col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYNUMBER'|translate}}</label>
                <input type="text" id="accountNumber" class="form-control" formControlName="accountNumber"
                    name="accountNumber">
            </div>
        </div> -->
        <!-- <div class="col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.PARENTNUMBER'|translate}}</label>
                <input type="text" id="parentAccountNum" class="form-control" formControlName="parentAccountNum"
                    name="parentAccountNum">
            </div>
        </div> -->
    </div>
    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.ABBREVIATIONNAME'|translate}}</label>
        <input type="text" id="shortName" class="form-control" formControlName="shortName" name="shortName">
        <ng-container *ngIf="form && form?.get('shortName')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('shortName')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div>
    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYNAME'|translate}}</label>
        <input type="text" id="shortName" class="form-control" formControlName="activityName" name="activityName">
        <ng-container *ngIf="form && form?.get('activityName')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('activityName')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div>
    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYNAMEEN'|translate}}</label>
        <input type="text" id="shortName" class="form-control" formControlName="account_Name_Latin"
            name="account_Name_Latin">
        <ng-container *ngIf="form && form?.get('account_Name_Latin')?.dirty">
            <p class="text-danger text-error"
                *ngIf="form && form?.get('account_Name_Latin')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYOPENINGDATE'|translate}}</label>
                <br />
                <nz-date-picker (ngModelChange)="dateHandler()" formControlName="activityOpeningDate"
                    class="datepicker"></nz-date-picker>
                <ng-container *ngIf="form && form?.get('activityOpeningDate')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('activityOpeningDate')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYOPENINGDATE'|translate}}
                    ({{'SHARED.HIJRI'|translate}})</label>
                <input [value]="form?.get('activityOpeningDatehijri')?.value |arabicNumerals" [attr.disabled]="true"
                    class="form-control" type="text" />
            </div>
        </div>
    </div>
    <!-- <div class="row">
        <div class="col-12 col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.CURRENCY'|translate}}</label>
                <select formControlName="currencyId" (change)="updateConversionRate()" class="form-select"
                    aria-label="Default select example">
                    <option *ngFor="let item of currencyList" [value]="item.id">{{ item.currencyNameEn }}</option>
                </select>
                <ng-container *ngIf="form && form?.get('englishName')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('englishName')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="col-12 col-md-6">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.CURRENCYRATE'|translate}}</label>
                <input type="text" id="conversionRate" class="form-control" formControlName="conversionRate"
                    name="conversionRate">
            </div>
        </div>

    </div> -->
    <div class="row">

        <div class="col-md-12">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.CREDITLIMITS'|translate}}</label>
                <input type="number" id="creditLimit" class="form-control" formControlName="creditLimit"
                    name="creditLimit">
                <ng-container *ngIf="form && form?.get('creditLimit')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('creditLimit')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>

    </div>

    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.ESTIMATEDBUDGET'|translate}}</label>
        <input type="number" id="estimatedBudget" class="form-control" formControlName="estimatedBudget"
            name="estimatedBudget">
        <ng-container *ngIf="form && form?.get('estimatedBudget')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('estimatedBudget')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div>
    <!-- <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.OPENINGBALANCEBYINFOREIGNCURRENCY'|translate}}</label>
        <input type="number" id="openingBalanceByInForeignCurrency" class="form-control"
            formControlName="openingBalanceByInForeignCurrency" name="openingBalanceByInForeignCurrency">
        <ng-container *ngIf="form && form?.get('openingBalanceByInForeignCurrency')?.dirty">
            <p class="text-danger text-error"
                *ngIf="form && form?.get('openingBalanceByInForeignCurrency')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div> -->

    <div class="row">
        <div class="col-12 col-md-4">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.CURRENTBALANCE'|translate}}</label>
                <input type="number" id="openingBalance" class="form-control" formControlName="openingBalance"
                    name="openingBalance">
                <ng-container *ngIf="form && form?.get('openingBalance')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('openingBalance')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="col-12 col-md-4">
            <div class="form-group">
                <label>{{'ACTIVITY.ADDACTIVITYFORM.CURRENTBALANCEDATE'|translate}}</label>
                <br />
                <nz-date-picker formControlName="openingBalanceDate" class="datepicker"></nz-date-picker>
                <ng-container *ngIf="form && form?.get('openingBalanceDate')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('openingBalanceDate')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>


        <div class="col-12 col-md-4 radio-style">
            <div class="d-flex">
                <div class="radio-style-padding"><input formControlName="status" class=" form-check-input"
                        type="radio" [value]="'0'">
                    <label class=" form-check-label ">{{'ACTIVITY.ADDACTIVITYFORM.CREDITOR'|translate}}</label>
                </div>
                <div>
                    <input formControlName="status" class=" form-check-input" type="radio" [value]="1">
                    <label class=" form-check-label">{{'ACTIVITY.ADDACTIVITYFORM.DEBTOR'|translate}}</label>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <!-- <div class="col-12 col-md-3 text-center pr-0">
            <div class="mb-3">
                <label for="isPrivate" class="form-label">{{'ACTIVITY.ADDACTIVITYFORM.ISPRIVATE'|translate}}</label>
                <input type="checkbox" class=" form-check-input" formControlName="isPrivate" value="true">

            </div>
        </div>

        <div class="col-12 col-md-3 text-center p-0">
            <div class="mb-3">
                <label for="isCostCenter"
                    class="form-label">{{'ACTIVITY.ADDACTIVITYFORM.ISCOSTCENTER'|translate}}</label>
                <input type="checkbox" class=" form-check-input" formControlName="isCostCenter" value="true">

            </div>
        </div> -->

        <div class="col-12 col-md-3 text-center  p-0">
            <div class="mb-3">
                <label for="isDetaileds"
                    class="form-label">{{'ACTIVITY.ADDACTIVITYFORM.ISCONGREGATION'|translate}}</label>
                <input type="radio" class=" form-check-input" id="isDetaileds" formControlName="isDetailed"
                    [value]="false" [attr.disabled]="form?.get('level')?.value < 4? '' : null">
            </div>
        </div>

        <div class="col-12 col-md-3 text-center  p-0">
            <div class="mb-3">
                <label for="isDetailed" class="form-label">{{'ACTIVITY.ADDACTIVITYFORM.ISDETAILED'|translate}}</label>
                <input type="radio" class=" form-check-input" id="isDetailed" formControlName="isDetailed"
                    [value]="true" [attr.disabled]="form?.get('level')?.value < 4? '' : null"
                    (change)="onRadioChange(true)">
            </div>
        </div>
    </div>
    <!-- <div *ngIf="(form.errors?.['atLeastOneCheckboxRequired'] && form.get('isPrivate')?.dirty )|| (form.errors?.['atLeastOneCheckboxRequired'] && form.get('isCostCenter')?.dirty)"
        class="alert alert-danger d-flex align-items-center" role="alert">

        <span>{{'ACTIVITY.ADDACTIVITYFORM.ISPRIVATEISCOSTCENTERREQUIRED'|translate}}</span>
    </div> -->

    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.COMMENTS'|translate}}</label>
        <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
        <ng-container *ngIf="form && form?.get('notes')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('notes')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div>
    <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.EMAIL'|translate}}</label>
        <input type="text" id="email" class="form-control" formControlName="email" name="email">
        <ng-container *ngIf="form && form?.get('email')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
            <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('email')  ">
                {{'SHARED.INVALIDEMAIL'|translate}}
            </p>
        </ng-container>
    </div>
    <!-- <div class="form-group">
        <label>{{'ACTIVITY.ADDACTIVITYFORM.VATREGISTER'|translate}}</label>
        <input type="text" id="vatRegister" class="form-control" formControlName="vatRegister" name="vatRegister">
        <ng-container *ngIf="form && form?.get('vatRegister')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('vatRegister')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
    </div> -->




</div>
<div *nzModalFooter>
    <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
    <button class="btn btn-primary" type="submit" *ngIf="!isUpdate"
        (click)="onSubmit()">{{'ACTIVITY.ADDACTIVITY'|translate}}</button>
    <button class="btn btn-primary" type="submit" *ngIf="isUpdate"
        (click)="update()">{{'ACTIVITY.UPDATEACTIVITY'|translate}}</button>
</div>
</form>