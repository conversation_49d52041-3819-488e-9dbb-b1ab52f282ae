import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BranchesComponent } from './branches.component';
import { AddEditBranchComponent } from './add-edit-branch/add-edit-branch.component';
import { SharedModule } from '../../shared/shared.module';
import { BranchesRoutingModule } from './branches-routing.module';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    BranchesComponent,
    AddEditBranchComponent
  ],
    imports: [
        CommonModule,
        SharedModule,
        BranchesRoutingModule,
        ReusableDropdownComponent
    ]
})
export class BranchesModule { }
