import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class AccountService {
  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }


  getCurrency() {
    return this.httpClient.get<IBaseResponse<any>>(`/Currencies`);
  }

  addAccount(data: any) {
    if (this.branchId) {
      data.branchId = this.branchId;
    }

    return this.httpClient.post<IBaseResponse<any>>(`/Account`, data);
  }
  getAccounts() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Account');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Account?branchId=${this.branchId}`);
    }
  }
  updateAccount(id: any, data: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Account/${id}`, data);
  }
  deleteAccount(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Account/${id}`);
  }


}
