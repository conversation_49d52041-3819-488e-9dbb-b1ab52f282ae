import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../core/models';

@Injectable({
  providedIn: 'root'
})
export class IdentityService {

  constructor(private httpClient:HttpClient) { }
  addIdentity(data: any) {
    return this.httpClient.post<IBaseResponse<any>>(`/Identity`, data);
  }
  getIdentity() {
    return this.httpClient.get<IBaseResponse<any>>(`/Identity`);
  }

}
