import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { StakeholdersService } from '../stakeholders.service';

@Component({
  selector: 'app-add-edit-stakeholders',
  templateUrl: './add-edit-stakeholders.component.html',
  styleUrl: './add-edit-stakeholders.component.scss'
})
export class AddEditStakeholdersComponent implements OnInit {
  form: FormGroup;
  towns: any;
  countries: any;
  cities: any;
  regions: any;
  constructor(private fb: FormBuilder, private stakeholders: StakeholdersService) {

    this.createForm();
  }
  ngOnInit(): void {
    this.getRegions(); 
    this.getCities();
    this.getTowns();
    this.getCountries();
  }

  createForm() {
    this.form = this.fb.group({
      "serial": [''],
      "openDate": [''],
      "openDateHijri": [''],
      "identityNumber": [''],
      "identityTypeId": [''],
      "name": [''],
      "nameAr": [''],
      "responsibleName": [''],
      "fullAddress": [''],
      "isStopped": [''],
      "countryId": [''],
      "cityId": [''],
      "regionId": [''],
      "townId": [''],
      "paymentMethodId": [''],
      "branchId": [''],
      "additionalCode": [''],
      "companyName": [''],
      "bankName": [''],
      "taxAccountNumber": [''],
      "streetName": [''],
      "buildingId": [''],
      "zipCode": [''],
      "postalCode": [''],
      "firstPhone": [''],
      "secondPhone": [''],
      "firstMobile": [''],
      "secondMobile": [''],
      "firstFax": [''],
      "secondFax": [''],
      "email": [''],
      "notes": [''],
      "boxNumber": [''],
      "facilityNumber":  [''],
      "accountNumber": [''],
      "buildingCode": [''],
      "commercialCode": [''],
      "iban": [''],
      "contractNo": [''],
      "vatRegNO": [''],
      "id": ['']
    })
  }
  getTowns() {
    this.stakeholders.getTowns().subscribe(res => {
      this.towns = res.data;
    })
  }
  getCountries() {
    this.stakeholders.getCountries().subscribe(res => {
      this.countries = res.data;
    })

  }
  getCities() {
    this.stakeholders.getCities().subscribe(res => {
      this.cities = res.data;
    })

  }

  getRegions() {
    this.stakeholders.getRegions().subscribe(res => {
      this.regions = res.data;
    })

  }
  onSubmit() {

  }
}
