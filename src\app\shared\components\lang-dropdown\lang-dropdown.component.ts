import { ChangeDetectorRef, Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';


@Component({
  selector: 'app-lang-dropdown',
  templateUrl: './lang-dropdown.component.html',
  styleUrl: './lang-dropdown.component.scss'
})
export class LangDropdownComponent {
  lang = localStorage.getItem('lang') || 'en';
  constructor(private translate: TranslateService ,private cd:ChangeDetectorRef) {
    this.translate.onLangChange.subscribe((langObject)=>{
      this.lang = langObject.lang;
      this.cd.markForCheck()
    })

  }

  switchLang(lang: string) {
    this.translate.use(lang);
  }





}
