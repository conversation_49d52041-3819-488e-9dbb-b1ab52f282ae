import { Inject, Injectable } from '@angular/core';
import { DOCUMENT } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';
import { Language } from '../../enums/language.enum';



@Injectable({
  providedIn: 'root'
})
export class TranslateCustomService  {
  constructor(
    @Inject(DOCUMENT) private document: Document,
    private translateService: TranslateService
  ) { }

  setLanguage(language: string): void {
    this.translateService.use(language);
    const doc = this.document.documentElement;
    doc.lang = language;
    doc.dir = language === Language.arabic ? 'rtl' : 'ltr';
  }

  get language(): any {
    return this.translateService.getBrowserLang();
  }
}