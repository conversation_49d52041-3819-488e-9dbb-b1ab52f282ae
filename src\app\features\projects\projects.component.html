<div class="erp-card erp-shadow-end">
  <div class="row my-3 px-3 align-items-center">

    <!-- Search Input -->
    <div class="col-12 col-md-3 mb-2 mb-md-0">
      <input type="text" id="search" class="form-control"
             placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
             name="search" [(ngModel)]="searchValue">
    </div>

    <!-- Buttons Section -->
    <div class="col-12 col-md-9 d-flex flex-wrap justify-content-md-end gap-2 text-center">

      <!-- Delete Button -->
      <button *ngIf="selectedProject" (click)="deleteProject()"
              class="btn btn-danger text-white">
        {{ 'PROJECTS.DELETE' | translate }}
      </button>

      <!-- Update Button -->
      <button *ngIf="selectedProject" (click)="updateProjectModal()"
              class="btn btn-primary">
        {{ 'PROJECTS.UPDATE' | translate }}
      </button>

      <!-- Add Project Button -->
      <button *ngIf="shouldShowAddButton()" (click)="addAccountModal()"
              class="btn btn-primary">
        + {{ 'PROJECTS.ADDPROJECT' | translate }}
      </button>

    </div>
  </div>


  <nz-tree #nzTreeComponent [nzData]="projectsList | treeFilters: {projectNumber: searchValue, projectName:searchValue}"
           [nzExpandAll]="true" [nzCheckStrictly]="true"
           [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
  <ng-template #nzTreeTemplate let-node let-origin="origin">
    <div class="d-flex align-items-end">
      <input type="checkbox" [checked]="origin.checked" id={{origin.id}} name={{origin.id}} class="mb-3"
             (click)="mycheck($event,origin)">
      <nz-table #basicTable [nzData]="['']" [nzShowPagination]="false" nzTableLayout="fixed">
        <thead *ngIf="projectsList[0].id == origin.id">
        <tr>
          <th nzColumnKey="name">{{ 'PROJECTS.PROJECTNUMBER'|translate }}</th>
          <th nzColumnKey="name">{{ 'PROJECTS.PROJECTNAME'|translate }}</th>
          <th nzColumnKey="name">{{ 'PROJECTS.ACCOUNTOPENINGDATE'|translate }}</th>

          <th nzColumnKey="name">{{ 'PROJECTS.NOTES'|translate }}</th>


        </tr>
        </thead>
        <tbody>
        <tr>
          <td>{{ origin.originalData.projectNumber }}</td>
          <td>{{ origin.originalData.projectName }}</td>
          <td><span *ngIf="!showARDate">{{ origin.originalData.accountOpeningDate|date:'fullDate' }}</span> <span
            *ngIf="showARDate"> {{ origin.originalData.arabicAccountOpeningDate| arabicNumerals }}</span></td>
          <td>{{ origin.originalData.notes }}</td>

        </tr>
        </tbody>
      </nz-table>
    </div>
  </ng-template>

  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h2>{{ 'PDF.PROJECTSREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr class="text-blue-500">
        <th>{{ 'PROJECTS.PROJECTNUMBER'|translate }}</th>
        <th>{{ 'PROJECTS.PROJECTNAME'|translate }}</th>
        <th>{{ 'PROJECTS.ACCOUNTOPENINGDATE'|translate }}</th>
        <th>{{ 'PROJECTS.NOTES'|translate }}</th>
        <th>{{ 'ACCOUNT.LEVEL' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let project of flattenedProjectsList">
        <td>{{ project.projectNumber }}</td>
        <td>{{ lang == "en" ? project.project_Latin_Name : project.projectName }}</td>
        <td>{{ project.openingBalanceDate|date:'yyyy-MM-dd' }}</td>
        <td>{{ project.notes }}</td>
        <td>{{ project.level }}</td>
      </tr>
      </tbody>
    </table>

  </div>
  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
