import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { FacilitiesService } from './services/facilities.service';
import { AddEditFacilityComponent } from './add-edit-facility/add-edit-facility.component';
import {MessageService} from "primeng/api";
import {BranchService} from "../branches/branch.service";

@Component({
  selector: 'app-facilities',
  templateUrl: './facilities.component.html',
  styleUrl: './facilities.component.scss'
})
export class FacilitiesComponent implements OnInit {
  facilitiesList: any;

  searchValue: string = '';
  pageSize: number = 5;
  lang: string;
  langDirection: 'ltr' | 'rtl' = 'ltr';

  currentDate: Date = new Date()
  currentRef: string
  flatFacilitiesList: any[]


  constructor(private facilities: FacilitiesService, private modalService: NzModalService, private translate: TranslateService, private messageService: MessageService, private branches: BranchService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'

    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDirection(this.lang)
      this.getFacilities()

    })
  }


  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getFacilities();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }
  getFacilities() {

    this.facilities.getFacilities().subscribe(res => {
      this.facilitiesList = res.data;
      this.flatFacilitiesList = this.flattenFacilities(res.data)
    })
  }


  delete(id: any) {
    this.facilities.deleteFacilities(id).subscribe(res => {
      if (res.message == "Facility deleted") {
        this.translate.get('TOAST.FACILITY_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        })
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        })
      }
      this.getFacilities();
    })
  }
  addNewFacility() {
    this.modalService.create({
      nzContent: AddEditFacilityComponent,
      nzDirection: this.langDirection,
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getFacilities();
      }
    })
  }
  EditFacility(data: any) {
    this.modalService.create({
      nzContent: AddEditFacilityComponent,
      nzDirection: this.langDirection,
      nzData: data
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getFacilities();
      }
    })
  }

  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  sortFnFacilityNum(a: any, b: any) {
    return a.facilityNumber - b.facilityNumber

  }
  sortFnFacilityName(a: any, b: any) {
    return a.facilityNameEn.localeCompare(b.facilityNameEn)

  }
  sortFnAdmin(a: any, b: any) {
    return a.administratorName.localeCompare(b.administratorName)
  }
  sortFnEmail(a: any, b: any) {
    return a.email.localeCompare(b.email)
  }
  sortFnAddress(a: any, b: any) {
    return a.address.localeCompare(b.address)
  }
  sortFnPosalCode(a: any, b: any) {
    return a.postalCode.localeCompare(b.postalCode)
  }
  sortFnPhone(a: any, b: any) {
    return a.phone - b.phone

  }
  sortFnNotes(a: any, b: any) {
    return a.notes.localeCompare(b.notes)
  }

  flattenFacilities(facilities: any[]): any[] {
    let result: any[] = [];
    facilities.forEach((facility) => {
      result.push(facility);
    });
    return result;
  }

}
