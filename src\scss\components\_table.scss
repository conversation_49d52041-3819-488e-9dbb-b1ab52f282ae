/* Enhanced Global Table Styles */
.enhanced-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }

  // Table controls section
  .table-controls {
    background: #fafbff;
    border-bottom: 1px solid #e8e9f3;
    padding: 1.5rem;

    @media (max-width: 768px) {
      padding: 1rem;
    }

    .controls-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
      }
    }

    .show-entries {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 0.9rem;
      color: var(--color-grey);

      .form-select {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        min-width: 80px;
        transition: all 0.3s ease;

        &:focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
          outline: none;
        }
      }
    }

    .search-input {
      flex: 1;
      max-width: 300px;

      @media (max-width: 768px) {
        max-width: 100%;
      }

      .form-control {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        transition: all 0.3s ease;

        &:focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
          outline: none;
        }

        &::placeholder {
          color: #9ca3af;
        }
      }
    }

    .add-button {
      background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
      border: none;
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      font-size: 0.9rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(29, 76, 186, 0.3);
      white-space: nowrap;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 76, 186, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        width: 100%;
        padding: 1rem;
      }
    }
  }
}

/* Enhanced Ant Design Table Overrides */
::ng-deep .enhanced-table-container {
  .ant-table-wrapper {
    .ant-table {
      border-radius: 0;

      .ant-table-thead > tr > th {
        background: linear-gradient(135deg, var(--color-secondary) 0%, #f0f4ff 100%);
        color: var(--color-primary);
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 2px solid var(--color-primary);
        padding: 1rem 0.75rem;
        position: relative;

        @media (max-width: 768px) {
          padding: 0.75rem 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.5rem 0.25rem;
          font-size: 0.7rem;
        }

        // Sort indicators
        .ant-table-column-sorter {
          color: var(--color-primary);
          opacity: 0.6;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }
      }

      .ant-table-tbody > tr > td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        font-size: 0.9rem;
        color: #374151;
        vertical-align: middle;

        @media (max-width: 768px) {
          padding: 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.4rem 0.25rem;
          font-size: 0.75rem;
        }
      }

      .ant-table-tbody > tr:hover > td {
        background-color: rgba(29, 76, 186, 0.05);
      }

      .ant-table-tbody > tr:nth-child(even) {
        background-color: #fafbff;

        &:hover > td {
          background-color: rgba(29, 76, 186, 0.08);
        }
      }
    }
  }
}

/* Status Badge Styles */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: none;
  cursor: default;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
  }

  &.inactive {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
  }

  @media (max-width: 768px) {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
  }
}

/* Action Dropdown Styles */
.action-dropdown {
  position: relative;

  .dropdown-trigger {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    color: var(--color-grey);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: rgba(29, 76, 186, 0.1);
      color: var(--color-primary);
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 180px;

    li {
      padding: 0.25rem;

      .erp-btn {
        width: 100%;
        text-align: left;
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
        }
      }
    }
  }
}