/*
 * UNIVERSAL TABLE CONTAINER
 *
 * How to use:
 * 1. Add the 'table-container' class to your main component wrapper
 * 2. Use the following HTML structure:
 *
 * <div class="table-container">
 *   <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
 *   <h3 class="page-title">{{ "PAGE.TITLE" | translate }}</h3>
 *
 *   <!-- For forms (optional) -->
 *   <div class="form-section">
 *     <form [formGroup]="form">
 *       <div class="form-row">
 *         <label class="form-label">Label</label>
 *         <!-- form controls -->
 *       </div>
 *     </form>
 *     <div class="submit-section">
 *       <button class="btn-submit">Submit</button>
 *     </div>
 *   </div>
 *
 *   <!-- For table controls -->
 *   <div class="table-controls">
 *     <div class="controls-row">
 *       <div class="show-entries">
 *         <span>Show</span>
 *         <select class="form-select" [(ngModel)]="pageSize">
 *           <option value="5">5</option>
 *           <option value="10">10</option>
 *         </select>
 *       </div>
 *       <div class="search-input">
 *         <input type="text" class="form-control" placeholder="Search...">
 *       </div>
 *       <button class="add-button">+ Add New</button>
 *     </div>
 *   </div>
 *
 *   <!-- Table section -->
 *   <div class="table-section">
 *     <nz-table>...</nz-table>
 *   </div>
 *
 *   <!-- Export section -->
 *   <div class="export-section">
 *     <app-export-pdf-button></app-export-pdf-button>
 *   </div>
 * </div>
 *
 * Features included:
 * - Responsive design for all screen sizes
 * - RTL/LTR support
 * - Enhanced form controls (multiselect, date pickers)
 * - Modern table styling with hover effects
 * - Status badges and action dropdowns
 * - Loading and empty states
 * - Consistent spacing and typography
 */
.table-container,
.enhanced-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border: 1px solid #e8e9f3;
  transition: box-shadow 0.3s ease;
  padding: 1.5rem;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  }

  @media (max-width: 768px) {
    border-radius: 8px;
    margin-bottom: 1.5rem;
    padding: 1rem;
  }

  // Page title styling
  .page-title {
    color: var(--color-primary);
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    text-align: center;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  // Table controls section
  .table-controls {
    background: #fafbff;
    border-bottom: 1px solid #e8e9f3;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;
    margin: -1.5rem -1.5rem 1rem -1.5rem;

    @media (max-width: 768px) {
      padding: 1rem;
      margin: -1rem -1rem 0 -1rem;
    }

    .controls-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
      }
    }

   

    .search-input {
      flex: 1;
      max-width: 300px;

      @media (max-width: 768px) {
        max-width: 100%;
      }

     
    }

    .add-button {
      background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
      border: none;
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      font-size: 1.5rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(29, 76, 186, 0.3);
      white-space: nowrap;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 76, 186, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        width: 100%;
        padding: 1rem;
      }
    }
  }
 .show-entries {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 1rem;
      color: var(--color-grey);
      font-weight: 500;
      width: 100px;
      .form-select {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.5rem 0.75rem;
        font-size: 1rem;
        min-width: 80px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--color-primary);
        }

        &:focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
          outline: none;
        }
      }
    }
  // Form section improvements
  .form-section {
    background: #fafbff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e8e9f3;

    @media (max-width: 768px) {
      padding: 1rem;
      margin-bottom: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      color: var(--color-primary);
      margin-bottom: 0.5rem;
      font-size: 1rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-row {
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        margin-bottom: 1rem;
      }
    }

    // Compact form styling
    &.compact-form {
      padding: 1.25rem;

      @media (max-width: 768px) {
        padding: 1rem;
      }

      .form-row {
        margin-bottom: 1rem;

        @media (max-width: 768px) {
          margin-bottom: 0.75rem;
        }
      }

      .compact-label {
        font-size: 0.85rem;
        margin-bottom: 0.4rem;
        font-weight: 600;
        color: var(--color-primary);
        text-transform: uppercase;
        letter-spacing: 0.3px;
      }

      .compact-submit {
        width: 100%;
        height: 38px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        border-radius: 8px;
        margin-top: 0;

        @media (max-width: 768px) {
          margin-top: 0.5rem;
        }
      }
    }
  }

  // Submit button styling
  .submit-section {
    text-align: center;
    margin: 2rem 0;

    .btn-submit {
      background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
      border: none;
      border-radius: 10px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      font-size: 1rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(29, 76, 186, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 76, 186, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        width: 100%;
        padding: 1rem;
      }
    }
  }

  // Table section styling
  .table-section {
    margin-top: 2rem;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

    @media (max-width: 768px) {
      margin-top: 1.5rem;
      border-radius: 8px;
    }
  }
}

/* Enhanced Form Controls */
::ng-deep .custom-multiselect {
  .p-multiselect {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &.p-focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-label {
    font-size: 14px;
    padding: 0.75rem;
    color: #374151;
  }

  .p-multiselect-item {
    font-size: 14px;
    padding: 0.5rem 1rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-panel {
    min-width: 280px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
  }

  .p-multiselect-filter-container {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    gap: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .p-multiselect-filter {
    flex: 1;
    height: 2.5rem;
    font-size: 14px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    line-height: 1.5;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 1rem;

    svg {
      height: 1rem;
      width: 1rem;
    }
  }

  // Compact multiselect styling
  &.compact-select {
    .p-multiselect {
      height: 38px;
      min-height: 38px;
    }

    .p-multiselect-label {
      padding: 0.5rem 0.75rem;
      font-size: 13px;
      line-height: 1.3;
    }

    .p-multiselect-trigger {
      width: 2rem;
    }
  }
}

/* Enhanced date picker styles */
::ng-deep .datepicker {
  width: 100%;

  .ant-picker {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 0.75rem;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &.ant-picker-focused {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }

  // Compact date picker styling
  &.compact-datepicker {
    .ant-picker {
      height: 38px;
      padding: 0.5rem 0.75rem;
      font-size: 13px;
    }
  }
}

/* Enhanced Ant Design Table Overrides */
::ng-deep .table-container {
  .ant-table-wrapper {
    .ant-table {
      border-radius: 0 0 12px 12px;
      overflow: hidden;

      @media (max-width: 768px) {
        border-radius: 0 0 8px 8px;
      }

      .ant-table-thead > tr > th {
        background: linear-gradient(135deg, var(--color-secondary) 0%, #f0f4ff 100%);
        color: var(--color-primary);
        font-weight: 600;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 2px solid var(--color-primary);
        padding: 1rem 0.75rem;
        text-align: center;
        position: relative;

        @media (max-width: 768px) {
          padding: 0.75rem 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.5rem 0.25rem;
          font-size: 0.7rem;
        }

        // Sort indicators
        .ant-table-column-sorter {
          color: var(--color-primary);
          opacity: 0.6;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }
      }

      .ant-table-tbody > tr > td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        font-size: 1rem;
        color: #374151;
        vertical-align: middle;
        text-align: center;

        @media (max-width: 768px) {
          padding: 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.4rem 0.25rem;
          font-size: 0.75rem;
        }
      }

      .ant-table-tbody > tr:hover > td {
        background-color: rgba(29, 76, 186, 0.05);
      }

      .ant-table-tbody > tr:nth-child(even) {
        background-color: #fafbff;

        &:hover > td {
          background-color: rgba(29, 76, 186, 0.08);
        }
      }
    }
  }

  // Mobile responsive table
  @media (max-width: 768px) {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .ant-table {
      min-width: 600px;
    }
  }

  @media (max-width: 480px) {
    .ant-table {
      min-width: 500px;
      font-size: 0.75rem;
    }
  }
}

/* Status Badge Styles */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 1.2rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;

  &.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  }

  &.inactive {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  }

  @media (max-width: 768px) {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    min-width: 70px;
  }
}

/* Action Dropdown Styles */
.action-dropdown {
  position: relative;

  .dropdown-trigger {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    color: var(--color-grey);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(29, 76, 186, 0.1);
      color: var(--color-primary);
      transform: scale(1.1);
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 200px;
    background: white;
    z-index: 1000;

    li {
      padding: 0.25rem;

      .erp-btn {
        width: 100%;
        text-align: left;
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        border: none;
        font-weight: 500;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.erp-btn-primary {
          background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
          color: white;
        }

        &.erp-btn-babyblue {
          background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
          color: white;
        }

        &.erp-btn-gray {
          background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
          color: white;
        }
      }
    }
  }
}

/* Table Content Styling */
.account-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .account-number {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.85rem;
  }

  .account-name {
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.2;
  }

  @media (max-width: 768px) {
    .account-number {
      font-size: 0.75rem;
    }

    .account-name {
      font-size: 0.7rem;
    }
  }
}

.amount {
  font-weight: 600;
  font-family: 'Courier New', monospace;

  &.debit {
    color: #dc2626;
  }

  &.credit {
    color: #059669;
  }

  @media (max-width: 768px) {
    font-size: 0.8rem;
  }
}

.document-type {
  background: rgba(29, 76, 186, 0.1);
  color: var(--color-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
}

.date {
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 500;

  @media (max-width: 768px) {
    font-size: 0.75rem;
  }
}

/* PDF Section Improvements */
.pdf-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid #e5e7eb;

  @media (max-width: 768px) {
    margin-top: 2rem;
    padding-top: 1.5rem;
  }
}

/* RTL/LTR Support */
:host-context([dir="rtl"]) {
  .table-container {
    .table-controls {
      .controls-row {
        direction: rtl;
      }

      .show-entries {
        direction: rtl;
      }
    }

    .form-section .form-label {
      text-align: right;
    }
  }

  .action-dropdown .dropdown-menu {
    li .erp-btn {
      text-align: right;
    }
  }

  ::ng-deep .custom-multiselect .p-multiselect-label {
    text-align: right;
  }

  ::ng-deep .ant-table-thead > tr > th {
    text-align: center;
  }

  ::ng-deep .ant-table-tbody > tr > td {
    text-align: center;
  }
}

:host-context([dir="ltr"]) {
  .table-container {
    .table-controls {
      .controls-row {
        direction: ltr;
      }

      .show-entries {
        direction: ltr;
      }
    }

    .form-section .form-label {
      text-align: left;
    }
  }

  .action-dropdown .dropdown-menu {
    li .erp-btn {
      text-align: left;
    }
  }

  ::ng-deep .custom-multiselect .p-multiselect-label {
    text-align: left;
  }

  ::ng-deep .ant-table-thead > tr > th {
    text-align: center;
  }

  ::ng-deep .ant-table-tbody > tr > td {
    text-align: center;
  }
}

/* Small Screen Optimizations */
@media (max-width: 600px) {
  .table-container {
    padding: 1rem;
    margin-bottom: 1rem;

    .page-title {
      font-size: 1.3rem;
      margin-bottom: 1rem;
    }

    .form-section {
      padding: 0.75rem;
      margin-bottom: 1rem;

      .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.4rem;
      }

      .form-row {
        margin-bottom: 0.75rem;
      }

      // Compact form mobile styles
      &.compact-form {
        padding: 0.75rem;

        .compact-label {
          font-size: 0.75rem;
          margin-bottom: 0.3rem;
        }

        .form-row {
          margin-bottom: 0.5rem;
        }

        .compact-submit {
          height: 36px;
          font-size: 0.85rem;
          margin-top: 0.5rem;
        }
      }
    }

    .submit-section {
      margin: 1.5rem 0;

      .btn-submit {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
      }
    }

    .table-controls {
      padding: 0.75rem;

      .controls-row {
        gap: 0.5rem;
      }

      .show-entries {
        font-size: 0.8rem;

        .form-select {
          font-size: 0.8rem;
          padding: 0.4rem 0.6rem;
        }
      }

      .search-input .form-control {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
      }

      .add-button {
        padding: 0.875rem 1.5rem;
        font-size: 1rem;
      }
    }
  }

  ::ng-deep .custom-multiselect {
    .p-multiselect-label {
      font-size: 12px;
      padding: 0.5rem;
    }

    .p-multiselect-item {
      font-size: 12px;
      padding: 0.4rem 0.75rem;
    }

    .p-multiselect-panel {
      min-width: 250px;
    }

    .p-multiselect-filter {
      font-size: 12px;
      height: 2.2rem;
      padding: 0.4rem 0.6rem;
    }

    .p-multiselect-filter-icon {
      font-size: 1rem;

      svg {
        height: 1rem;
        width: 1rem;
      }
    }

    // Compact multiselect mobile styles
    &.compact-select {
      .p-multiselect {
        height: 36px;
        min-height: 36px;
      }

      .p-multiselect-label {
        padding: 0.4rem 0.6rem;
        font-size: 11px;
      }
    }
  }

  ::ng-deep .datepicker {
    .ant-picker {
      padding: 0.5rem;
      font-size: 12px;
    }

    // Compact date picker mobile styles
    &.compact-datepicker .ant-picker {
      height: 36px;
      padding: 0.4rem 0.6rem;
      font-size: 11px;
    }
  }
}

/* Export Section Styles */
.export-section {
  text-align: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fafbff;
  border-radius: 12px;
  border: 1px solid #e8e9f3;

  @media (max-width: 768px) {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 8px;
  }
}

/* Loading and Empty States */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .loading-spinner {
    margin-bottom: 1rem;
  }
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .empty-description {
    font-size: 1rem;
    opacity: 0.7;
  }
}