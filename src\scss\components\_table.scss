/* Enhanced Global Table Styles */
.enhanced-table-container {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
  border: 1px solid #e8e9f3;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  }

  @media (max-width: 768px) {
    border-radius: 8px;
    margin-bottom: 1.5rem;
  }

  // Page title styling
  .page-title {
    color: var(--color-primary);
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    text-align: center;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  // Table controls section
  .table-controls {
    background: #fafbff;
    border-bottom: 1px solid #e8e9f3;
    padding: 1.5rem;
    border-radius: 12px 12px 0 0;

    @media (max-width: 768px) {
      padding: 1rem;
    }

    .controls-row {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
      }
    }

    .show-entries {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      font-size: 0.9rem;
      color: var(--color-grey);
      font-weight: 500;

      .form-select {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
        min-width: 80px;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--color-primary);
        }

        &:focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
          outline: none;
        }
      }
    }

    .search-input {
      flex: 1;
      max-width: 300px;

      @media (max-width: 768px) {
        max-width: 100%;
      }

      .form-control {
        border-radius: 8px;
        border: 1px solid #d1d5db;
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        transition: all 0.3s ease;

        &:hover {
          border-color: var(--color-primary);
        }

        &:focus {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
          outline: none;
        }

        &::placeholder {
          color: #9ca3af;
        }
      }
    }

    .add-button {
      background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
      border: none;
      border-radius: 10px;
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      font-size: 0.9rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(29, 76, 186, 0.3);
      white-space: nowrap;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 76, 186, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        width: 100%;
        padding: 1rem;
      }
    }
  }
}

/* Enhanced Ant Design Table Overrides */
::ng-deep .enhanced-table-container {
  .ant-table-wrapper {
    .ant-table {
      border-radius: 0;

      .ant-table-thead > tr > th {
        background: linear-gradient(135deg, var(--color-secondary) 0%, #f0f4ff 100%);
        color: var(--color-primary);
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 2px solid var(--color-primary);
        padding: 1rem 0.75rem;
        position: relative;

        @media (max-width: 768px) {
          padding: 0.75rem 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.5rem 0.25rem;
          font-size: 0.7rem;
        }

        // Sort indicators
        .ant-table-column-sorter {
          color: var(--color-primary);
          opacity: 0.6;
          transition: opacity 0.3s ease;

          &:hover {
            opacity: 1;
          }
        }
      }

      .ant-table-tbody > tr > td {
        padding: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        font-size: 0.9rem;
        color: #374151;
        vertical-align: middle;

        @media (max-width: 768px) {
          padding: 0.5rem;
          font-size: 0.8rem;
        }

        @media (max-width: 480px) {
          padding: 0.4rem 0.25rem;
          font-size: 0.75rem;
        }
      }

      .ant-table-tbody > tr:hover > td {
        background-color: rgba(29, 76, 186, 0.05);
      }

      .ant-table-tbody > tr:nth-child(even) {
        background-color: #fafbff;

        &:hover > td {
          background-color: rgba(29, 76, 186, 0.08);
        }
      }
    }
  }
}

/* Status Badge Styles */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  border: none;
  cursor: default;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 80px;

  &.active {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
  }

  &.inactive {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
  }

  @media (max-width: 768px) {
    padding: 0.2rem 0.5rem;
    font-size: 0.7rem;
    min-width: 70px;
  }
}

/* Action Dropdown Styles */
.action-dropdown {
  position: relative;

  .dropdown-trigger {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 6px;
    color: var(--color-grey);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(29, 76, 186, 0.1);
      color: var(--color-primary);
      transform: scale(1.1);
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .dropdown-menu {
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 200px;
    background: white;
    z-index: 1000;

    li {
      padding: 0.25rem;

      .erp-btn {
        width: 100%;
        text-align: left;
        border-radius: 6px;
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
        transition: all 0.3s ease;
        border: none;
        font-weight: 500;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.erp-btn-primary {
          background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
          color: white;
        }

        &.erp-btn-babyblue {
          background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
          color: white;
        }

        &.erp-btn-gray {
          background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
          color: white;
        }
      }
    }
  }
}

/* RTL/LTR Support */
:host-context([dir="rtl"]) {
  .enhanced-table-container {
    .table-controls {
      .controls-row {
        direction: rtl;
      }

      .show-entries {
        direction: rtl;
      }
    }
  }

  .action-dropdown .dropdown-menu {
    li .erp-btn {
      text-align: right;
    }
  }
}

:host-context([dir="ltr"]) {
  .enhanced-table-container {
    .table-controls {
      .controls-row {
        direction: ltr;
      }

      .show-entries {
        direction: ltr;
      }
    }
  }

  .action-dropdown .dropdown-menu {
    li .erp-btn {
      text-align: left;
    }
  }
}

/* Export Section Styles */
.export-section {
  text-align: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fafbff;
  border-radius: 12px;
  border: 1px solid #e8e9f3;

  @media (max-width: 768px) {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 8px;
  }
}

/* Loading and Empty States */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .loading-spinner {
    margin-bottom: 1rem;
  }
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .empty-description {
    font-size: 0.9rem;
    opacity: 0.7;
  }
}