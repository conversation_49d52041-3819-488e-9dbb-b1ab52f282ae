import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { en_US, ar_EG, NzI18nService } from 'ng-zorro-antd/i18n';


@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  lang: string;
  constructor(private i18n: NzI18nService, private translate: TranslateService) {
  }
  ngOnInit(): void {
    this.lang = 'en'
    this.translate.setDefaultLang(this.lang);
    this.translate.use(this.lang);
    this.onLanguageChange()
  }

  switchLanguage() {

    if (this.translate.currentLang === 'en') {
      this.i18n.setLocale(en_US);
      this.changePageDirection('en');
    } else {
      this.i18n.setLocale(ar_EG);
      this.changePageDirection('ar');
    }

  }

  onLanguageChange() {
    this.translate.onLangChange.subscribe(res => {
      localStorage.setItem("lang", res.lang);
      this.switchLanguage();
    });
  }

  // Change page Direction as per Selected Lang
  changePageDirection(lang: string) {
    const htmlTag = document.getElementsByTagName('html')[0];
    if (lang === 'ar') {
      htmlTag.dir = 'rtl';
      htmlTag.lang = 'ar';
    } else {
      htmlTag.dir = 'ltr';
      htmlTag.lang = 'en';
    }
  }
}
