<div class="erp-card erp-shadow-end">
    <div class="row my-3 row px-3">
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
            <P class="text-primary pt-3">{{'STAKEHOLDERS.BASICDATA'|translate}}</P>
            <hr />
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.SALESNUMBER'|translate}}</label>
                        <input type="text" id="serial" class="form-control" formControlName="serial" name="serial">
                    </div>
                </div>
                <div class="col-md-1 text-center pt-5">
                    <label>{{'STAKEHOLDERS.ISSTOPPED'|translate}}</label>
                    <input type="checkbox" class="mx-3 form-check-input" formControlName="isStopped">
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.NAMEAR'|translate}}</label>
                        <input type="text" id="name" class="form-control" formControlName="name" name="name">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.NAMEEN'|translate}}</label>
                        <input type="text" id="nameAr" class="form-control" formControlName="nameAr" name="nameAr">
                    </div>
                </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{'STAKEHOLDERS.OPENINGDATE'|translate}}</label>
                  <br />
                  <nz-date-picker (ngModelChange)="dateHandler()" class="datepicker"
                                  formControlName="openDate"></nz-date-picker>
                </div>
              </div>

              <div class="col-md-6">
                <div class="form-group">
                  <label>{{'STAKEHOLDERS.OPENINGDATE'|translate}} {{'SHARED.HIJRI'|translate}}</label>
                  <br />
                  <input [value]="form?.get('openDateHijri')?.value |arabicNumerals" [attr.disabled]="true"
                         class="form-control" type="text" />
                </div>
              </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.RESPONSIBLE'|translate}}</label>
                    <input type="text" id="responsibleName" class="form-control" formControlName="responsibleName"
                        name="responsibleName">
                </div>
            </div>
            <P class="text-primary pt-3">{{'STAKEHOLDERS.ADDRESSDATA'|translate}}</P>
            <hr />
            <div class="row">
                <div class="col-md-6">
                  <label>{{ 'STAKEHOLDERS.COUNTRY'|translate }}</label>
                  <app-reusable-dropdown
                    [options]="countries"
                    [displayKey]="'name'"
                    [valueKey]="'id'"
                    [formControl]="countryControl"
                    [selectedId]="form?.get('countryId')?.value"
                    (selectedValue)="onSelectedCountry($event)">
                  </app-reusable-dropdown>
                </div>
                <div class="col-md-6">
                  <label>{{ 'STAKEHOLDERS.REGION'|translate }}</label>
                  <app-reusable-dropdown
                    [options]="regions"
                    [displayKey]="'name'"
                    [valueKey]="'id'"
                    [formControl]="regionControl"
                    [selectedId]="form?.get('regionId')?.value"
                    (selectedValue)="onSelectedRegion($event)">
                  </app-reusable-dropdown>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                  <label>{{ 'STAKEHOLDERS.CITY'|translate }}</label>
                  <app-reusable-dropdown
                    [options]="cities"
                    [displayKey]="'name'"
                    [valueKey]="'id'"
                    [formControl]="cityControl"
                    [selectedId]="form?.get('cityId')?.value"
                    (selectedValue)="onSelectedCity($event)">
                  </app-reusable-dropdown>
                </div>
                <div class="col-md-6">
                  <label>{{ 'STAKEHOLDERS.DISTRICT'|translate }}</label>
                  <app-reusable-dropdown
                    [options]="towns"
                    [displayKey]="'name'"
                    [valueKey]="'id'"
                    [formControl]="townControl"
                    [selectedId]="form?.get('townId')?.value"
                    (selectedValue)="onSelectedTown($event)">
                  </app-reusable-dropdown>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.ADDRESS'|translate}}</label>
                    <input type="text" id="fullAddress" class="form-control" formControlName="fullAddress"
                        name="fullAddress">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.STREETNAME'|translate}}</label>
                        <input type="text" id="streetName" class="form-control" formControlName="streetName"
                            name="streetName">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.BUILDINGID'|translate}}</label>
                        <input type="text" id="buildingId" class="form-control" formControlName="buildingId"
                            name="buildingId">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.POSTALBOX'|translate}}</label>
                        <input type="text" id="postalCode" class="form-control" formControlName="postalCode"
                            name="postalCode">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.ZIPCODE'|translate}}</label>
                        <input type="text" id="zipCode" class="form-control" formControlName="zipCode" name="zipCode">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.PHONE1'|translate}}</label>
                        <input type="text" id="firstPhone" class="form-control" formControlName="firstPhone"
                            name="firstPhone">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.PHONE2'|translate}}</label>
                        <input type="text" id="secondPhone" class="form-control" formControlName="secondPhone"
                            name="secondPhone">
                    </div>
                </div>
            </div>
            <P class="text-primary pt-3">{{'STAKEHOLDERS.CONTACTINFORMATION'|translate}}</P>
            <hr />
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.MOBILE1'|translate}}</label>
                        <input type="text" id="firstMobile" class="form-control" formControlName="firstMobile"
                            name="firstMobile">
                    </div>
                </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label>{{'STAKEHOLDERS.EMAIL'|translate}}</label>
                  <input type="text" id="email" class="form-control" formControlName="email" name="email">
                </div>
              </div>
<!--                <div class="col-md-6">-->
<!--                    <div class="form-group">-->
<!--                        <label>{{'STAKEHOLDERS.MOBILE2'|translate}}</label>-->
<!--                        <input type="text" id="secondMobile" class="form-control" formControlName="secondMobile"-->
<!--                            name="secondMobile">-->
<!--                    </div>-->
<!--                </div>-->
            </div>
<!--            <div class="row">-->
<!--                <div class="col-md-6">-->
<!--                    <div class="form-group">-->
<!--                        <label>{{'STAKEHOLDERS.FAX1'|translate}}</label>-->
<!--                        <input type="text" id="firstFax" class="form-control" formControlName="firstFax"-->
<!--                            name="firstFax">-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="col-md-6">-->
<!--                    <div class="form-group">-->
<!--                        <label>{{'STAKEHOLDERS.FAX2'|translate}}</label>-->
<!--                        <input type="text" id="secondFax" class="form-control" formControlName="secondFax"-->
<!--                            name="secondFax">-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.COMMENTS'|translate}}</label>
                    <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                </div>
            </div>
            <P class="text-primary pt-3">{{'STAKEHOLDERS.ACCOUNTDATA'|translate}}</P>
            <hr />
            <!--
            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.COMMISSION'|translate}}</label>
                    <input type="number" id="commission" class="form-control" formControlName="commission"
                        name="commission">
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.TARGET'|translate}}</label>
                    <input type="number" id="target" class="form-control" formControlName="target" name="target">
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label>{{'STAKEHOLDERS.DISCOUNTFORCUSTOMER'|translate}}</label>
                    <input type="number" id="customerDescount" class="form-control" formControlName="customerDescount"
                        name="customerDescount">
                </div>
            </div> -->


            <div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>{{'STAKEHOLDERS.TAXACCOUNTNUMBER'|translate}}</label>
                            <input type="text" id="taxAccountNumber" class="form-control"
                                formControlName="taxAccountNumber" name="taxAccountNumber">
                        </div>
                    </div>
                    <div class="col-md-6">
                      <label>{{ 'STAKEHOLDERS.IDENTITY'|translate }}</label>
                      <app-reusable-dropdown
                        [options]="identityList"
                        [displayKey]="'identity_Name'"
                        [valueKey]="'id'"
                        [formControl]="identityControl"
                        [selectedId]="form?.get('identityNumber')?.value"
                        (selectedValue)="onSelectedIdentity($event)">
                      </app-reusable-dropdown>
                    </div>
                </div>
                <!-- <div class="row">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.FACILITYNUMBER'|translate}}</label>
                        <input type="text" id="facilityNumber" class="form-control" formControlName="facilityNumber"
                            name="facilityNumber">
                    </div>
                </div> -->
                <!-- <div class="row">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.EXTRANUMBER'|translate}}</label>
                        <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                    </div>
                </div> -->
                <div class="row">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.COMPANYNAME'|translate}}</label>
                        <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.COMMERCIALREGISTER'|translate}}</label>
                        <input type="text" id="commercialCode" class="form-control" formControlName="commercialCode" name="commercialCode">
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label>{{'STAKEHOLDERS.BANKNUMBER'|translate}}</label>
                        <input type="text" id="bankName" class="form-control" formControlName="bankName" name="bankName">
                    </div>
                </div>
            </div>
            <div class="row my-3 row px-3 text-align-end">

                <div class="col-12 text-align-end">
                    <button *ngIf="!id" class="btn btn-primary"  (click)="onSubmit()">
                        {{'SHARED.ADD' |translate}}</button>
                    <button *ngIf="id" class="btn btn-primary" (click)="edit()">
                        {{'SHARED.SAVE' |translate}}</button>
                </div>

            </div>
        </form>
    </div>
</div>
