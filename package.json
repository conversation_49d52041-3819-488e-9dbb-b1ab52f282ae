{"name": "erp", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^17.3.10", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/forms": "^17.3.0", "@angular/material": "^17.3.10", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/router": "^17.3.0", "@ngx-translate/core": "^15.0.0", "@ngx-translate/http-loader": "^8.0.0", "bootstrap": "^5.3.3", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "moment-hijri": "^2.30.0", "ng-zorro-antd": "^17.4.1", "ngx-spinner": "^17.0.0", "primeflex": "^3.3.1", "primeng": "^17.18.15", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.8", "@angular/cli": "^17.3.8", "@angular/compiler-cli": "^17.3.0", "@types/jasmine": "~5.1.0", "@types/moment-hijri": "^2.1.4", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.4.2"}}