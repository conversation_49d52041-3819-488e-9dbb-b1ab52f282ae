export interface SystemSettings {
  id?: string;
  assetsAccountId?: string;
  assetsAccountNumber?: string;
  deductAccountId?: string;
  deductAccountNumber?: string;
  prophitAccountId?: string;
  prophitAccountNumber?: string;
  yearProphitAccountId?: string;
  yearProphitAccountNumber?: string;
  incomingAccountId?: string;
  incomingAccountNumber?: string;
  costsAccountId?: string;
  costsAccountNumber?: string;
  outComeAccountId?: string;
  outComeAccountNumber?: string;
  expensesAccountId?: string;
  expensesAccountNumber?: string;
  purchasesAccountId?: string;
  purchasesAccountNumber?: string;
  boxAccountId?: string;
  boxAccountNumber?: string;
  sellsAccountId?: string;
  sellsAccountNumber?: string;
  bankNumber?: string;
  isHijriDate?: boolean;
  startYear?: string;
  endYear?: string;
}
