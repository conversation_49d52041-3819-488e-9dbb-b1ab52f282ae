import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from "@angular/forms";
import {TranslateService} from "@ngx-translate/core";
import {BondsService} from "../../bonds/services/bonds.service";
import {ReportsService} from "../services/reports.service";
import { MenuItem } from 'primeng/api';
import {dateRangeValidator} from "../../../shared/utils/validators";
import {Account, AnalyticReport, Report} from "../../../core/models/reports";
import {ActivityService} from "../../activity/services/activity.service";
import {CostService} from "../../cost/services/cost.service";
import {ProjectsService} from "../../projects/services/projects.service";
import {BranchService} from "../../branches/branch.service";
import {flattenNodes} from "../../../../utils/functions";

@Component({
  selector: 'app-analytical-reports',
  templateUrl: './analytical-reports.component.html',
  styleUrl: './analytical-reports.component.scss'
})
export class AnalyticalReportsComponent implements OnInit {
  form: FormGroup
  lang : string = "en"
  accountsList: Account[]
  activityList: any[] = [];
  costList: any[] = [];
  projectList: any[] = [];
  branchesList: any[] = [];
  breadcrumbItems: MenuItem[] = [];
  reportList: any[] = [];
  currentDate: Date = new Date()
  currentRef: string

  constructor(
      private fb: FormBuilder,
      private bonds: BondsService,
      private  translate: TranslateService,
      private reports: ReportsService,
      private cdr: ChangeDetectorRef,
      private activity: ActivityService,
      private costCenter: CostService,
      private projects: ProjectsService,
      private branch: BranchService
    ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.translate
        .get(['SIDEBAR.REPORTS', 'REPORTS.ANALYTICALREPORTS'])
        .subscribe((translations) => {
          this.breadcrumbItems = [
            { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
            { label: translations['REPORTS.ANALYTICALREPORTS'] },
          ];
        });
    })
  }

  ngOnInit() {
    this.fetchingAccounts();
    this.fetchingActivities();
    this.fetchingCostCenter()
    this.fetchingProjects();
    this.fetchingBranches()
    this.createForm()
    this.translate
      .get(['SIDEBAR.REPORTS', 'REPORTS.ANALYTICALREPORTS'])
      .subscribe((translations) => {
        this.breadcrumbItems = [
          { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
          { label: translations['REPORTS.ANALYTICALREPORTS'] },
        ];
      });

    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  createForm() {
    this.form = this.fb.group({
      accountIds: [[]],
      activityIds: [[]],
      costCenterIds: [[]],
      projectIds: [[]],
      brancheIds: [[]],
      startDate: [''],
      endDate: ['']
    }, { validators: dateRangeValidator() });
  }

  fetchingAccounts() {
    this.bonds.getAccounts().subscribe((res) => {
      this.accountsList = res.data.map((account: any) => ({
        ...account,
        label: `${account.accountName} - ${account.accountNumber}`
      }));
    });
  }

  fetchingActivities() {
    this.activity.getActivities().subscribe(
      (res) => {
        this.activityList = res.data.map((activity: any) => ({
          ...activity,
          label: `${activity.activityName} - ${activity.activityNumber}`
        }))
      }
    );
  }

  fetchingCostCenter() {
    this.costCenter.getCost().subscribe(
      (res) => {
        this.costList = flattenNodes(res.data, 'childCostCenter', 'isDetailed', true).map((cost: any) => ({
          ...cost,
          label: `${cost.costCenterName} - ${cost.costCenterNumber}`
        }))
      }
    )
  }

  fetchingProjects() {
    this.projects.getProjects().subscribe((res) => {
      this.projectList = flattenNodes(res.data, "childProjects", "isDetailed" , true).map((project : any) => ({
        ...project,
        label: `${project.projectName} - ${project.projectNumber}`
      }))
    })
  }

  fetchingBranches() {
    this.branch.getBranches().subscribe((res) => {
      this.branchesList = res.data.map((branch : any) => ({
        ...branch,
        label: `${branch.branchName} - ${branch.branchNumber}`
      }))
    })
  }

  onSubmit() {
    const selectedAccountIds = this.form.value.accountIds.map((account: any) => account.id);

    const selectedActivityIds = this.form.value.activityIds.map((activity: any) => activity.id);

    const formValue = {
      ...this.form.value,
      accountIds: selectedAccountIds,
      activityIds: selectedActivityIds,
      costCenterIds: this.form.value.costCenterIds.map((cost: any) => cost.id),
      projectIds: this.form.value.projectIds.map((project: any) => project.id),
      brancheIds: this.form.value.brancheIds.map((branch: any) => branch.id),
    };


    this.reports.getAnalyticalReports(formValue).subscribe((res: any) => {
      this.reportList = res.data;
      this.cdr.detectChanges();
    });
  }

  getSelectedAccountNames(): string {
    if (!this.form.value.accountIds || this.form.value.accountIds.length === 0) {
      return 'Nothing Selected';
    }
    return this.form.value.accountIds
      .map((acc: any) => acc.label)
      .join(', ');
  }

  getSelectedActivityNames(): string {
    if (!this.form.value.activityIds || this.form.value.activityIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا شيء محدد";
    }
    return this.form.value.activityIds
      .map((activity: any) => activity.label)
      .join(', ');
  }
  getSelectedCostNames(): string {
    if (!this.form.value.costCenterIds || this.form.value.costCenterIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا شيء محدد";
    }
    return this.form.value.costCenterIds
      .map((cost: any) => cost.label)
      .join(', ');
  }

  getSelectedProjectNames(): string {
    if (!this.form.value.projectIds || this.form.value.projectIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا شيء محدد";
    }
    return this.form.value.projectIds
      .map((project: any) => project.label)
      .join(', ');
  }

  getSelectedBranchNames(): string {
    if (!this.form.value.bracheIds || this.form.value.bracheIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا شيء محدد";
    }
    return this.form.value.bracheIds
      .map((branch: any) => branch.label)
      .join(', ');
  }
}
