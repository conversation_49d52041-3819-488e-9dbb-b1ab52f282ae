// Component-specific styles
.all-final-reports-container {
  .erp-card {
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e9f3;
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }
  }

  // Page title styling
  .page-title {
    color: var(--color-primary);
    font-weight: 600;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    text-align: center;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  // Form section improvements
  .form-section {
    background: #fafbff;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e8e9f3;

    @media (max-width: 768px) {
      padding: 1rem;
      margin-bottom: 1.5rem;
    }

    .form-label {
      font-weight: 500;
      color: var(--color-primary);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .form-row {
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        margin-bottom: 1rem;
      }
    }
  }

  // Submit button styling
  .submit-section {
    text-align: center;
    margin: 2rem 0;

    .btn-submit {
      background: linear-gradient(135deg, var(--color-primary) 0%, #2563eb 100%);
      border: none;
      border-radius: 10px;
      padding: 0.75rem 2rem;
      font-weight: 500;
      font-size: 1rem;
      color: white;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(29, 76, 186, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 76, 186, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      @media (max-width: 768px) {
        width: 100%;
        padding: 1rem;
      }
    }
  }
}

/* Enhanced multiselect styles */
::ng-deep .custom-multiselect {
  .p-multiselect {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &.p-focus {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-label {
    font-size: 14px;
    padding: 0.75rem;
    color: #374151;
  }

  .p-multiselect-item {
    font-size: 14px;
    padding: 0.5rem 1rem;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-panel {
    min-width: 280px;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border: 1px solid #e5e7eb;
  }

  .p-multiselect-filter-container {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    gap: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .p-multiselect-filter {
    flex: 1;
    height: 2.5rem;
    font-size: 14px;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    border: 1px solid #d1d5db;
    line-height: 1.5;

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px rgba(29, 76, 186, 0.1);
    }
  }

  .p-multiselect-filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
    font-size: 1rem;

    svg {
      height: 1rem;
      width: 1rem;
    }
  }
}

/* Enhanced date picker styles */
::ng-deep .datepicker {
  width: 100%;

  .ant-picker {
    width: 100%;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 0.75rem;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &.ant-picker-focused {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 3px rgba(29, 76, 186, 0.1);
    }
  }
}

/* Responsive table styles */
.table-section {
  margin-top: 2rem;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  @media (max-width: 768px) {
    margin-top: 1.5rem;
    border-radius: 8px;
  }
}

::ng-deep .ant-table-wrapper {
  .ant-table {
    border-radius: 12px;
    overflow: hidden;

    @media (max-width: 768px) {
      border-radius: 8px;
    }
  }

  .ant-table-thead > tr > th {
    background: linear-gradient(135deg, var(--color-secondary) 0%, #f0f4ff 100%);
    color: var(--color-primary);
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid var(--color-primary);
    padding: 1rem 0.75rem;

    @media (max-width: 768px) {
      padding: 0.75rem 0.5rem;
      font-size: 0.8rem;
    }

    @media (max-width: 480px) {
      padding: 0.5rem 0.25rem;
      font-size: 0.7rem;
    }
  }

  .ant-table-tbody > tr > td {
    padding: 0.75rem;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.9rem;
    color: #374151;

    @media (max-width: 768px) {
      padding: 0.5rem;
      font-size: 0.8rem;
    }

    @media (max-width: 480px) {
      padding: 0.4rem 0.25rem;
      font-size: 0.75rem;
    }
  }

  .ant-table-tbody > tr:hover > td {
    background-color: rgba(29, 76, 186, 0.05);
  }

  .ant-table-tbody > tr:nth-child(even) {
    background-color: #fafbff;
  }
}

/* Mobile responsive table */
@media (max-width: 768px) {
  .table-section {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  ::ng-deep .ant-table {
    min-width: 600px;
  }
}

@media (max-width: 480px) {
  ::ng-deep .ant-table {
    min-width: 500px;
    font-size: 0.75rem;
  }
}

/* PDF section improvements */
.pdf-section {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid #e5e7eb;

  @media (max-width: 768px) {
    margin-top: 2rem;
    padding-top: 1.5rem;
  }
}

/* Export button section */
.export-section {
  text-align: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: #fafbff;
  border-radius: 12px;
  border: 1px solid #e8e9f3;

  @media (max-width: 768px) {
    margin-top: 1.5rem;
    padding: 1rem;
    border-radius: 8px;
  }
}

/* RTL/LTR support improvements */
:host-context([dir="rtl"]) {
  .form-section .form-label {
    text-align: right;
  }

  ::ng-deep .custom-multiselect .p-multiselect-label {
    text-align: right;
  }

  ::ng-deep .ant-table-thead > tr > th {
    text-align: center;
  }

  ::ng-deep .ant-table-tbody > tr > td {
    text-align: center;
  }
}

:host-context([dir="ltr"]) {
  .form-section .form-label {
    text-align: left;
  }

  ::ng-deep .custom-multiselect .p-multiselect-label {
    text-align: left;
  }

  ::ng-deep .ant-table-thead > tr > th {
    text-align: center;
  }

  ::ng-deep .ant-table-tbody > tr > td {
    text-align: center;
  }
}

/* Loading and empty states */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .loading-spinner {
    margin-bottom: 1rem;
  }
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: var(--color-grey);

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-message {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .empty-description {
    font-size: 0.9rem;
    opacity: 0.7;
  }
}

/* Small screen optimizations */
@media (max-width: 600px) {
  .all-final-reports-container {
    .erp-card {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .page-title {
      font-size: 1.3rem;
      margin-bottom: 1rem;
    }

    .form-section {
      padding: 0.75rem;
      margin-bottom: 1rem;

      .form-label {
        font-size: 0.8rem;
        margin-bottom: 0.4rem;
      }

      .form-row {
        margin-bottom: 0.75rem;
      }
    }

    .submit-section {
      margin: 1.5rem 0;

      .btn-submit {
        padding: 0.875rem 1.5rem;
        font-size: 0.9rem;
      }
    }
  }

  ::ng-deep .custom-multiselect {
    .p-multiselect-label {
      font-size: 12px;
      padding: 0.5rem;
    }

    .p-multiselect-item {
      font-size: 12px;
      padding: 0.4rem 0.75rem;
    }

    .p-multiselect-panel {
      min-width: 250px;
    }

    .p-multiselect-filter {
      font-size: 12px;
      height: 2.2rem;
      padding: 0.4rem 0.6rem;
    }

    .p-multiselect-filter-icon {
      font-size: 0.9rem;

      svg {
        height: 0.9rem;
        width: 0.9rem;
      }
    }
  }

  ::ng-deep .datepicker .ant-picker {
    padding: 0.5rem;
    font-size: 12px;
  }
}

/* Table content styling */
.account-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  .account-number {
    font-weight: 600;
    color: var(--color-primary);
    font-size: 0.85rem;
  }

  .account-name {
    color: #6b7280;
    font-size: 0.8rem;
    line-height: 1.2;
  }
}

.amount {
  font-weight: 600;
  font-family: 'Courier New', monospace;

  &.debit {
    color: #dc2626;
  }

  &.credit {
    color: #059669;
  }
}

.document-type {
  background: rgba(29, 76, 186, 0.1);
  color: var(--color-primary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.date {
  color: #6b7280;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive table content */
@media (max-width: 768px) {
  .account-info {
    .account-number {
      font-size: 0.75rem;
    }

    .account-name {
      font-size: 0.7rem;
    }
  }

  .amount {
    font-size: 0.8rem;
  }

  .document-type {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .date {
    font-size: 0.75rem;
  }
}
