type GenericNode<T> = T & { childNodes?: GenericNode<T>[] };

export function flattenNodes<T>(data: GenericNode<T>[], childKey: keyof GenericNode<T> = "childNodes", filterKey?: keyof T, filterValue?: any): T[] {
  let result: T[] = [];

  function traverse(node: GenericNode<T>) {
    // Extract children and exclude childKey
    const { [childKey]: _, ...rest } = node;
    const nodeData = rest as T; // Explicitly cast to T

    // Apply filter condition if provided
    if (!filterKey || nodeData[filterKey] === filterValue) {
      result.push(nodeData);
    }

    // Recursively flatten child nodes
    if (node[childKey] && Array.isArray(node[childKey])) {
      (node[childKey] as GenericNode<T>[]).forEach(traverse);
    }
  }

  data.forEach(traverse);
  return result;
}
