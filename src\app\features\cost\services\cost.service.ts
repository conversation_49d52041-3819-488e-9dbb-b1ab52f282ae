import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class CostService {

  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }
  addCost(data: any) {
    if (this.branchId) {
      data.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/CostCenters`, data);

  }
  getCost() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/CostCenters');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/CostCenters?branchId=${this.branchId}`);
    }
  }
  updateCost(id: any, data: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/CostCenters/${id}`, data);
  }
  deleteCost(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/CostCenters/${id}`);
  }


}
