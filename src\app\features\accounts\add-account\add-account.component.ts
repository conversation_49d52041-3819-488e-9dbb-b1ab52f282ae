import {Component, inject, SimpleChanges} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { AccountService } from '../services/account.service';
import { NzI18nService } from 'ng-zorro-antd/i18n';
import moment from 'moment-hijri';
import { BondsService } from '../../bonds/services/bonds.service';
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
function atLeastOneCheckbox(control: FormGroup) {
  const isPrivate = control.get('isPrivate')?.value;
  const isCostCenter = control.get('isCostCenter')?.value;

  if (!isPrivate && !isCostCenter) {
    return { atLeastOneCheckboxRequired: true };
  }
  return null;
}

@Component({
  selector: 'app-add-account',
  templateUrl: './add-account.component.html',
  styleUrl: './add-account.component.scss'
})
export class AddAccountComponent {
  form: FormGroup;
  data: any
  currencyList: any;

  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  isUpdate: boolean = false;
  ischild: any;
  parentAccountId: any;
  childLevel: any;
  converssionRateValue: any;
  accountsList: any;
  lang: string = 'en'

  constructor(private modal: NzModalRef, private bonds: BondsService, private fb: FormBuilder, private account: AccountService, private i18n: NzI18nService, private  translate: TranslateService, private messageService: MessageService

  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
    })
  }
  ngOnInit() {
    this.getCurrency();
    this.createFormAdd();
    this.getAccounts();
  }

  dateHandler() {
    if (this.form?.get('accountOpeningDate')?.value) {

      let date = moment(this.form?.get('accountOpeningDate')?.value).format('iD iMMMM iYYYY')

      this.form.patchValue({
        accountOpeningDatehijri: date
      });
    }
  }
  updateConversionRate() {
    if (this.form?.get('currencyId')?.value) {

      this.currencyList.forEach((element: any) => {

        if (this.form?.get('currencyId')?.value == element.id)
          this.converssionRateValue = element.conversionRate
      });
      this.form.patchValue({
        conversionRate: this.converssionRateValue
      });
    }
  }
  createFormAdd() {
    this.ischild = this.nzModalData?.child;
    if (this.ischild) {

      this.parentAccountId = this.nzModalData?.id;
      this.childLevel = this.nzModalData?.originalData?.level + 1;
    }

    if (!this.ischild) {
      this.data = this.nzModalData?.originalData;
      if (this.data) {
        this.isUpdate = true;
      }
    }
    const level = this.data?.level || this.childLevel || null;
    let status = this.data?.status == 0 ? '0' : 1
    this.form = this.fb.group({
      shortName: [this.data?.shortName || '', Validators.required],
      accountName: [this.data?.accountName || '', Validators.required],
      account_Name_Latin: [this.data?.account_Name_Latin || '', Validators.required],
      currencyId: [this.data?.currencyId || '', Validators.required],
      accountOpeningDate: [this.data?.accountOpeningDate || '', Validators.required],
      creditLimit: [this.data?.creditLimit || ''],
      openingBalance: [this.data?.openingBalance || ''],
      openingBalanceDate: [this.data?.openingBalanceDate || ''],
      estimatedBudget: [this.data?.estimatedBudget || '', [Validators.required, Validators.min(0)]],
      notes: [this.data?.notes || '',],
      email: [this.data?.email || '', [Validators.email]],
      status: [status || ''],
      isPrivate: [this.data?.isPrivate || false],
      isCostCenter: [this.data?.isCostCenter || false],
      relatedAccount:[this.data?.relatedAccount || ''],
      level: [level],
      isDetailed: [this.data?.isDetailed || false],
      vatRegister: [this.data?.vatRegister || ''],
      openingBalanceByInForeignCurrency: [this.data?.openingBalanceByInForeignCurrency || ''],
      parentAccountId: [this.parentAccountId || this.data?.parentAccountId || null],
      accountOpeningDatehijri: [],
      conversionRate: [this.data?.conversionRate],
      accountNumber: [this.data?.accountNumber],
      parentAccountNum: [ this.nzModalData.accountNumber || this.data?.parentAccountNum]
    }, {
      validators: [
        atLeastOneCheckbox,

      ]
    });
    this.dateHandler();

    this.form.controls['parentAccountNum'].disable();
    this.form.controls['accountNumber'].disable();
  }

  destroyModal(): void {
    this.modal.destroy();
  }
  getAccounts() {
    this.bonds.getAccounts().subscribe(res => {
      this.accountsList = res.data;
      if (this.isUpdate && this.data && this.data.relatedAccount) {

        const selectedAccount = this.accountsList.find(
          (account: any) => account.id === this.data.relatedAccount
        );

        if (selectedAccount) {
          this.onSelectedAccount(selectedAccount);
        }
      }
    })
  }

  // controlling Account
  get accountNameControl(): FormControl {
    return this.form.get('relatedAccount') as FormControl;
  }

  onSelectedAccount(selected: any): void {
    this.form.patchValue({
      relatedAccount: selected.id,
    });
  }

  getCurrency() {
    this.account.getCurrency().subscribe((res) => {
      this.currencyList = res.data.filter((item: any) => item.isActive == true);

      if (this.isUpdate && this.data && this.data.currencyId) {

        const selectedCurrency = this.currencyList.find(
          (currency: any) => currency.id === this.data.currencyId
        );

        if (selectedCurrency) {
          this.onCurrencySelected(selectedCurrency);
        }
      }
    });
  }

  // selecting currency
  onCurrencySelected(selected: any): void {
    this.form.patchValue({
      currencyId: selected.id,
    });
    this.updateConversionRate();
  }

  // controlling currency
  get currencyIdControl(): FormControl {
    return this.form.get('currencyId') as FormControl;
  }

  onRadioChange(value: boolean) {
    this.form.get('isDetailed')?.setValue(value);
  }

  onSubmit() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      this.account.addAccount(data).subscribe(res => {
        this.modal.destroy(res);
        if (res.message == "Account created") {
          this.translate.get('TOAST.ACCOUNT_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  update() {
    if (this.form.valid) {
      let dataForm = this.form.getRawValue();
      dataForm == '0' ? 0 : dataForm
      this.account.updateAccount(this.data.id, dataForm).subscribe(res => {

        this.modal.destroy(res);
        if (res.message == "Account updated") {
          this.translate.get('TOAST.ACCOUNT_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: "Error",
            detail: "Something went wrong"
          })
        }
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
