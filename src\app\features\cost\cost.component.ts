import {ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { AddEditCostComponent } from './add-edit-cost/add-edit-cost.component';
import { CostService } from './services/cost.service';
import { NzTreeComponent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import {MessageService} from "primeng/api";
import {BranchService} from "../branches/branch.service";

@Component({
  selector: 'app-cost',
  templateUrl: './cost.component.html',
  styleUrl: './cost.component.scss'
})
export class CostComponent implements OnInit{
  costList: any;
  translatedHeaders: string[] = [];
  translatedData: any[] = [];
  flattenedCost: any[] = [];
  showARDate: boolean = false;
  @ViewChild('nzTreeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;
  @ViewChild('pdfTable', { static: false }) pdfTable!: ElementRef;
  selectedCost: any;
  child: any;
  currentDate: Date = new Date()
  currentRef: string

  constructor(private cost: CostService, private modalService: NzModalService, private translate: TranslateService, private messageService: MessageService, private cd: ChangeDetectorRef, private branches: BranchService
  ) {
    this.getDirection();
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getCost()
      if (this.lang === 'ar') {
        this.showARDate = true;
      }
      else {
        this.showARDate = false;
      }
      this.getDirection()

    })
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string = "en";
  langDirection: 'ltr' | 'rtl' = 'ltr';
  addCostModal() {
    if (this.selectedCost) {
      this.child = true
    }
    this.modalService.create({
      nzContent: AddEditCostComponent,
      nzDirection: this.langDirection,
      nzData: { child: this.child, ...this.selectedCost },
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedCost = null;
        this.getCost();
      }
    })
  }
  getDirection() {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';
    }
  }

  shouldShowAddButton(): boolean {
    // Show the button if no account is selected
    if (!this.selectedCost) {
      return true;
    }

    // Check if the selected account has isDetailed === true
    // Look in originalData first, then direct property
    const isDetailed = this.selectedCost.originalData?.isDetailed || this.selectedCost.isDetailed;

    // Hide button if isDetailed is true, show otherwise
    return isDetailed !== true;
  }

  mycheck(event: any, origin: any) {
    event.stopPropagation();

    // If the node is already checked, uncheck it (toggle behavior)
    if (origin.checked) {
      origin.checked = false;
      this.selectedCost = null;  // Deselect the cost
    } else {
      // Uncheck all nodes first
      this.uncheckAllNodes();

      // Check the selected node
      origin.checked = true;
      this.selectedCost = origin;  // Set the selected cost
    }

    // Ensure UI reflects changes
    this.cd.detectChanges();
  }

  uncheckAllNodes() {
    // Use the nzTreeComponent for projects
    this.nzTreeComponent.getTreeNodes().forEach(node => this.uncheckNodeRecursively(node));

    // Reset selected Cost when no nodes are selected
    this.selectedCost = null;
    this.cd.detectChanges();
  }

  uncheckNodeRecursively(node: any) {
    // Uncheck the node and its children
    node.origin.checked = false;
    if (node.children) {
      node.children.forEach((child: any) => this.uncheckNodeRecursively(child));
    }
  }

  uncheckOtherNodes(e: any) {
    e.children.forEach((c: any) => {
      c.checked = false;
      this.uncheckOtherNodes(c)
    })
  }

  updateCostModal() {
    this.modalService.create({
      nzContent: AddEditCostComponent,
      nzDirection: this.langDirection,
      nzData: this.selectedCost,
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedCost = null;
        this.getCost();
      }
    })

  }
  deleteCost() {

    this.cost.deleteCost(this.selectedCost.id).subscribe(res => {
      if (res.message == "Cost Center deleted") {
        this.translate.get('TOAST.COST_CENTER_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.selectedCost = null;
      this.getCost();
    })
  }
  transformToNzTreeNodeOptions(data: any): NzTreeNodeOptions[] {
    return data.map((node: any) => ({
      id: node.id, // map id to key
      disabled: false,
      checked: false,
      originalData: { ...node },
      children: this.transformToNzTreeNodeOptions(node.childCostCenter) // recursively transform children
    }));
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getCost();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }
  getCost() {
    this.cost.getCost().subscribe(res => {
      this.costList = res.data

      this.costList.forEach((element: any) => {
        if (element.openingBalanceDate) {
          element.arabicAccountOpeningDate = new Date(element.openingBalanceDate).toLocaleDateString('ar-EG-u-nu-latn', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })
        }
      });
      this.costList = this.transformToNzTreeNodeOptions(this.costList)
      this.flattenedCost = this.flattenCosts(res.data);
    })
  }

  flattenCosts(costs: any[]): any[] {
    let result: any[] = [];
    costs.forEach((cost) => {
      result.push(cost);
      if (cost.childCostCenter && cost.childCostCenter.length > 0) {
        result = result.concat(this.flattenCosts(cost.childCostCenter));
      }
    });
    return result;
  }

}
