.input-group:not(.has-validation)> :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation)>.dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation)>.form-floating:not(:last-child)>.form-control,
.input-group:not(.has-validation)>.form-floating:not(:last-child)>.form-select {
    &:lang(ar) {
        border-top-right-radius: var(--bs-border-radius);

        border-bottom-right-radius: var(--bs-border-radius);
        border-top-left-radius: 0px;
        border-bottom-left-radius: 0px;

    }

}

.input-group> :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {

    &:lang(ar) {

        border-top-left-radius: var(--bs-border-radius);

        border-bottom-left-radius: var(--bs-border-radius);

        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
    }
}
.datepicker{
    width: 100%;
    border-radius: 1.3rem;
    padding: 1rem;
}
.radio-style{

    padding-top: 3rem !important;
    &-padding{
        padding-right: 1rem;
    }
}