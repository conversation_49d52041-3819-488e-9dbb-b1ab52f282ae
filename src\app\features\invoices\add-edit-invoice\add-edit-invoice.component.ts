import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import { InvoiceService } from '../invoice.service';
import { StakeholdersService } from '../../stakeholders/stakeholders.service';
import { BondsService } from '../../bonds/services/bonds.service';
import { CostService } from '../../cost/services/cost.service';
import { ProjectsService } from '../../projects/services/projects.service';
import { ActivityService } from '../../activity/services/activity.service';
import { CurrencyService } from '../../currencies/services/currency.service';
import { SalesAccountService } from '../../sales-account/sales-account.service';
import { ActivatedRoute, Router } from '@angular/router';
import {forkJoin, of, Subject, takeUntil} from "rxjs";
import {NgxSpinnerService} from "ngx-spinner";
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import {filterDetailedCostCenters, filterDetailedProjects} from "../../../shared/utils/filters";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-add-edit-invoice',
  templateUrl: './add-edit-invoice.component.html',
  styleUrl: './add-edit-invoice.component.scss'
})
export class AddEditInvoiceComponent implements OnInit {
  form: FormGroup;
  tempform: FormGroup;
  dalegateList: any;
  clientList: any;
  nameAr: any;
  name: any;
  responsibleName: any;
  address: any;
  accountsList: any;
  costList: any;
  projectList: any;
  activityList: any;
  paymentList: any;
  currencyList: any;
  converssionRateValue: any;
  salesAccountList: any;
  branchId: string = '';
  tempName: any;
  tempNameAr: any;
  pageSize: number = 5;
  invoiceLines: any = [];
  id: any;
  data: any;

  lang: string = 'en';
  showInvoiceItems = false;
  showAccountData = false;
  isInitialized: boolean = false;
  private destroy$ = new Subject<void>();

  constructor(private route: ActivatedRoute, private translate: TranslateService, private router: Router, private salesAccount: SalesAccountService, private currency: CurrencyService, private activity: ActivityService, private projects: ProjectsService, private cost: CostService, private fb: FormBuilder, private bonds: BondsService, private invoice: InvoiceService, private stakeholder: StakeholdersService, private spinner: NgxSpinnerService,  private cdr: ChangeDetectorRef, private messageService: MessageService, private branches: BranchService) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang
    })

    this.createForm();
    this.id = this.route.snapshot.paramMap.get('id');
  }

  // Show sections
  showSections(section: string) {
    this.spinner.show()
    if (section === 'invoiceItems') {
      this.showInvoiceItems = true;
    } else if (section === 'accountData') {
      this.showAccountData = true;
    }
    setTimeout(() => {
      this.spinner.hide();
      this.cdr.detectChanges();
    },2000);
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
    this.loadData();

    if (this.id) {
      this.showInvoiceItems = true
      this.showAccountData = true
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadData() {
    this.spinner.show();

    forkJoin({
      delegates: this.stakeholder.getDelegate(),
      clients: this.stakeholder.getClient(),
      accounts: this.bonds.getAccounts(),
      costs: this.cost.getCost(),
      projects: this.projects.getProjects(),
      activities: this.activity.getActivities(),
      payments: this.invoice.getPaymentMethod(),
      currencies: this.currency.getCurrencies(),
      salesAccounts: this.salesAccount.getsalesaccount(),
      invoiceData: this.id ? this.invoice.getInvoiceById(this.id) : of(null)
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: any) => {
          // Populate dropdown lists
          this.dalegateList = response.delegates.data;
          this.clientList = response.clients.data;
          this.accountsList = response.accounts.data;
          this.costList = filterDetailedCostCenters(response.costs.data);
          this.projectList = filterDetailedProjects(response.projects.data);
          this.activityList = response.activities.data;
          this.paymentList = response.payments.data;
          this.currencyList = response.currencies.data.filter((item: any) => item.isActive);
          this.salesAccountList = response.salesAccounts.data;

          // Populate form if editing
          if (this.id && response.invoiceData) {
            this.data = response.invoiceData.data;
            this.invoiceLines = this.data.invoiceLines || [];
            this.form.patchValue({
              nameAr: this.data.nameAr,
              name: this.data.name,
              responsibleName: this.data.responsibleName,
              address: this.data.address,
              paymentTypeId: this.data.paymentTypeId,
              clientType: this.data.clientType,
              isDebit: this.data.isDebit,
              currencyId: this.data.currencyId,
              currencyRate: this.data.currencyRate,
              brnchId: this.data.brnchId,
              invoiceDate: this.data.invoiceDate,
              delegateId: this.data.delegateId,
              terms: this.data.terms,
              notes: this.data.notes,
              reciever: this.data.reciever,
              phoneNumber: this.data.phoneNumber,
              clientId: this.data.clientId,
              totalPrice: this.data.totalPrice,
              descount: this.data.descount,
              taxPer: this.data.taxPer,
              taxAmount: this.data.taxAmount,
              finalAmount: this.data.finalAmount,
              debitAccountId: this.data.debitAccountId,
              creditAccountId: this.data.creditAccountId,
              taxAccountAccountId: this.data.taxAccountAccountId,
              financialReportHeaderId: this.data.financialReportHeaderId,
              costCenterId: this.data.costCenterId,
              activityId: this.data.activityId,
              projectId: this.data.projectId,
            });
          }

          this.isInitialized = true;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading data:', error);
          this.isInitialized = true;
          this.cdr.detectChanges();
        },
        complete: () => {
          this.spinner.hide();
          this.cdr.detectChanges();
        }
      });
  }


  updateConversionRate() {
    if (this.form?.get('currencyId')?.value) {

      this.currencyList.forEach((element: any) => {

        if (this.form?.get('currencyId')?.value == element.id)
          this.converssionRateValue = element.conversionRate
      });
      this.form.patchValue({
        currencyRate: this.converssionRateValue
      });
    }
  }


  // selecting currency
  onCurrencySelected(selected: any): void {
    this.form.patchValue({
      currencyId: selected.id,
    });
    this.updateConversionRate();
  }

  // controlling currency
  get currencyIdControl(): FormControl {
    return this.form.get('currencyId') as FormControl;
  }

  createForm() {
    this.form = this.fb.group({
      "nameAr": [this.data?.nameAr || null],
      "name": [this.data?.name || null],
      "responsibleName": [this.data?.responsibleName || null],
      "address": [this.data?.address || null],

      "code": [this.data?.code || '1'],
      "paymentTypeId": [this.data?.paymentTypeId || null],
      "clientType": [this.data?.clientType || '3fa85f64-5717-4562-b3fc-2c963f66afa6'],
      "isDebit": [this.data?.isDebit || false],
      "currencyId": [this.data?.currencyId || null],
      "currencyRate": [this.data?.currencyRate || null],
      "brnchId": [this.data?.brnchId || null],
      "invoiceDate": [this.data?.invoiceDate || null],
      "delegateId": [this.data?.delegateId || null],
      "terms": [this.data?.terms || null],
      "notes": [this.data?.notes || null],
      "reciever": [this.data?.reciever || null],
      "phoneNumber": [this.data?.phoneNumber || null],
      "clientId": [this.data?.clientId || null],
      "totalPrice": [this.data?.totalPrice || null],
      "descount": [this.data?.descount || null],
      "taxPer": [this.data?.taxPer || null],
      "taxAmount": [this.data?.taxAmount || null],
      "finalAmount": [this.data?.finalAmount || null],
      "debitAccountId": [this.data?.debitAccountId || null],
      "creditAccountId": [this.data?.creditAccountId || null],
      "taxAccountAccountId": [this.data?.taxAccountAccountId || null],
      "financialReportHeaderId": [this.data?.financialReportHeaderId || null],
      "costCenterId": [this.data?.costCenterId || null],
      "activityId": [this.data?.activityId || null],
      "projectId": [this.data?.projectId || null],

    })

    this.form.controls['nameAr'].disable();
    this.form.controls['name'].disable();
    this.form.controls['responsibleName'].disable();
    this.form.controls['address'].disable();
    this.form.controls['code'].disable();
    this.form.controls['totalPrice'].disable();
    this.form.controls['descount'].disable();
    this.form.controls['taxAmount'].disable();
    this.form.controls['finalAmount'].disable();



    if (this.data !== null) {
      this.tempform = this.fb.group({
        "tempNameAr": null,
        "tempName": null,
        "salesAccountId": null,
        "salesAccountNumber": null,
        "arabicDesc": null,
        "latinDesc": null,
        "insuranceDate": null,
        "endDate": null,
        "cost": 0,
        "quantity": 1,
        "totalPrice": 0,
        "isPercintage": true,
        "descountAmount": 0,
        "priceAfterDescount": 0,
        "taxPer": true,
        "taxAmount": 0,
        "finalPrice": 0,
        "ratio": 0,

      })
    }
    this.tempform.controls['totalPrice'].disable();
    this.tempform.controls['priceAfterDescount'].disable();
    this.tempform.controls['finalPrice'].disable();
    this.tempform.controls['tempNameAr'].disable();
    this.tempform.controls['tempName'].disable();

  }

  updateTempTotalPrice() {
    this.tempform.patchValue({
      totalPrice: this.tempform.controls['quantity'].value * this.tempform.controls['cost'].value,
    });
    this.tempform.patchValue({
      priceAfterDescount: this.tempform.controls['totalPrice'].value - this.tempform.controls['descountAmount'].value
    });
    if (this.tempform.controls['isPercintage'].value) {
      let tax = parseInt(this.tempform.controls['priceAfterDescount'].value) * (parseInt(this.tempform.controls['taxAmount'].value) / 100)
      this.tempform.patchValue({
        finalPrice: parseInt(this.tempform.controls['priceAfterDescount'].value) + tax
      });
    } else {
      this.tempform.patchValue({
        finalPrice: parseInt(this.tempform.controls['priceAfterDescount'].value) + parseInt(this.tempform.controls['taxAmount'].value)
      });
    }
    this.cdr.detectChanges();
  }

  addRow() {
    let data = this.tempform.getRawValue();
    let totalPrice = 0;
    let discount = 0;
    let taxAmount = 0;
    let finalAmount = 0;

    if (data) {
      this.invoiceLines.push(data);
      this.invoiceLines = [...this.invoiceLines];
    }

    if (this.invoiceLines.length > 0) {
      for (let i = 0; i < this.invoiceLines.length; i++) {
        totalPrice = totalPrice + this.invoiceLines[i].finalPrice;
        discount = discount + this.invoiceLines[i].descountAmount;
        taxAmount = taxAmount + this.invoiceLines[i].taxAmount;
        finalAmount = finalAmount + this.invoiceLines[i].priceAfterDescount;
      }
      this.form.patchValue({
        totalPrice: totalPrice,
        descount: discount,
        taxAmount: taxAmount,
        finalAmount: finalAmount
      });
    }
    this.cdr.detectChanges();
  }


  onDelegateSelected(delegate: any) {
    this.form.patchValue({
      delegateId: delegate.id,
    });
  }

  get delegateIdControl(): FormControl {
    return this.form.get('delegateId') as FormControl;
  }


  onClientSelected(client: any) {
    this.form.patchValue({
      clientId: client.id,
      nameAr: client.nameAr,
      name: client.name,
      responsibleName: client.responsibleName,
      address: client.address,
    });
  }

  get clientIdControl(): FormControl {
    return this.form.get('clientId') as FormControl;
  }


  onPaymentSelected(payment: any) {
    this.form.patchValue({
      paymentTypeId: payment.id,
    });
  }

  get paymentTypeIdControl(): FormControl {
    return this.form.get('paymentTypeId') as FormControl;
  }


  onSalesAccountSelected(salesAccount: any) {
    this.tempform.patchValue({
      salesAccountId: salesAccount.id,
      salesAccountNumber: salesAccount.accountNumber,
      tempNameAr: salesAccount.nameAr,
      tempName: salesAccount.name,
    });
  }

  get salesAccountIdControl(): FormControl {
    return this.tempform.get('salesAccountId') as FormControl;
  }

  get DebitAccountIdControl(): FormControl {
    return this.form.get('debitAccountId') as FormControl;
  }

  onDebitAccountSelected(selectedAccount: any) {
    this.form.patchValue({
      debitAccountId: selectedAccount.id,
    });
  }

  get creditAccountIdControl(): FormControl {
    return this.form.get('creditAccountId') as FormControl;
  }

  onCreditAccountSelected(selectedAccount: any) {
    this.form.patchValue({
      creditAccountId: selectedAccount.id,
    });
  }

  get taxAccountAccountIdControl(): FormControl {
    return this.form.get('taxAccountAccountId') as FormControl;
  }

  onTaxAccountSelected(selectedAccount: any) {
    this.form.patchValue({
      taxAccountAccountId: selectedAccount.id,
    });
  }

  get costCenterIdControl(): FormControl {
    return this.form.get('costCenterId') as FormControl;
  }

  onCostCenterSelected(selectedAccount: any) {
    this.form.patchValue({
      costCenterId: selectedAccount.id,
    });
  }

  get projectIdControl(): FormControl {
    return this.form.get('projectId') as FormControl;
  }

  onProjectSelected(selectedAccount: any) {
    this.form.patchValue({
      projectId: selectedAccount.id,
    });
  }

  get activityIdControl(): FormControl {
    return this.form.get('activityId') as FormControl;
  }

  onActivitySelected(selectedAccount: any) {
    this.form.patchValue({
      activityId: selectedAccount.id,
    });
  }

  onSubmit() {
    let data = this.form.getRawValue();
    data.invoiceLines = this.invoiceLines;
    data.branchId = this.branchId;
    this.spinner.show();
    this.invoice.addInvoice(data).subscribe({
      next: (res) => {
        if (res.message == "Invoice created") {
          this.translate.get('TOAST.INVOICE_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          })
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }
        this.router.navigate(['/invoices']);
      },
      error: (error) => {
        console.error('Error submitting invoice:', error);
      },
      complete: () => {
        this.spinner.hide();
        this.cdr.detectChanges();
      }
    });
  }
  edit() {
    let data = this.form.getRawValue();
    this.spinner.show();
    this.invoice.editInvoicet(this.id, data).subscribe({
      next: (res) => {
        if (res.message == "Invoice updated") {
          this.translate.get('TOAST.INVOICE_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          })
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }
        this.router.navigate(['/invoices']);
      },
      error: (error) => {
        console.error('Error editing invoice:', error);
      },
      complete: () => {
        this.spinner.hide();
        this.cdr.detectChanges();
      }
    });
  }

  protected readonly String = String;
}
