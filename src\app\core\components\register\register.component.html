<div class="container">
  <div class="pt-5"><app-lang-dropdown></app-lang-dropdown></div>
  <div class=" ">
    <div class=" row justify-content-center">
      <div class="erp-card  p-5 col-md-7 erp-shadow-center">

        <form [formGroup]="form" (ngSubmit)="register()" *ngIf="!registrationDone">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.COMPANYNAME'|translate}}</label>
                <input type="text" id="CompanyName" class="form-control" formControlName="CompanyName"
                  name="CompanyName">
                <ng-container *ngIf="form && form?.get('CompanyName')?.dirty">
                  <p class="text-danger text-error" *ngIf="form && form?.get('CompanyName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                  </p>
                </ng-container>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.FAX'|translate}}</label>
                <input type="text" id="FAX" class="form-control" formControlName="fax" name="FAX">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.HOTLINE'|translate}}</label>
                <input type="text" id="hotLine" class="form-control" formControlName="hotLine" name="hotLine">

              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.OFFICAILEMAIL'|translate}}</label>
                <input type="text" id="officialEmail" class="form-control" formControlName="officialEmail"
                  name="officialEmail">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.HOTLINE'|translate}}</label>
                <input type="text" id="hotLine" class="form-control" formControlName="hotLine" name="hotLine">

              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.WEBSITE'|translate}}</label>
                <input type="text" id="website" class="form-control" formControlName="website" name="website">
              </div>
            </div>
          </div>
          <div class="form-group">
            <label>{{'REGISTERFORM.ADDRESS'|translate}}</label>
            <input type="text" id="address" class="form-control" formControlName="address" name="address">
          </div>
          <div class="form-group">
            <label>{{'REGISTERFORM.DESCRIPTION'|translate}}</label>
            <input type="text" id="description" class="form-control" formControlName="description" name="description">
          </div>


          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.CONTACTNAME'|translate}}</label>
                <input type="text" id="contactName" class="form-control" formControlName="contactName"
                  name="contactName">

              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.REGISTRATIONNUMBER'|translate}}</label>
                <input type="text" id="registrationNumber" class="form-control" formControlName="registrationNumber"
                  name="registrationNumber">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.TIMEZONE'|translate}}</label>
                <input type="text" id="timeZone" class="form-control" formControlName="timeZone" name="timeZone">

              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>{{'REGISTERFORM.LANGUAGE'|translate}}</label>
                <input type="text" id="language" class="form-control" formControlName="language" name="language">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="form-group">
              <label>{{'SIDEBAR.BRANCHES'|translate}}</label>
              <input type="number" id="BranchesCount" class="form-control" formControlName="BranchesCount" name="BranchesCount">
            </div>
          </div>

          <button type="submit" class="btn btn-primary btn-block mt-4">{{ 'REGISTERFORM.REGISTER' |translate}}</button>



        </form>
        <div *ngIf="registrationDone">
          <div class="alert alert-primary text-primary" role="alert">
            <p> {{"REGISTERFORM.REGISTRATIONDONE"|translate}}:{{userInfo.superAdminUserName}}</p>
            <p> {{"REGISTERFORM.PASSWORD"|translate}}:{{userInfo.superAdminPassword}}</p>
          </div>
          <div class="alert alert-info text-success" role="alert">
            {{"REGISTERFORM.THANKYOUFORSIGNINGUP"|translate}}
          </div>

        </div>
        <p class="text-center mt-3">{{ 'REGISTERFORM.DOYOUHAVEANACCOUNT' |translate}} <a class="text-primary"
            routerLink="/#/login">{{
            'REGISTERFORM.LOGIN'
            |translate}}</a></p>
      </div>


    </div>
  </div>
</div>
