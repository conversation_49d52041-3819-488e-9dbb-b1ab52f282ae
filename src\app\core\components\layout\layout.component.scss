:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100%;
}

.menu-sidebar {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  overflow: hidden;
}

:lang(en) .menu-sidebar {
  border-radius: 0 13px 13px 0;
}

:lang(ar) .menu-sidebar {
  border-radius: 13px 0px 0px 13px;
}

.header-trigger {
  height: 64px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s, padding 0s;
}

.trigger:hover {
  color: #1890ff;
}

.sidebar-logo {
  position: relative;
  height: 64px;
  text-align: center;
  overflow: hidden;
  line-height: 64px;
  background: var(--color-primary);
  transition: all 0.3s;

}

:lang(ar) .sidebar-logo {
  border-radius: 13px 0 0 13px;
}

:lang(en) .sidebar-logo {
  border-radius: 0 13px 13px 0px;
}

.sidebar-logo img {
  display: inline-block;
  vertical-align: middle;
}

.sidebar-logo h1 {
  display: inline-block;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  vertical-align: middle;
}

:lang(en) .ant-menu-item .anticon+span {
  margin-left: 10px;
}

:lang(ar) .ant-menu-item .anticon+span {
  margin-right: 10px;
}



nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.ant-layout.ant-layout-has-sider>.ant-layout {
  width: 100%;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

nz-content {
  margin: 24px;
}

.inner-content {
  padding: 24px;
  background: #fff;
  height: 100%;
}

.white-divider {
  color: #fff;
  height: 2rem;
  width: 100%;
  background-color: #fff;
}

.sider-container {
  height: 100%;
  padding-top: 2rem;
  background-color: var(--color-primary);

  .menu {
    background-color: var(--color-primary);
    color: #fff;

  }

  :lang(en) .menu-item {
    padding-left: 1rem;
    background-color: var(--color-primary);

  }

  :lang(ar) .menu-item {
    padding-right: 1rem;
    background-color: var(--color-primary);
  }
}

.ant-menu-item-selected {
  background-color: #1D4CBA !important;
  color: var(--color-light-grey) !important;
  width: 100%;
  border-radius: 0 13px 13px 0
}

:lang(en) .ant-menu-item-selected {

  border-radius: 0 13px 13px 0;
}

:lang(ar) .ant-menu-item-selected {

  border-radius: 13px 0 0 13px;
}

:lang(en) .sider-container {
  border-radius: 0 13px 13px 0;
}

:lang(ar) .sider-container {
  border-radius: 13px 0 0 13px;
}

.ant-menu-inline .ant-menu-item {
  // margin-top: 0;
  // margin-bottom: 0;
  // padding-right: 0 !important;
  // padding-left: 0 !important;
}


.ant-menu-item {
  transition: none !important;
}



.ant-menu-inline .ant-menu-item::after {
  border-right: 0;
}

.ant-menu-item:active,
.ant-menu-submenu-title:active {
  background-color: transparent;
}

.ant-menu-inline,
.ant-menu-vertical,
.ant-menu-vertical-left {
  border-right: 1px solid var(--color-primary) !important;
}

.p-navbar {

  &:lang(en){
    padding: 1rem 4rem 0.5rem 3rem;
  }
  &:lang(ar){
    padding: 1rem 3rem 0.5rem 4rem;
  }
}

.submenu-title {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 0;
  direction: ltr !important; // icons on left, text on right always

  :lang(ar) & {
    flex-direction: row-reverse;
  }
}

:lang(ar) .submenu-title span:last-child {
   padding: 0;
  direction: rtl; // keep Arabic text natural
  text-align: start;
}

