import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {IBaseResponse} from "../../../core/models";
import {AnalyticalReport, EstimatedBudgetReport} from "../../../core/models/reports";

@Injectable({
  providedIn: 'root'
})
export class ReportsService {

  constructor(private httpClient: HttpClient) { }

  // Getting Analytical Reports
  getAnalyticalReports(data: AnalyticalReport) {
    const options = {
      accountIds: data.accountIds || [],
      activityIds: data.activityIds || [],
      costCenterIds: data.costCenterIds || [],
      projectIds: data.projectIds || [],
      brancheIds: data.brancheIds || [],
      startDate: data.startDate || '',
      endDate: data.endDate|| ''
    };
    return this.httpClient.post<IBaseResponse[]>('/Reports/AnalyticalReport', options);
  }

  // Getting Estimated Budget Report
  getEstimatedBudgetReport(data: EstimatedBudgetReport) {
    const options = {
        startDate: data.startDate || '',
        endDate: data.endDate|| ''
    };
    return this.httpClient.post<IBaseResponse[]>('/Reports/EstimatedBudjetReport', options);
  }

  // Getting All Final Reports
  getAllFinalReports(data: any) {
    const options = {
      accountIds: data.accountIds || [],
      activityIds: data.activityIds || [],
      costCenterIds: data.costCenterIds || [],
      projectIds: data.projectIds || [],
      brancheIds: data.brancheIds || [],
      startDate: data.startDate || '',
      endDate: data.endDate|| ''
    };
    return this.httpClient.post<IBaseResponse[]>('/Reports/AllFinalReports/', options);
  }
}
