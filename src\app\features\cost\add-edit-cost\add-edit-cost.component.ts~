import { Component, inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { NzI18nService } from 'ng-zorro-antd/i18n';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import moment from 'moment-hijri';
import { CostService } from '../services/cost.service';
import { ChangeDetectorRef } from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";

@Component({
  selector: 'app-add-edit-cost',
  templateUrl: './add-edit-cost.component.html',
  styleUrl: './add-edit-cost.component.scss'
})
export class AddEditCostComponent implements OnInit {
  form!: FormGroup;
  data: any;
  currencyList: any;

  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  isUpdate: boolean = false;
  ischild: any;
  costCenterId: any;
  childLevel: any;
  converssionRateValue: any;

  constructor(
    private modal: NzModalRef,
    private fb: FormBuilder,
    private cost: CostService,
    private i18n: NzI18nService,
    private cdr: ChangeDetectorRef,
    private translate: TranslateService,
    private messageService: MessageService
  ) {
    // Initialize form in constructor
    this.initializeForm();
  }

  ngOnInit() {
    // Handle any additional initialization if needed
    this.dateHandler();
    // Trigger change detection
    this.cdr.detectChanges();
  }



  private initializeForm() {
    this.ischild = this.nzModalData?.child;
    if (this.ischild) {
      this.costCenterId = this.nzModalData?.id;
      this.childLevel = this.nzModalData?.originalData?.level + 1;
    }

    if (!this.ischild) {
      this.data = this.nzModalData?.originalData;
      if (this.data) {
        this.isUpdate = true;
      }
    }

    let level = this.data?.level || this.childLevel || null
    const status = this.data?.status == 0 ? '0' : 1;

    this.form = this.fb.group({
      shortName: [this.data?.shortName || '', Validators.required],
      costCenterName: [this.data?.costCenterName || '', Validators.required],
      cost_Center_Latin_Name: [this.data?.cost_Center_Latin_Name || '', Validators.required],
      englishName: [this.data?.englishName || ''],
      cost_Center_Opening_Date: [this.data?.cost_Center_Opening_Date || '', Validators.required],
      creditLimit: [this.data?.creditLimit || ''],
      openingBalance: [this.data?.openingBalance || ''],
      openingBalanceDate: [this.data?.openingBalanceDate || ''],
      estimatedBudget: [this.data?.estimatedBudget || '', [Validators.required, Validators.min(0)]],
      notes: [this.data?.notes || ''],
      email: [this.data?.email || '', [Validators.email]],
      status: [status || ''],
      level: [level],
      isDetailed: [this.data?.isDetailed || false],
      costCenterId: [this.costCenterId || this.data?.costCenterId || null],
      accountOpeningDatehijri: [''],
      costCenterNumber: [this.data?.costCenterNumber],
      parentCostCenterNumber: [this.nzModalData?.originalData?.costCenterNumber],
      planInCome: [this.data?.planInCome],
      planOutCome: [this.data?.planOutCome]
    });
    this.dateHandler()

    // Disable controls after form initialization
    this.form.get('parentCostCenterNumber')?.disable();
    this.form.get('costCenterNumber')?.disable();
  }

  dateHandler() {
    if (this.form?.get('cost_Center_Opening_Date')?.value) {
      let date = moment(this.form?.get('cost_Center_Opening_Date')?.value).format('iD iMMMM iYYYY');
      console.log(date)
      this.form.patchValue({
        accountOpeningDatehijri: date
      });
    }
  }

  destroyModal(): void {
    this.modal.destroy();
  }

  onRadioChange(value: boolean) {
    this.form.get('isDetailed')?.setValue(value);
    this.cdr.detectChanges();
  }

  onSubmit() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      this.cost.addCost(data).subscribe(res => {
        if (res.message == "Cost center created") {
          this.translate.get('TOAST.COST_CENTER_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      });
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  update() {
    if (this.form.valid) {
      let dataForm = this.form.getRawValue();
      this.cost.updateCost(this.data.id, dataForm).subscribe(res => {
        if (res.message == "Cost center updated") {
          this.translate.get('TOAST.COST_CENTER_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      });
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
