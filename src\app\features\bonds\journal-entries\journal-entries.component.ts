import { Component } from '@angular/core';
import {BondsService} from "../services/bonds.service";
import {TranslateService} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {formatDate} from "@angular/common";

@Component({
  selector: 'app-journal-entries',
  templateUrl: './journal-entries.component.html',
  styleUrl: './journal-entries.component.scss'
})
export class JournalEntriesComponent {
  type:string='1F1E5B71-5431-4BA5-A25F-50D7C93E1ED4'
  bondsList: any;
  lang: string
  searchValue: string = '';
  pageSize: number = 5;
  flattenedBonds: any[] = []
  currentDate: Date = new Date()
  currentRef: string

  constructor(private bonds: BondsService, private translate: TranslateService, private router: Router) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getBonds()
    })
  }


  ngOnInit(): void {
    this.getBonds();
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  getBonds() {
    this.bonds.getReports(this.type).subscribe(res => {
      this.bondsList = res.data
      this.bondsList.forEach((e:any) => {
        e.expand=false
      });
      this.flattenedBonds = this.flattenBondsData(res.data)
    })
  }

  flattenBondsData(bondsList: any[]): any[] {
    const flattenedData: any[] = [];

    bondsList.forEach((bond: any) => {
      // Add parent row
      flattenedData.push({
        isParent: true,
        date: formatDate(bond.docDate, 'dd/MM/yyyy', 'en'),
        amount: bond.amount,
        branchName: bond.branchName || '-'
      });

      // Add child rows
      bond.financialReportLines.forEach((line: any) => {
        flattenedData.push({
          isParent: false,
          date: line.accountName,
          amount: line.centerName,
          branchName: line.projectName
        });
      });
    });

    return flattenedData;
  }

  onEditBond(id: string) {
    this.bonds.changeEntries(id).subscribe(res => {
      this.getBonds()
    })
  }

  addJournalEntry() {
    this.router.navigate(['/add-journal-entries']);
  }

}
