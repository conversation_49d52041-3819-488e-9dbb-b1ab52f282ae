<div *nzModalTitle> <span *ngIf="!isUpdate">{{'IDENTITY.ADDIDENTITY'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="form-group">
            <label>{{'IDENTITY.IDENTITYCODE'|translate}}</label>
            <input type="text" id="identity_Code" class="form-control" formControlName="identity_Code"
                name="identity_Code">
            <ng-container *ngIf="form && form?.get('identity_Code')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('identity_Code')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'IDENTITY.IDENTITYNAMEAR'|translate}}</label>
            <input type="text" id="identity_Name_Latin" class="form-control" formControlName="identity_Name_Latin"
                name="identity_Name_Latin">
            <ng-container *ngIf="form && form?.get('identity_Name_Latin')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('identity_Name_Latin')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'IDENTITY.IDENTITYNAMEEN'|translate}}</label>
            <input type="text" id="identity_Name" class="form-control" formControlName="identity_Name"
                name="identity_Name">
            <ng-container *ngIf="form && form?.get('identity_Name')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('identity_Name')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>

    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="addorEditCurrency()"> <span
                *ngIf="!isUpdate">{{'IDENTITY.ADDIDENTITY'|translate}}</span></button>
    </div>
</form>