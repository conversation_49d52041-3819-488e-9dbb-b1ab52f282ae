import { Component, OnInit } from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import { AuthService } from '../../services/auth/auth.service';
import { Router } from '@angular/router';
import { UserService } from '../../services/user/user.service';
import {branch} from "../../models";
import {BranchService} from "../../../features/branches/branch.service";
import {TranslateModule} from "@ngx-translate/core";
import {ReusableDropdownComponent} from "../../../shared/components/reusable-dropdown/reusable-dropdown.component";
import {SharedModule} from "../../../shared/shared.module";
import {NgIf} from "@angular/common";

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrl: './register.component.scss',
  standalone: true,
  imports: [
    TranslateModule,
    SharedModule,
    NgIf
  ]
})
export class RegisterComponent implements OnInit {
  registrationDone: boolean = false;
  form: FormGroup;
  userInfo: any;
  constructor(private user: UserService, private router: Router, private fb: FormBuilder) {

  }
  ngOnInit(): void {
    this.creatFormRegister();
  }

  creatFormRegister() {
    this.form = this.fb.group({
      CompanyName: ['', [Validators.required, Validators.minLength(3)]],
      fax: [''],
      hotLine: [''],
      officialEmail: ['', [Validators.email]],
      website: ['', [Validators.pattern(/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/)]],
      officialPhoneNumber: [''],
      address: [''],
      description: [''],
      contactName: [''],
      registrationNumber: [''],
      timeZone: [''],
      language: [''],
      BranchesCount: ''
    });
  }
  createFormData() {
    let newData = new FormData();
    Object.entries(this.form.getRawValue()).forEach(([key, value]: any) => {
      newData.append(key, value);
    });
    return newData;
  }
  register() {

    if (this.form.valid) {
      let data = this.createFormData();
      this.user.registerUser(data).subscribe(res => {
        this.registrationDone = true;
        this.userInfo = res.data;
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

}
