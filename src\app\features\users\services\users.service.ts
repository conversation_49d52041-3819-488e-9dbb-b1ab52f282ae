import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse, Users, Permissions } from '../../../core/models';
@Injectable({
  providedIn: 'root'
})
export class UsersService {

  constructor(private httpClient: HttpClient) { }
  getUsers() {
    return this.httpClient.get<IBaseResponse<Users[]>>(`/Users/<USER>
  }
  getPermissions() {
    return this.httpClient.get<IBaseResponse<Permissions>>(`/Modules/TenantModulesPermissions`);
  }
  getUserPermissions(id: string) {
    return this.httpClient.get<IBaseResponse>(`/Permissions/UserPermissions?userId=${id}`);
  }
  updateUserPermissions(data: any) {
    return this.httpClient.post<any>(`/Permissions/ModifyUserPermissions`, data);
  }
  addUser(data: any) {
    return this.httpClient.post<any>(`/Users/<USER>
  }
  
  updatePassword(data:any){
    return this.httpClient.post<any>(`/Users/<USER>
  }
  updateUsersData(data:any){
    return this.httpClient.post<any>(`/UsersProfiles/UpdateUserDetails`, data)
   
  }
}
