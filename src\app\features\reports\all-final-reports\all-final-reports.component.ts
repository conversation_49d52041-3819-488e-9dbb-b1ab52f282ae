import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { BondsService } from "../../bonds/services/bonds.service";
import { TranslateService } from "@ngx-translate/core";
import { ReportsService } from "../services/reports.service";
import { dateRangeValidator } from "../../../shared/utils/validators";
import {Account, Report} from "../../../core/models/reports";
import {ActivityService} from "../../activity/services/activity.service";
import {CostService} from "../../cost/services/cost.service";
import {flattenNodes} from "../../../../utils/functions";
import {ProjectsService} from "../../projects/services/projects.service";
import {BranchService} from "../../branches/branch.service";


@Component({
  selector: 'app-all-final-reports',
  templateUrl: './all-final-reports.component.html',
  styleUrls: ['./all-final-reports.component.scss']
})
export class AllFinalReportsComponent implements OnInit {
  form: FormGroup;
  lang: string = "en";
  accountsList: Account[] = [];
  activityList: any[] = [];
  costList: any[] = [];
  projectList: any[] = [];
  branchesList: any[] = [];
  breadcrumbItems: MenuItem[] = [];
  reportList: any[] = []
  currentDate: Date = new Date()
  currentRef: string
  groupedReportList: any[]=[]

  constructor(
    private fb: FormBuilder,
    private bonds: BondsService,
    private translate: TranslateService,
    private reports: ReportsService,
    private activity: ActivityService,
    private costCenter: CostService,
    private projects: ProjectsService,
    private branch: BranchService
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.translate
        .get(['SIDEBAR.REPORTS', 'REPORTS.ALLFINALREPORTS'])
        .subscribe((translations) => {
          this.breadcrumbItems = [
            { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
            { label: translations['REPORTS.ALLFINALREPORTS'] },
          ];
        });
    });
  }

  ngOnInit() {
    this.fetchingAccounts();
    this.fetchingActivities();
    this.fetchingCostCenter()
    this.fetchingProjects();
    this.fetchingBranches()
    this.createForm();
    this.translate
      .get(['SIDEBAR.REPORTS', 'REPORTS.ALLFINALREPORTS'])
      .subscribe((translations) => {
        this.breadcrumbItems = [
          { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
          { label: translations['REPORTS.ALLFINALREPORTS'] },
        ];
      });

    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  createForm() {
    this.form = this.fb.group({
      accountIds: [[]],
      activityIds: [[]],
      costCenterIds: [[]],
      projectIds: [[]],
      brancheIds: [[]],
      startDate: [''],
      endDate: ['']
    }, { validators: dateRangeValidator() });
  }

  fetchingAccounts() {
    this.bonds.getAccounts().subscribe(
      (res) => {
        this.accountsList = res.data.map((account: Account) => ({
          ...account,
          label: `${account.accountName} - ${account.accountNumber}`
        }));
      },
      (error) => {
        console.error('Error fetching accounts:', error);
      }
    );
  }

  fetchingActivities() {
    this.activity.getActivities().subscribe(
      (res) => {
        this.activityList = res.data.map((activity: any) => ({
          ...activity,
          label: `${activity.activityName} - ${activity.activityNumber}`
        }))
      }
    );
  }

  fetchingCostCenter() {
    this.costCenter.getCost().subscribe(
      (res) => {
        this.costList = flattenNodes(res.data, 'childCostCenter', 'isDetailed', true).map((cost: any) => ({
          ...cost,
          label: `${cost.costCenterName} - ${cost.costCenterNumber}`
        }))
      }
    )
  }

  fetchingProjects() {
    this.projects.getProjects().subscribe((res) => {
      this.projectList = flattenNodes(res.data, "childProjects", "isDetailed" , true).map((project : any) => ({
        ...project,
        label: `${project.projectName} - ${project.projectNumber}`
      }))
    })
  }

  fetchingBranches() {
    this.branch.getBranches().subscribe((res) => {
      this.branchesList = res.data.map((branch : any) => ({
        ...branch,
        label: `${branch.branchName} - ${branch.branchNumber}`
      }))
    })
  }

  onSubmit() {
    const selectedAccountIds = this.form.value.accountIds.map((account: Account) => account.id);
    const selectedActivityIds = this.form.value.activityIds.map((activity: any) => activity.id);

    const formValue = {
      ...this.form.value,
      accountIds: selectedAccountIds,
      activityIds: selectedActivityIds,
      costCenterIds: this.form.value.costCenterIds.map((cost: any) => cost.id),
      projectIds: this.form.value.projectIds.map((project: any) => project.id),
      brancheIds: this.form.value.brancheIds.map((branch: any) => branch.id),
    };

    this.reports.getAllFinalReports(formValue).subscribe(
      (res: any) => {
        this.reportList = res.data;
        this.groupReportByAccount();
      },
      (error) => {
        console.error('Error fetching reports:', error);
      }
    );
  }


  getSelectedAccountNames(): string {
    if (!this.form.value.accountIds || this.form.value.accountIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا  شيء محدد";
    }
    return this.form.value.accountIds
      .map((acc: any) => acc.label)
      .join(', ');
  }

  getSelectedActivityNames(): string {
    if (!this.form.value.activityIds || this.form.value.activityIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا  شيء محدد";
    }
    return this.form.value.activityIds
      .map((activity: any) => activity.label)
      .join(', ');
  }
  getSelectedCostNames(): string {
    if (!this.form.value.costCenterIds || this.form.value.costCenterIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا  شيء محدد";
    }
    return this.form.value.costCenterIds
      .map((cost: any) => cost.label)
      .join(', ');
  }

  getSelectedProjectNames(): string {
    if (!this.form.value.projectIds || this.form.value.projectIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا  شيء محدد";
    }
    return this.form.value.projectIds
      .map((project: any) => project.label)
      .join(', ');
  }

  getSelectedBranchNames(): string {
    if (!this.form.value.bracheIds || this.form.value.bracheIds.length === 0) {
      return this.lang == "en" ? 'Nothing Selected' : "لا شيء محدد";
    }
    return this.form.value.bracheIds
      .map((branch: any) => branch.label)
      .join(', ');
  }

  groupReportByAccount() {
    if (!this.reportList || this.reportList.length === 0) {
      console.warn("No reports available to group.");
      return;
    }

    const grouped = this.reportList.reduce((acc: any, record: any, index: number, array: any[]) => {
      const accountKey = record.account.number; // Ensure correct property access

      if (!acc[accountKey]) {
        acc[accountKey] = {
          parentAccountNumber: record.accountNumber,
          accountNumber: record.account.number,
          accountNameEn: record.account.nameEn,
          accountNameAr: record.account.nameAr,
          pairedRecords: [],
          documentNumber: record.account.documentNumber,
          documentTypeName: record.account.documentTypeName
        };
      }

      if (index % 2 === 0 && array[index + 1]) {
        acc[accountKey].pairedRecords.push({
          accountNumber: record.account.number,
          accountNameEn: record.account.nameEn,
          accountNameAr: record.account.nameAr,
          totalDebit: record.totalDebit,
          totalCredit: array[index + 1]?.totalCredit || 0,
          movementDate: array[index + 1]?.movementDate || null,
          descriptionAr: record.descriptionAr,
          descriptionEn: record.descriptionEn,
          refNumber: record.refNumber,
          documentNumber: record.number
        });
      }

      return acc;
    }, {});

    this.groupedReportList = Object.values(grouped);
  }

}
