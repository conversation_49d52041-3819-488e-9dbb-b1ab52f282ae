import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IdentitiesComponent } from './identities/identities.component';
import { AddEditIdentityComponent } from './add-edit-identity/add-edit-identity.component';
import { SharedModule } from '../../shared/shared.module';
import { IdentityRoutingModule } from './identity-routing.module';



@NgModule({
  declarations: [
    IdentitiesComponent,
    AddEditIdentityComponent,
    
  ],
  imports: [
    CommonModule,
    SharedModule,
    IdentityRoutingModule
  ]
})
export class IdentityModule { }
