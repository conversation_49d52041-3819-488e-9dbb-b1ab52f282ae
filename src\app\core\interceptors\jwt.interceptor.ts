import {Injectable} from '@angular/core';
import {HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest} from '@angular/common/http';
import {Observable} from 'rxjs';
import {AuthService} from '../services/auth/auth.service';
import {tap} from 'rxjs/operators';
import {ClientHeaders} from '../enums/client-headers.enum';


@Injectable()
export class JWTInterceptor implements HttpInterceptor {


  constructor(private auth: AuthService,) {
  }

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {

    if (request.headers.has(ClientHeaders.external) || request.headers.has(ClientHeaders.ignoreAuthorization)) {
      return next.handle(request);
    }

    if (this.auth.isAuthenticated()) {
      const token = this.auth.token;
      request = request.clone({
        setHeaders: {
          authorization: 'Bearer '+ this.auth.token,
        },
      });
    }

    return next.handle(request).pipe(tap(() => {
    }, this.handleExpiredSessions.bind(this)));
  }


  handleExpiredSessions(err: HttpErrorResponse) {
    if (err.status === 401) {

    } else if (err.status === 419){
      
    //   this.auth.logout();
    }
  }
}
