import { Component, OnInit } from '@angular/core';
import { User } from '../../core/models';
import { UserService } from '../../core/services/user/user.service';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent implements OnInit {
  userInfo: User;
  constructor(private user: UserService) {

  }
  ngOnInit(): void {
    this.getUserDetails();
  }
  getUserDetails() {
    this.user.getUserInfo().subscribe(res => {
      this.userInfo = res.data;
    })
  }

}
