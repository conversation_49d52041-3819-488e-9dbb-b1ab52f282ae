<div class="erp-card erp-shadow-end">
  <div class="row my-3 px-3 align-items-center">

    <!-- Search Input -->
    <div class="col-12 col-md-3 mb-2 mb-md-0">
      <input type="text" id="search" class="form-control"
             placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
             name="search" [(ngModel)]="searchValue">
    </div>

    <!-- Buttons Section -->
    <div class="col-12 col-md-9 d-flex flex-wrap justify-content-md-end gap-2 text-center">

      <!-- Delete Button -->
      <button *ngIf="selectedAccount" (click)="deleteAccount()"
              class="btn btn-danger text-white">
        {{'ACCOUNT.DELETE' | translate}}
      </button>

      <!-- Update Button -->
      <button *ngIf="selectedAccount" (click)="updateAccountModal()"
              class="btn btn-primary">
        {{'ACCOUNT.UPDATE' | translate}}
      </button>

      <!-- Add Account Button -->
      <button *ngIf="shouldShowAddButton()"
              (click)="addAccountModal()"
              class="btn btn-primary">
        + {{'ACCOUNT.ADDACCOUNT' | translate}}
      </button>

    </div>

  </div>


  <nz-tree #nzTreeComponent [nzData]="accountsList | treeFilters: {accountName: searchValue, email:searchValue, accountNumber:searchValue, account_Name_Latin:searchValue}" [nzCheckStrictly]="true"
        [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
  <ng-template #nzTreeTemplate let-node let-origin="origin">
        <div class="d-flex align-items-end">
            <input type="checkbox" [checked]="origin.checked" id={{origin.id}} name={{origin.id}} class="mb-3"
                (click)="mycheck($event,origin)">
            <nz-table #basicTable [nzData]="['']" [nzShowPagination]="false" nzTableLayout="fixed">
                <thead *ngIf="accountsList[0].id == origin.id">
                    <tr>
                        <th nzColumnKey="name">{{'ACCOUNT.ACCOUNTNUMBER'|translate}}</th>
                        <th nzColumnKey="name">{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME'|translate}}</th>
                        <th nzColumnKey="name">{{'ACCOUNT.ADDACCOUNTFORM.CURRENCY'|translate}}</th>
                        <th nzColumnKey="name">{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTOPENINGDATE'|translate}}</th>
                        <th nzColumnKey="name">{{'ACCOUNT.ADDACCOUNTFORM.COMMENTS'|translate}}</th>


                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{{origin.accountNumber|translate}}</td>
                        <td>{{origin.title|translate}}</td>
                        <td>{{origin.originalData.currencyName|translate}}</td>
                        <td><span *ngIf="!showARDate">{{origin.originalData.accountOpeningDate|date:'fullDate'}}</span>  <span *ngIf="showARDate"> {{origin.originalData.arabicAccountOpeningDate| arabicNumerals}}</span></td>
                        <td>{{origin.notes}}</td>

                    </tr>
                </tbody>
            </nz-table>
        </div>
    </ng-template>

  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightgray; padding-bottom: 10px;">
      <h2>{{ 'PDF.ACCOUNTREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr >
        <th>{{ 'ACCOUNT.ACCOUNTNUMBER' | translate }}</th>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}</th>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.CURRENCY' | translate }}</th>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTOPENINGDATE' | translate }}</th>
        <th>{{ 'ACCOUNT.LEVEL' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let account of flatAccountsList">
        <td>{{ account.accountNumber }}</td>
        <td>{{ lang == "en" ? account.account_Name_Latin : account.accountName }}</td>
        <td>{{ account.currencyName }}</td>
        <td>{{ account.accountOpeningDate | date:'yyyy-MM-dd' }}</td>
        <td>{{ account.level }}</td>
      </tr>
      </tbody>
    </table>

  </div>

    <div class="row mt-5 text-center">
        <app-export-pdf-button
          [tableElement]="pdfTable"
        >
        </app-export-pdf-button>
    </div>
</div>
