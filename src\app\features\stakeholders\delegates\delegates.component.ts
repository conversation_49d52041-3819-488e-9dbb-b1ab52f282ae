import { Component, OnInit } from '@angular/core';
import { AddEditDelegateComponent } from './add-edit-delegate/add-edit-delegate.component';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { StakeholdersService } from '../stakeholders.service';
import {MessageService} from "primeng/api";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-delegates',
  templateUrl: './delegates.component.html',
  styleUrl: './delegates.component.scss'
})
export class DelegatesComponent implements OnInit {
  delegates: any;
  flattenedDelegates: any[] = []
  currentDate: Date = new Date()
  currentRef: string

  constructor(private translate: TranslateService, private router: Router, private stakeholder: StakeholdersService, private messageService: MessageService, private branches: BranchService
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDelegates()
    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getDelegates();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;

  addDelegateModal() {
    this.router.navigate(['/add-delegate']);

  }
  getDelegates() {
    this.stakeholder.getDelegate().subscribe((res: any) => {
      this.delegates = res.data;
      this.flattenedDelegates = this.flattenDelegates(res.data);
    })
  }


  edit(data: any) {
    this.router.navigate(['/edit-delegate', data.id]);
  }
  delete(id: any) {
    this.stakeholder.deleteDelegate(id).subscribe(res => {
      if (res.message == "Delegate Deleted!") {
        this.translate.get('TOAST.DELEGATE_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getDelegates();
    })
  }


  flattenDelegates(delegates: any[]): any[] {
    let result: any[] = [];
    delegates.forEach((delegate) => {
      result.push(delegate);
    });
    return result;
  }


}
