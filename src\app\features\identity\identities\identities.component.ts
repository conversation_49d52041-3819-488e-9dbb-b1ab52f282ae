import { Component, OnInit } from '@angular/core';
import { IdentityService } from '../identity.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { AddEditIdentityComponent } from '../add-edit-identity/add-edit-identity.component';

@Component({
  selector: 'app-identities',
  templateUrl: './identities.component.html',
  styleUrl: './identities.component.scss'
})
export class IdentitiesComponent implements OnInit {
  identityList: any;
  pageSize:number=5;
  lang: string;
  searchValue:string='';
  langDirection: 'ltr' | 'rtl' = 'ltr';
  constructor(private identity: IdentityService, private modalService: NzModalService, private translate: TranslateService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'

    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDirection(this.lang)

    })
  }
  ngOnInit(): void {
    this.getIdentities()
  }
  getIdentities() {
    this.identity.getIdentity().subscribe(res => {
      this.identityList = res.data;
    })
  }
  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  addIdentity(){
    this.modalService.create({
      nzContent: AddEditIdentityComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res) {

        this.getIdentities();
      }
    })
  }
}
