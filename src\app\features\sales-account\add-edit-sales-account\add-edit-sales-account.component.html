<div *nzModalTitle><span *ngIf="isUpdate">{{'SALESACCOUNT.UPDATESALESACCOUNT'|translate}}</span> <span
        *ngIf="!isUpdate">{{'SALESACCOUNT.ADDNEWSALESACCOUNT'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'SALESACCOUNT.CODE'|translate}}</label>
                <input type="number" id="code" class="form-control" formControlName="code" name="code">
                <ng-container *ngIf="form && form?.get('code')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('code')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group d-flex align-items-center">
                <div class="mt-3">
                    <label for="isActive" class="form-label">{{'SALESACCOUNT.ISACTIVE'|translate}}</label>
                    <input type="checkbox" class=" form-check-input" formControlName="isActive" value="true">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'SALESACCOUNT.NAMEAR'|translate}}</label>
                <input type="text" id="nameAr" class="form-control" formControlName="nameAr" name="nameAr">
                <ng-container *ngIf="form && form?.get('nameAr')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('nameAr')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'SALESACCOUNT.NAME'|translate}}</label>
                <input type="text" id="name" class="form-control" formControlName="name" name="name">
                <ng-container *ngIf="form && form?.get('name')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('name')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>


        <div class="row">
            <div class="col-md-6">
              <label>{{ 'SALESACCOUNT.ACCOUNTNUMBER'|translate }}</label>
              <app-reusable-dropdown
                [options]="accountsList"
                [displayKey]="'accountName'"
                [valueKey]="'accountNumber'"
                [formControl]="accountNameControl"
                [selectedId]="form?.get('accountNumber')?.value"
                (selectedValue)="onSelectedAccount($event)">
              </app-reusable-dropdown>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'SALESACCOUNT.OPENDATE'|translate}}</label>
                    <br />
                    <nz-date-picker class="datepicker" formControlName="openDate"></nz-date-picker>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <label>{{'SALESACCOUNT.COMMENTS'|translate}}</label>
                <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                <ng-container *ngIf="form && form?.get('notes')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('notes')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>

        </div>
    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="addorEditSalesAccount()"><span
                *ngIf="isUpdate">{{'SALESACCOUNT.UPDATESALESACCOUNT'|translate}}</span> <span
                *ngIf="!isUpdate">{{'SALESACCOUNT.ADDNEWSALESACCOUNT'|translate}}</span></button>
    </div>
</form>
