import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountsComponent } from './accounts.component';
import { AccountsRoutingModule } from './accounts-routing.module';
import { AddAccountComponent } from './add-account/add-account.component';
import { SharedModule } from '../../shared/shared.module';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    AccountsComponent,
    AddAccountComponent,
    ReusableDropdownComponent
  ],
  imports: [
    CommonModule,
    AccountsRoutingModule,
    SharedModule
  ]
})
export class AccountsModule { }
