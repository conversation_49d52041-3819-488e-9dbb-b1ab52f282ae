
// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming
@use '@angular/material' as mat;
// Plus imports for other components in your app.

// Include the common styles for Angular Material. We include this here so that you only
// have to load a single css file for Angular Material in your app.
// Be sure that you only ever include this mixin once!
@include mat.core();

// Define the palettes for your theme using the Material Design palettes available in palette.scss
// (imported above). For each palette, you can optionally specify a default, lighter, and darker
// hue. Available color palettes: https://material.io/design/color/
$erp-primary: mat.define-palette(mat.$indigo-palette);
$erp-accent: mat.define-palette(mat.$pink-palette, A200, A100, A400);

// The warn palette is optional (defaults to red).
$erp-warn: mat.define-palette(mat.$red-palette);

// Create the theme object. A theme consists of configurations for individual
// theming systems such as "color" or "typography".
$erp-theme: mat.define-light-theme((
  color: (
    primary: $erp-primary,
    accent: $erp-accent,
    warn: $erp-warn,
  ),
  typography: mat.define-typography-config(),
  density: 0
));

// Include theme styles for core and each component used in your app.
// Alternatively, you can import and @include the theme mixins for each component
// that you are using.
@include mat.all-component-themes($erp-theme);

/* You can add global styles to this file, and also import other style files */
// @import"ng-zorro-antd/ng-zorro-antd.min";

@import'./scss/main.scss';

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }


/* Center the toast at the top */
.p-toast {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
}

/* Remove the close (×) icon */
.p-toast .p-toast-icon-close {
  display: none;
}

/* Modern styling for toast messages */
.p-toast .p-toast-message {
  border-radius: 12px; /* Rounded corners */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); /* Subtle shadow */
  padding: 16px 20px; /* Padding for better spacing */
  border: none; /* Remove default border */
}

/* Success toast styling */
.p-toast .p-toast-message-success {
  background-color: #4caf50; /* Green background */
  color: white; /* White text */
}

/* Error toast styling */
.p-toast .p-toast-message-error {
  background-color: #f44336; /* Red background */
  color: white; /* White text */
}

/* Add animation for toast */
.p-toast-message {
  animation: toast-fade 0.3s ease-in-out;
}

@keyframes toast-fade {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Adjust font size */
.p-toast .p-toast-message-content {
  font-size: 12px;
}

/* RTL support for toast */
[dir='rtl'] .p-toast {
  left: auto;
  right: 50%;
  transform: translateX(50%);
}

[dir='rtl'] .p-toast .p-toast-message {
  text-align: right;
}


.pdf-container {
  width: 100%;
  margin: 0;
  background-color: #ffffff;
  box-sizing: border-box;
}

/* Header styles */
.pdf-container h2 {
  font-size: 12pt; /* Increase font size to match the image */
  margin: 0 0 15px 0;
  color: #000000; /* Adjust color to match the brown tone */
  text-align: center;
  padding: 6px 16px;
  display: inline-block; /* Prevent full-width stretching */
  border-radius: 4px;
  font-weight: bold;
  box-shadow: #8989A2 0 0 10px;
}


.pdf-container div[style*="display: flex"] {
  display: flex;
  justify-content: space-between;
  margin: 10px 0;
  font-size: 10pt;
  color: #333333;
}

/* Table styles */
.pdf-container table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
  table-layout: fixed;
  border: 0.5px solid #000000;
}

.pdf-container th,
.pdf-container td {
  border: 0.5px solid #000000;
  padding: 8pt;
  text-align: center;
  font-size: 8pt;
  word-wrap: break-word;
  overflow: hidden;
}

.pdf-container th {
  background-color: #f0f0f0;
  font-weight: bold;
  color: #000000;
  text-transform: capitalize;
}

/* Row styles */
.pdf-container .parent-row {
  background-color: #e9ecef;
  font-weight: 600;
}

.pdf-container .child-row {
  background-color: #ffffff;
}

/* Header styles */
.pdf-header {
  text-align: center;
  margin-bottom: 15pt;
  padding-bottom: 8pt;
  border-bottom: 1pt solid #000000;
}

.pdf-header h1 {
  font-size: 12pt;
  font-weight: bold;
  margin: 0 0 8pt 0;
  color: #000000;
}

.pdf-header p {
  font-size: 9pt;
  margin: 0;
  color: #000000;
}

/* Table styles */
.pdf-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  table-layout: fixed; /* Add fixed layout for better control */
}

.pdf-table th,
.pdf-table td {
  border: 0.5px solid #000000;
  padding: 6pt;
  text-align: center;
  word-wrap: break-word; /* Allow text wrapping */
  overflow: hidden;
  max-width: 150px; /* Maximum column width */
}

.pdf-table th {
  background-color: #d6d6d6 !important;
  font-weight: bold;
  font-size: 9pt;
  color: #000000;
  text-transform: capitalize;
}

.pdf-table td {
  font-size: 8pt;
  color: #000000;
}

/* Row styles */
.parent-row {
  background-color: #d9d9d9 !important;
  font-weight: 600;
  font-size: 9pt;
}

.child-row {
  background-color: #f0f0f0 !important;
  font-size: 7pt;
}

/* Zebra striping */
.pdf-table tbody tr:nth-child(even) {
  background-color: #f5f5f5 !important;
}

/* Footer styles */
.pdf-footer {
  padding: 8pt;
  text-align: center;
  font-size: 7pt;
  border-top: 0.5pt solid #000000;
  background-color: #ffffff;
}

.pdf-footer p {
  margin: 2pt 0;
  color: #000000;
}

/* Print-specific styles */
@media print {
  .pdf-container {
    margin: 0;
    padding: 0;
  }

  .pdf-table {
    page-break-inside: auto;
  }

  .pdf-table tr {
    page-break-inside: avoid;
    page-break-after: auto;
  }
}

/* Utility classes */
.pdf-hidden {
  display: none;
}
.filters-section {
  color: #5a3e36;
  font-size: 16px;
}

/* Two-column grid layout */
.filters-section .grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Two equal columns */
  gap: 20px; /* Space between columns */
}

/* Section alignment and spacing */
.filters-section .section {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filters-section strong {
  color: #7b4d3a;
}

.filters-section span {
  margin-right: 5px;
  color: #333;
}

/* Individual row alignment */
.filters-section .section div {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#pdfTable {
  position: absolute;
  top: -9999px;
  left: -9999px;
}
