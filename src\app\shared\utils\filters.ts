export function filterDetailedProjects(projects: any): any[] {
  return projects
    .map((project: any) => {
      // Filter child projects recursively
      const filteredChildren = filterDetailedProjects(project.childProjects);

      // If the current project is detailed, return it
      if (project.isDetailed) {
        return {
          ...project,
          childProjects: filteredChildren,
        };
      }

      // If the current project is not detailed but has detailed children, return only the children
      if (filteredChildren.length > 0) {
        return filteredChildren;
      }

      // Otherwise, exclude it
      return null;
    })
    .filter((project: any) => project !== null) // Filter out null values
    .flat(); // Flatten the array to remove nested arrays
}


export function filterDetailedCostCenters(costCenters: any[]): any[] {
  return costCenters
    .map((costCenter) => {
      // Filter child cost centers recursively
      const filteredChildren = filterDetailedCostCenters(costCenter.childCostCenter);

      // If the current cost center is detailed, return it
      if (costCenter.isDetailed) {
        return {
          ...costCenter,
          childCostCenter: filteredChildren,
        };
      }

      // If the current cost center is not detailed but has detailed children, return only the children
      if (filteredChildren.length > 0) {
        return filteredChildren;
      }

      // Otherwise, exclude it
      return null;
    })
    .filter((costCenter) => costCenter !== null) // Filter out null values
    .flat(); // Flatten the array to remove nested arrays
}
