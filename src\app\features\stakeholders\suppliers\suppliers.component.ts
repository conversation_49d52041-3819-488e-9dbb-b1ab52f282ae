import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { StakeholdersService } from '../stakeholders.service';
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-suppliers',
  templateUrl: './suppliers.component.html',
  styleUrl: './suppliers.component.scss'
})
export class SuppliersComponent {
  suppliersList: any;
  translatedHeaders: string[] = [];
  translatedData: any[] = [];
  flattenedSuppliers: any[] = [];
  currentDate: Date = new Date()
  currentRef: string
  constructor(private router: Router, private stakeholder: StakeholdersService, private translate: TranslateService, private messageService: MessageService, private branches: BranchService
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getSuppliers()
    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getSuppliers();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;

  addSupplierModal() {
    this.router.navigate(['/add-supplier']);

  }
  getSuppliers() {
    this.stakeholder.getSupplier().subscribe((res: any) => {
      this.suppliersList = res.data;
      this.flattenedSuppliers = this.flattenSuppliers(res.data);
    })
  }

  edit(data: any) {
    this.router.navigate(['/edit-supplier', data.id]);
  }
  delete(id: any) {
    this.stakeholder.deleteSupplier(id).subscribe(res => {
      if (res.message == "Supplier Deleted!") {
        this.translate.get('TOAST.SUPPLIER_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getSuppliers();
    })
  }

  flattenSuppliers(suppliers: any[]): any[] {
    let result: any[] = [];
    suppliers.forEach((supplier) => {
      result.push(supplier);
    });
    return result;
  }
}
