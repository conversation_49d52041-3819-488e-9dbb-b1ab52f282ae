{"LOGINFORM": {"LOGIN": "<PERSON><PERSON>", "USERNAMEOREMAIL": "username or email", "PASSWORD": "password", "NEWONOURPLATFORM": "New on our platform?", "CREATEANACCOUNT": "Create an account", "PASSWORDREQUIRED": "Password is required", "USERNAMEREQUIRED": "Username is required"}, "REGISTERFORM": {"REGISTER": "Register", "COMPANYNAME": "Company name", "FAX": "Fax", "HOTLINE": "Hotline", "OFFICAILEMAIL": "Official email", "WEBSITE": "Website", "OFFICAILPHONENUMBER": "Official phone number", "ADDRESS": "Address", "DESCRIPTION": "Description", "CONTACTNAME": "Contact name", "REGISTRATIONNUMBER": "Registration number", "TIMEZONE": "Time zone", "LANGUAGE": "Language", "DOYOUHAVEANACCOUNT": "Do you have an account?", "LOGIN": "<PERSON><PERSON>", "REGISTRATIONDONE": "Your account has been registered and your account information is now under review. If there are no errors, you can log in using this email", "PASSWORD": "Password", "THANKYOUFORSIGNINGUP": "Thank you for signing up"}, "SIDEBAR": {"USERS": "Users", "MAIN_SETTINGS": "Main Settings", "DAILY_TRANSACTIONS": "Daily Transactions", "ACCOUNTS": "Accounts", "CURRENCIES": "Currencies", "FACILITIES": "Facilities", "TENANTS": "Tenants", "PROJECTS": "Projects", "COST": "Cost Center", "ALLBONDS": "All Reports", "NOTEPAYABLE": "Note Payable", "CASHRECEIPT": "Cash Receipt", "CREDITNOTE": "Credit Note", "DEBITNOTE": "Debit Note", "JOURNALENTRIES": "Journal Entries", "IDENTITY": "Identity", "BRANCHES": "Branches", "ACTIVITIES": "Activities", "SUPPLIERS": "Suppliers", "CLIENTS": "Clients", "DELEGATES": "Delegates", "SALESACCOUNT": "Sales Account", "INVOICES": "Invoices", "SYSTEMSETTINGS": "System Settings", "REPORTS": "Reports"}, "USERS": {"TABLE": {"USERNAME": "Username", "EMAIL": "Email", "PHONENUMBER": "Phone Number", "STATUS": "Status", "ACTIONS": "Actions", "ADDUSER": "Add New User"}, "HEADERS": {"USERNAME": "Username", "EMAIL": "Email", "PHONENUMBER": "Phone Number", "STATUS": "Status"}, "DROPDOWN": {"EDITTENANTUSER": "Edit tenant user", "RESETPASSWORD": "Reset password", "EDITUSERPERMISSIONS": "Edit user permissions"}}, "ADDUSERFORM": {"USERNAME": "User Name", "PASSWORD": "Password", "PHONENUMBER": "Phone Number", "EMAIL": "Email", "INVALIDEMAIL": "Invalid email"}, "EditPASSWORD": {"NEWPASWORD": "New Password", "CONFIRMPASSWORD": "Confirm Password", "EDITPASSWORD": "Edit password"}, "NAVBAR": {"REGISTEREDIN": "Registered in"}, "ACCOUNT": {"ADDACCOUNT": "Add Account", "UPDATE": "Update", "DELETE": "Delete", "UPDATEACCOUNT": "Update Account", "ACCOUNTNUMBER": "Account Number", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "LEVEL": "Level", "ACCOUNTOPENINGDATE": "Account opening date", "ADDACCOUNTFORM": {"RELATEDACCOUNT": "Related account", "ABBREVIATIONNAME": "Abbreviation name", "ACCOUNTNAME": "Account name", "ACCOUNTNAMEEN": "Account name (english)", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CURRENCYRATE": "Currency rate", "ACCOUNTOPENINGDATE": "Account opening date", "CREDITLIMITS": "Credit limits", "CURRENTBALANCE": "Current balance", "CURRENTBALANCEDATE": "Current balance date", "CREDITOR": "Credit", "DEBTOR": "Debit", "ESTIMATEDBUDGET": "Estimated budget", "OPENINGBALANCEBYINFOREIGNCURRENCY": "opening balance in foreign currency", "COMMENTS": "Notes", "EMAIL": "Email", "VATREGISTER": "Vat register", "ISPRIVATE": "Is private", "ISCONGREGATION": "Is congregation", "ISDETAILED": "Is detailed", "ISCOSTCENTER": "Is cost center", "ISPRIVATEISCOSTCENTERREQUIRED": "At least one of Is private or Is cost center is required.", "ACCOUNTNUMBER": "Account number", "PARENTNUMBER": "Parent number"}, "HEADERS": {"ACCOUNTNUMBER": "Account Number", "ACCOUNTNAME": "Account Name", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "OPENINGDATE": "Opening Date", "COMMENTS": "Comments"}}, "ACTIVITY": {"ADDACTIVITY": "Add Activity", "UPDATE": "Update", "DELETE": "Delete", "UPDATEACTIVITY": "Update Activity", "ACTIVITYNUMBER": "Activity Number", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "ACTIVITYOPENINGDATE": "Activity opening date", "ADDACTIVITYFORM": {"ABBREVIATIONNAME": "Abbreviation name", "ACTIVITYNAME": "Activity name", "ACTIVITYNAMEEN": "Activity name (english)", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CURRENCYRATE": "Currency rate", "ACTIVITYOPENINGDATE": "Activity opening date", "CREDITLIMITS": "Credit limits", "CURRENTBALANCE": "Current balance", "CURRENTBALANCEDATE": "Current balance date", "CREDITOR": "Credit", "DEBTOR": "Debit", "ESTIMATEDBUDGET": "Estimated budget", "OPENINGBALANCEBYINFOREIGNCURRENCY": "opening balance in foreign currency", "COMMENTS": "Notes", "EMAIL": "Email", "VATREGISTER": "Vat register", "ISPRIVATE": "Is private", "ISCONGREGATION": "Is congregation", "ISDETAILED": "Is detailed", "ISCOSTCENTER": "Is cost center", "ISPRIVATEISCOSTCENTERREQUIRED": "At least one of Is private or Is cost center is required.", "ACTIVITYNUMBER": "Activity number", "PARENTNUMBER": "Parent number"}, "HEADERS": {"ACTIVITYNUMBER": "Activity Number", "ACTIVITYNAME": "Activity Name", "EMAIL": "Email", "ESTIMATEDBUDGET": "Estimated Budget", "ACTIVITYOPENINGDATE": "Activity Opening Date", "STATUS": "Status"}}, "CURRENCIES": {"ADDNEWCURRENCY": "Add new currency", "UPDATECURRENCY": "Update currency", "CURRECNYCODE": "Currency code", "CURRENCYNAMEEN": "Currency name (EN)", "CURRECENCYNAMEAR": "Currency name (AR)", "CURRENCYNICKNAMEEN": "Currency abbreviation name (EN)", "CURRENCYNICKNAMEAR": "Currency abbreviation name (AR)", "FRACTIONNAMEEN": "Fraction name (EN)", "FRACTIONNAMEAR": "Fraction name (AR)", "CONVERSIONRATE": "Conversion rate", "CURRENCYNAME": "Currency name", "STATUS": "Status", "NOTES": "Notes", "ISACTIVE": "Active", "ACTIONS": "Actions", "HEADERS": {"CURRENCYCODE": "Currency Code", "CURRENCYNAMEEN": "Currency Name (En)", "CURRENCYNAMEAR": "Currency Name (Ar)", "CURRENCYNICKNAMEEN": "<PERSON><PERSON><PERSON><PERSON> (En)", "CURRENCYNICKNAMEAR": "<PERSON><PERSON><PERSON><PERSON> (Ar)", "FRACTIONNAMEEN": "Fraction Name (En)", "FRACTIONNAMEAR": "Fraction Name (Ar)", "CONVERSIONRATE": "Conversion Rate", "STATUS": "Status", "NOTES": "Notes"}}, "FACILITIES": {"ADDFACILITY": "Add facility", "UPDATEFACILITY": "Update facility", "FACILITYLOGO": "Facility logo", "FACILITYNUMBER": "Facility number", "FACILITYNAMEAR": "Facility name (ar)", "FACILITYNAMEEN": "Facility name (en)", "ADMINISTRATORNAME": "Administrator name", "FACILITYNAME": "Facility name", "EMAIL": "Email", "ADDRESS": "Address", "POSTALCODE": "Postal code", "PHONE": "Phone", "FIRSTMOBILE": "First mobile", "SECONDMOBILE": "Second mobile", "VATREGISTER": "Vat register", "VATACCOUNTNUMBER": "Vat account number", "NOTES": "Notes", "FILENAME": "File name", "POSTALBOX": "Postal box", "FAX": "Fax", "ACTIONS": "Actions", "HEADERS": {"FACILITYNUMBER": "Facility Number", "FACILITYNAMEAR": "Facility Name (Ar)", "FACILITYNAMEEN": "Facility Name (En)", "ADMINISTRATORNAME": "Administrator Name", "EMAIL": "Email", "ADDRESS": "Address", "POSTALCODE": "Postal Code", "PHONE": "Phone", "FIRSTMOBILE": "First Mobile", "SECONDMOBILE": "Second Mobile", "VATREGISTER": "Vat Register", "VATACCOUNTNUMBER": "Vat Account Number", "NOTES": "Notes", "FILENAME": "File Name", "POSTALBOX": "Postal Box", "FAX": "Fax"}}, "TENANTS": {"CONTACTNAME": "Contact Name", "EMAIL": "Email", "HOTLINE": "Hotline", "STATUS": "Status", "ACTIONS": "Actions", "EDITTENANTMODULE": "Edit Tenant <PERSON>"}, "PROJECTS": {"PROJECTNUMBER": "Project number", "PROJECTNAME": "Project name", "ACCOUNTOPENINGDATE": "Account opening date", "ABBREVIATIONNAME": "Abbreviation name", "PROJECTNAMEEN": "Project name (EN)", "CREDITLIMITS": "Credit limits", "CURRENTBALANCE": "Current balance", "CURRENTBALANCEDATE": "Current balance date", "NOTES": "Notes", "DEBTOR": "Debit", "CREDITOR": "Credit", "ESTIMATEDBUDGET": "Estimated budget", "ISCONGREGATION": "Is congregation", "ISDETAILED": "Is detailed", "EMAIL": "Email", "ADDPROJECT": "Add Project", "UPDATE": "Update", "DELETE": "Delete", "UPDATEPROJECT": "Update Project", "PARENTNUMBER": "Parent number", "HEADERS": {"PROJECTNUMBER": "Project number", "PROJECTNAME": "Project Name", "OPENINGBALANCE": "Opening Balance", "ACCOUNTOPENINGDATE": "Account Opening Date", "STATUS": "Status"}}, "COST": {"ADDCOST": "Add Cost", "UPDATE": "Update", "DELETE": "Delete", "UPDATECOST": "Update Cost", "ABBREVIATIONNAME": "Abbreviation name", "COSTCENTERNAME": "Cost center name", "ISCONGREGATION": "Is congregation", "ISDETAILED": "Is detailed", "COSTCENTERNAMEEN": "Cost center name (EN)", "ACCOUNTOPENINGDATE": "Account opening date", "CREDITLIMITS": "Credit limits", "CREDIT": "Credit", "DEBIT": "Debit", "CURRENTBALANCE": "Current balance", "CURRENTBALANCEDATE": "Current balance date", "ESTIMATEDBUDGET": "Estimated budget", "COMMENTS": "Comments", "EMAIL": "Email", "PlANINCOME": "Plan income", "PLANOUTCOME": "Plan outcome", "COSTNUMBER": "Cost number", "PARENTNUMBER": "Parent number", "HEADERS": {"COSTCENTERNUMBER": "Cost Center Number", "COSTCENTERNAME": "Cost Center Name", "OPENINGBALANCE": "Opening Balance", "COSTCENTEROPENINGDATE": "Cost Center Opening Date", "STATUS": "Status"}}, "BONDS": {"DAY": "Day", "MONTH": "Month", "YEAR": "Year", "ISCHECK": "Is there a check?", "CHECKNUMBER": "Check number", "CHECKDATE": "Check date", "BANKNAME": "Bank name", "BONDNUM": "Bond number", "CHANGE": "Create Report", "POST": "Post", "TYPE": "Type", "ACTIONS": "Actions", "TRANSACTIONHIJRIDATE": "Transaction date (Hijri)", "DATE": "Date", "DATEHIJRI": "Date (Hijri)", "AMOUNT": "Amount", "BRANCHNAME": "Branch name", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CONVERSIONRATE": "Conversion rate", "DEBIT": "Debit", "CREDIT": "Credit", "ADDDEBIT": "Add Debit", "ADDCREDIT": "Add Credit", "ACCOUNTNUMBER": "Account", "COSTCENTER": "Cost center", "PROJECT": "Project", "EXPLANATIONAR": "Explanation (Ar)", "EXPLANATIONEN": "Explanation (En)", "TRANSACTIONDATE": "Transaction date", "SEQUENCE": "sequence", "ACTIVITY": "Activity", "SAVE": "Save", "REFNUMBER": "Note number", "WARRNINGMSG": "The sum of debit and credit must be equal to 0", "HEADERS": {"DOCUMENTDATE": "Document Date", "AMOUNT": "Amount", "BRANCHNAME": "Branch Name", "FINANCIALREPORTLINES": "Financial Report Lines", "ACCOUNTNAME": "Account Name", "COSTCENTER": "Cost Center", "EXPLANATIONAR": "Explanation (AR)", "EXPLANATIONEN": "Explanation (EN)", "PROJECT": "Project"}}, "IDENTITY": {"IDENTITYCODE": "Identity code", "IDENTITYNAMEAR": "Identity code (ar)", "IDENTITYNAMEEN": "Identity code (en)", "ADDIDENTITY": "Add Identity"}, "BRANCHES": {"BRANCHNAME": "Branch name", "BRANCHNAMEEN": "Branch name (en)", "ADDRESS": "Address", "TELEPHONE": "Telephone", "FAX": "Fax", "MOBILE": "Mobile", "EMAIL": "Email", "CONTACTNAME": "Contact name", "ACCOUNTNO": "Account", "VATREG": "Vat register", "COMMERRCIALREGISTER": "Commercial register", "COUNTRYCODE": "Country code", "CITY": "City", "REGION": "Region", "DISTRICT": "District", "STREET": "Street", "POSTALCODE": "Postal code", "BUILDINGNO": "Building No", "ADDITIONALSTREETADDRESS": "Additional street address", "IDENTITYNO": "Identity No", "IDENTITYTYPEID": "Identity type", "ADDBRANCH": "Add Branch", "EDITBRANCH": "Edit Branch", "ALL": "All", "SELECT_BRANCH": "Select Branch"}, "PROFILE": {"ABOUT": "About", "ACCOUNTDETAILS": "Account details", "CONTACT": "Contact", "NAME": "Name", "STATUS": "Status", "PHONE": "Phone", "EMAIL": "Email", "USERNAME": "User name", "COMPANYNAME": "Company name"}, "STAKEHOLDERS": {"SALESNUMBER": "Sales number", "SUPPLIERNUMBER": "Supplier number", "OPENINGDATE": "Opening date", "ISSTOPPED": "stop", "NAMEAR": "Name (ar)", "NAMEEN": "Name (en)", "NAME": "Name", "RESPONSIBLE": "Responsible", "REGION": "Region", "COUNTRY": "Country", "DISTRICT": "District", "CITY": "City", "ADDRESS": "Address", "BUILDINGID": "Building ID", "STREETNAME": "Street name", "POSTALBOX": "Postal box", "ZIPCODE": "Zip code", "PHONE1": "phone 1", "PHONE2": "phone 2", "MOBILE1": "Mobile 1", "MOBILE2": "Mobile 2", "FAX1": "Fax 1", "FAX2": "Fax 2", "EMAIL": "Email", "COMMENTS": "Comments", "TAXACCOUNTNUMBER": "Account Number", "BOXNUMBER": "Box number", "FACILITYNUMBER": "Facility number", "EXTRANUMBER": "Extra number", "COMPANYNAME": "Company name", "COMMERCIALREGISTER": "Commercial register", "BANKNUMBER": "Bank number", "TARGET": "Target", "DISCOUNTFORCUSTOMER": "Discount for customer", "COMMISSION": "Commission", "ADDDELEGATE": "Add Delegate", "ADDCLIENT": "Add Client", "ADDSUPPLIER": "Add Supplier", "BASICDATA": "Basic data", "ADDRESSDATA": "Address data", "CONTACTINFORMATION": "Contract information", "ACCOUNTDATA": "Account data", "IDENTITY": "Identity", "ADDJOURNALENTRY": "Add Journal"}, "SALESACCOUNT": {"CODE": "Code", "NAME": "Name", "NAMEAR": "name (AR)", "ACCOUNTNUMBER": "Account number", "OPENDATE": "Opening date", "COMMENTS": "Comments", "ISACTIVE": "Active", "UPDATESALESACCOUNT": "Update Sales Account", "ADDNEWSALESACCOUNT": "Add New Sales Account", "HEADERS": {"CODE": "Code", "NAME": "Name", "NAMEAR": "Name Ar", "OPENDATE": "Open Date", "ISACTIVE": "Is Active", "ISDELETED": "Is Deleted"}}, "SUPPLIERS": {"NAME": "Name", "NAMEAR": "Name Ar", "STATUS": "Status", "BANKNAME": "Bank Name", "ADDRESS": "Address", "TAXACCOUNTNUMBER": "Tax Account Number"}, "CLIENTS": {"NAME": "Name", "NAMEAR": "Name Ar", "STATUS": "Status", "BANKNAME": "Bank Name", "FIRSTMOBILE": "First Mobile", "EMAIL": "Email"}, "DELEGATES": {"NAME": "Name", "NAMEAR": "Name Ar", "STATUS": "Status", "FIRSTPHONE": "firstPhone", "FIRSTMOBILE": "First Mobile", "EMAIL": "Email"}, "INVOICES": {"ADDINVOICE": "Add Invoice", "EDITINVOICE": "Edit Invoice", "INVOICENUMBER": "Invoice number", "DATE": "Date", "DELEGATE": "Delegate", "TERMS": "Terms", "NOTES": "Notes", "PHONE": "Phone", "RECIPIENT": "Recipient", "CLIENTNUMBER": "Client number", "NAMEAR": "name (AR)", "NAMEEN": "Name (en)", "RESPONSIBLE": "Responsible", "ADDRESS": "Address", "TOTALPRICE": "Total price", "DISCOUNT": "Discount", "DISC": "Disc", "NET": "Net", "TAXVALUE": "Tax value", "NETWORTH": "Net worth", "TAX": "Tax", "BASICDATA": "Basic data", "ITEMS": "Invoice items", "SALESACCOUNT": "Sales Account", "ADDINVOICEITEMS": "Add Invoice items", "ADDACCOUNTDATA": "Add Account data", "QUANTITY": "Quantity", "COST": "Cost", "TOTAL": "Total", "VAT": "VAT", "RATIO": "Percentage", "DESCRIPTIONAR": "<PERSON><PERSON> (AR)", "DESCRIPTIONEN": "Desc (EN)", "WARRANTYDATE": "Warrenty date", "ENDDATE": "End date", "APPROXIMATION": "Approximation", "ACCOUNTDATA": "Account data", "DEBIT": "Debit", "CREDIT": "Credit", "TAXACCOUNTNUMBER": "Tax account number", "COSTCENTER": "Cost center", "ACTIVITY": "Activity", "PAYMENTTYPE": "Payment type", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CONVERSIONRATE": "Conversion rate", "HEADERS": {"INSURANCEDATE": "Insurance Date", "ENDDATE": "End Date", "DISCOUNTAMOUNT": "Discount Amount", "PRICEAFTERDISCOUNT": "Price After Discount", "TAXAMOUNT": "Tax Amount", "TOTALPRICE": "Total Price", "CODE": "Code", "CURRENCYNAMEEN": "C<PERSON>rency EN", "CURRENCYNAMEAR": "Currency AR", "CURRENCYRATE": "Currency Rate", "FINALAMOUNT": "Final Amount"}}, "SYSTEMSETTINGS": {"ASSETSACCOUNT": "Assets Account", "DEDUCTACCOUNT": "Deduct Account", "PROPHETACCOUNT": "Prophet Account", "YEARPROPHETACCOUNT": "Year Prophet Account", "INCOMINGACCOUNT": "Incoming Account", "COSTACCOUNT": "Cost Account", "OUTCOMEACCOUNT": "Outcome Account", "EXPENSESACCOUNT": "Expenses Account", "PURCHASESACCOUNT": "Purchases Account", "BOXACCOUNT": "Box Account", "SELLSACCOUNT": "Sells Account", "BANKNUMBER": "Bank Number", "ISHIJRI": "<PERSON> <PERSON>jri Date", "STARTDATE": "Start Date", "ENDDATE": "End Date", "DATERANGE": "Date Range", "CREATEORUPDATESETTINGS": "Create or Update Settings"}, "REPORTS": {"ANALYTICALREPORTS": "Analytical Reports", "ESTIMATEDBUDGETREPORT": "Estimated Budget Report", "ALLFINALREPORTS": "All Final Reports", "DOCUMENTNAME": "Document Name", "ACCOUNTS": "Accounts", "ACCOUNT": "Account", "PARENTACCOUNT": "Parent Account", "DOCUMENTNUMBER": "Document Number", "TOTAL_DEBIT": "Total Debit", "TOTAL_CREDIT": "Total Credit", "FILTEREDBY": "Filtered by", "ACTIVITY": "Activity", "DESCRIPTION": "Description", "REFNUMBER": "Ref Number", "COSTCENTER": "Cost Center", "PROJECT": "Project", "BRANCHES": "Branches", "MOVEMENTDATE": "Movement Date", "BALANCESTARTPERIOD": "Balance Start Period", "PERIODTRANSACTION": "Period Transaction", "BALANCEENDPERIOD": "Balance End Period", "SUBMIT": "Submit"}, "SHARED": {"ACTIVE": "Active", "ALL": "All", "CHANGED": "Changed", "NOTCHANGED": "Not Changed", "INACTIVE": "Inactive", "TYPEHERETOSEARCH": "Type Here to Search...", "SHOW": "Show", "INPUTS": "Inputs", "UPDATE": "Update", "EDIT": "Edit", "DELETE": "Delete", "ACTIONS": "Actions", "CANCEL": "Cancel", "ADD": "Add", "ADDROW": "Add Row", "SAVE": "Save", "THISFIELDISREQUIRED": "This field is required", "INVALIDEMAIL": "Invalid email", "HIJRI": "<PERSON><PERSON><PERSON>", "EXPORTASPDF": "Export as PDF"}, "PDF": {"ACCOUNTREPORTS": "Accounts Report", "CURRIENCYREPORTS": "Currency Report", "FACILITIESREPORTS": "Facilities Report", "PROJECTSREPORTS": "Projects Report", "COSTSREPORTS": "Costs Report", "NOTEPAYABLEREPORTS": "Notes Payable Report", "CASHRECEIPTREPORTS": "Cash Receipt Report", "CREDITNOTEREPORTS": "Credit Note Report", "DEBITNOTEREPORTS": "Debit Note Report", "SUPPLIERREPORTS": "Supplier Report", "CLIENTSREPORTS": "Clients Report", "DELEGATEREPORTS": "Delegates Report", "ACTIVITYREPORTS": "Activity Report", "SALESACCOUNTREPORTS": "Sales Account Report", "INVOICESREPORTS": "Invoices Report", "USERSREPORTS": "Users Report", "DATE": "Date", "REF": "Reference", "JOURNALREPORTS": "Journal Entires Report"}, "TOAST": {"ACCOUNT_CREATED": "Account Created", "ACCOUNT_UPDATED": "Account Updated", "ACCOUNT_DELETED": "Account Deleted", "CURRENCY_CREATED": "Currency <PERSON>", "CURRENCY_UPDATED": "Currency Updated", "CURRENCY_DELETED": "<PERSON><PERSON><PERSON>cy Deleted", "FACILITY_CREATED": "Facility Created", "FACILITY_UPDATED": "Facility Updated", "FACILITY_DELETED": "Facility Deleted", "PROJECT_CREATED": "Project Created", "PROJECT_UPDATED": "Project Updated", "PROJECT_DELETED": "Project Deleted", "COST_CENTER_CREATED": "Cost Center Created", "COST_CENTER_UPDATED": "Cost Center Updated", "COST_CENTER_DELETED": "Cost Center Deleted", "REPORT_CREATED": "Report Created", "SUPPLIER_CREATED": "Supplier Created", "SUPPLIER_UPDATED": "Supplier Updated", "SUPPLIER_DELETED": "Supplier Deleted", "CLIENT_CREATED": "Client Created", "CLIENT_UPDATED": "Client Updated", "CLIENT_DELETED": "Client Deleted", "DELEGATE_CREATED": "Delegate Created", "DELEGATE_UPDATED": "Delegate Updated", "DELEGATE_DELETED": "Delegate Deleted", "ACTIVITY_CREATED": "Activity Created", "ACTIVITY_UPDATED": "Activity Updated", "ACTIVITY_DELETED": "Activity Deleted", "SALES_ACCOUNT_CREATED": "Sales Account Created", "SALES_ACCOUNT_UPDATED": "Sales Account Updated", "SALES_ACCOUNT_DELETED": "Sales Account Deleted", "INVOICE_CREATED": "Invoice Created", "INVOICE_UPDATED": "Invoice Updated", "INVOICE_DELETED": "Invoice Deleted", "SETTINGS_CREATED": "Settings Created", "USER_CREATED": "User Created", "PASSWORD_UPDATED": "Password Updated", "USER_UPDATED": "User Updated", "USER_PERMISSION_UPDATED": "User Permission Updated", "SOMETHING_WENT_WRONG": "Something went wrong", "SUCCESS": "Success", "ERROR": "Error", "NO_CHANGES_HAPPENED": "No changes happened"}, "VALIDATORS": {"DATERANGE": "Start date must be before the end date."}}