<div class="erp-card erp-shadow-end">
  <div class="my-3 row px-3">
    <div class="col-sm-12 col-md-6">
      <div class="d-flex align-items-center">
        <div class="mr-3">
          {{'SHARED.SHOW' |translate}}
        </div>
        <div class="mx-3">
          <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-12 col-md-3">
      <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
        name="search" [(ngModel)]="searchValue">
    </div>
    <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end"><button (click)="addBranch()"
        class="btn btn-primary">+
        {{'BRANCHES.ADDBRANCH' |translate}}</button></div>
  </div>
  <nz-table #basicTable #sortTable [nzPageSize]="pageSize" [nzData]="branchList | tablefilters: {branch_Name: searchValue, branch_Name_latin:searchValue, 
      adress:searchValue,telephone:searchValue,fax:searchValue,mobile:searchValue,
      email:searchValue,contact_Name:searchValue,account_No:searchValue,vat_Reg:searchValue,commercial_Register:searchValue,
      country_Code:searchValue,city:searchValue,region:searchValue,district:searchValue,street:searchValue,
      postalCode:searchValue,building_No:searchValue,additionalStreetAdress:searchValue,identityTypeId:searchValue}"
    nzTableLayout="fixed">
    <thead>
      <tr>
        <th nzColumnKey="name">{{'BRANCHES.BRANCHNAME'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.BRANCHNAMEEN'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.ADDRESS'|translate}}</th>
        <!-- <th nzColumnKey="name">{{'BRANCHES.TELEPHONE'|translate}}</th> -->
        <!-- <th nzColumnKey="name">{{'BRANCHES.FAX'|translate}}</th> -->
        <th nzColumnKey="name">{{'BRANCHES.MOBILE'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.EMAIL'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.CONTACTNAME'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.ACCOUNTNO'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.VATREG'|translate}}</th>
        <!-- <th nzColumnKey="name">{{'BRANCHES.COMMERRCIALREGISTER'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.COUNTRYCODE'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.CITY'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.REGION'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.DISTRICT'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.STREET'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.POSTALCODE'|translate}}</th> -->
        <!-- <th nzColumnKey="name">{{'BRANCHES.BUILDINGNO'|translate}}</th> -->
        <!-- <th nzColumnKey="name">{{'BRANCHES.ADDITIONALSTREETADDRESS'|translate}}</th> -->
        <!-- <th nzColumnKey="name">{{'BRANCHES.IDENTITYNO'|translate}}</th>
        <th nzColumnKey="name">{{'BRANCHES.IDENTITYTYPEID'|translate}}</th> -->
        <th nzColumnKey="name">{{'SHARED.ACTIONS'|translate}}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data">
        <td>{{ data?.branch_Name }}</td>
        <td>{{ data?.branch_Name_latin}}</td>
        <td>{{ data?.adress }}</td>
        <!-- <td>{{ data?.telephone }}</td> -->
        <!-- <td>{{ data?.fax }}</td> -->
        <td>{{ data?.mobile }}</td>
        <td>{{ data?.email }}</td>
        <td>{{ data?.contact_Name }}</td>
        <td>{{ data?.account_No }}</td>
        <td>{{ data?.vat_Reg }}</td>
        <!-- <td>{{ data?.commercial_Register }}</td>
        <td>{{ data?.country_Code }}</td>
        <td>{{ data?.city }}</td>
        <td>{{ data?.region }}</td>
        <td>{{ data?.district }}</td>
        <td>{{ data?.street }}</td>
        <td>{{ data?.postalCode }}</td> -->
        <!-- <td>{{ data?.building_No }}</td> -->
        <!-- <td>{{ data?.additionalStreetAdress }}</td> -->
        <!-- <td>{{ data?.identity_No }}</td>
        <td>{{ data?.identityTypeId }}</td> -->
        <td><span data-bs-toggle="dropdown" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg" width="16"
              height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
              <path
                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
            </svg></span>
          <ul class="dropdown-menu">
            <li class="p-2"><button (click)="EditBranch(data)"
                class="erp-btn erp-btn-primary">{{"SHARED.EDIT"|translate}} </button>
            </li>
            <li class="p-2"><button (click)="delete(data.id)"
                class="erp-btn erp-btn-danger">{{"SHARED.DELETE"|translate}} </button>
            </li>
          </ul>
        </td>

      </tr>
    </tbody>
  </nz-table>
</div>