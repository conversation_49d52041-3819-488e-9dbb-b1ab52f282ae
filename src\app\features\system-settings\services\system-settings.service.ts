import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {IBaseResponse} from "../../../core/models";
import {SystemSettings} from "../../../core/models/system-settings";

@Injectable({
  providedIn: 'root'
})
export class SystemSettingsService {

  constructor(private httpClient: HttpClient) { }

  // Getting System Settings
  getSystemSettings() {
    return this.httpClient.get<IBaseResponse<SystemSettings>>('/SystemSettings');
  }

  // Create Or Update System Settings
  createOrUpdateSystemSettings(data: SystemSettings) {
    return this.httpClient.post<IBaseResponse<SystemSettings>>('/SystemSettings', data);
  }
}
