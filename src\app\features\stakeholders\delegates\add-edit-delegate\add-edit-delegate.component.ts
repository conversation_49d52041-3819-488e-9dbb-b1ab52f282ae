import { Component, OnInit } from '@angular/core';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import { StakeholdersService } from '../../stakeholders.service';
import { ActivatedRoute, Router } from '@angular/router';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-add-edit-delegate',
  templateUrl: './add-edit-delegate.component.html',
  styleUrl: './add-edit-delegate.component.scss'
})
export class AddEditDelegateComponent implements OnInit {
  form: FormGroup;
  towns: any;
  countries: any;
  cities: any;
  regions: any;
  id: any;
  data: any;
  isSubmitting: boolean = false
  constructor(private fb: FormBuilder, private stakeholders: StakeholdersService, private route: ActivatedRoute, private router: Router, private translate: TranslateService, private messageService: MessageService) {
    this.createForm();
  }
  ngOnInit(): void {
    this.getRegions();
    this.getCities();
    this.getTowns();
    this.getCountries();
    this.id = this.route.snapshot.paramMap.get('id');
    if (this.id) {
      this.getDelegateById(this.id)
    }
  }
  getDelegateById(id: any) {
    this.stakeholders.getDelegateById(id).subscribe(res => {
      this.data = res.data;
      this.createForm();
    })

  }
  createForm() {
    this.form = this.fb.group(
      {
        "code": [this.data?.code || null],
        "name": [this.data?.name || null],
        "nameAr": [this.data?.nameAr || null],
        "accountNumber": [this.data?.accountNumber || '1'],
        "isStopped": [this.data?.isStopped || false],
        "branchId": [this.data?.branchId || null],
        "responsibleName": [this.data?.responsibleName || null],
        "countryId": [this.data?.countryId || null],
        "cityId": [this.data?.cityId || null],
        "regionId": [this.data?.regionId || null],
        "townId": [this.data?.townId || null],
        "fullAddress": [this.data?.fullAddress || null],
        "streetName": [this.data?.streetName || null],
        "buildingId": [this.data?.buildingId || null],
        "zipCode": [this.data?.zipCode || null],
        "postalCode": [this.data?.postalCode || null],
        "firstPhone": [this.data?.firstPhone || null],
        "secondPhone": [this.data?.secondPhone || null],
        "firstMobile": [this.data?.firstMobile || null],
        "secondMobile": [this.data?.secondMobile || null],
        "firstFax": [this.data?.firstFax || null],
        "secondFax": [this.data?.secondFax || null],
        "email": [this.data?.email || null],
        "notes": [this.data?.notes || null],
        "target": [this.data?.target || null],
        "commission": [this.data?.commission || null],
        "customerDescount": [this.data?.customerDescount || null]
      }
    )
  }
  getTowns() {
    this.stakeholders.getTowns().subscribe(res => {
      this.towns = res.data;
    })
    if (this.data && this.data.townId) {
      const selectedTown = this.towns.find((town: any) => town.id === this.data.townId);
      if (selectedTown) {
        this.onSelectedTown(selectedTown)
      }
    }
  }

  onSelectedTown(selected: any): void {
    this.form.patchValue({
      townId: selected.id,
    });
  }

  get townControl(): FormControl {
    return this.form.get('townId') as FormControl;
  }

  getCountries() {
    this.stakeholders.getCountries().subscribe(res => {
      this.countries = res.data;
    })

    if (this.data && this.data.countryId) {
      const selectedCountry = this.countries.find((country: any) => country.id === this.data.countryId);
      if (selectedCountry) {
        this.onSelectedCountry(selectedCountry)
      }
    }
  }

  onSelectedCountry(selected: any): void {
    this.form.patchValue({
      countryId: selected.id,
    });
  }

  get countryControl(): FormControl {
    return this.form.get('countryId') as FormControl;
  }

  getCities() {
    this.stakeholders.getCities().subscribe(res => {
      this.cities = res.data;
    })

    if (this.data && this.data.cityId) {
      const selectedCity = this.cities.find((city: any) => city.id === this.data.cityId);
      if (selectedCity) {
        this.onSelectedCity(selectedCity)
      }
    }
  }

  onSelectedCity(selected: any): void {
    this.form.patchValue({
      cityId: selected.id,
    });
  }

  get cityControl(): FormControl {
    return this.form.get('cityId') as FormControl;
  }

  getRegions() {
    this.stakeholders.getRegions().subscribe(res => {
      this.regions = res.data;
    })

    if (this.data && this.data.regionId) {
      const selectedRegion = this.regions.find((region: any) => region.id === this.data.regionId);
      if (selectedRegion) {
        this.onSelectedRegion(selectedRegion)
      }
    }
  }

  onSelectedRegion(selected: any): void {
    this.form.patchValue({
      regionId: selected.id,
    });
  }

  get regionControl(): FormControl {
    return this.form.get('regionId') as FormControl;
  }
  onSubmit() {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    let data = this.form.getRawValue();
    data.openDateHijri = null
    this.stakeholders.addDelegate(data).subscribe({
      next: (res) => {
        if (res.message == "Delegate created") {
          this.translate.get('TOAST.DELEGATE_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }

        this.isSubmitting = false;
        this.router.navigate(['/delegates']);
      },
      error: (err) => {
        this.isSubmitting = false; // Reset the flag even on error
        console.error('Error while adding delegate:', err);
      },
    });
  }

  edit() {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    let data = this.form.getRawValue();
    data.openDateHijri = null
    this.stakeholders.editDelegate(this.id, data).subscribe({
      next: (res) => {
        if (res.message == "Delegate updated") {
          this.translate.get('TOAST.DELEGATE_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }
        this.isSubmitting = false;
        this.router.navigate(['/delegates']);
      },
      error: (err) => {
        this.isSubmitting = false; // Reset the flag even on error
        console.error('Error while editing delegate:', err);
      },
    });
  }
}
