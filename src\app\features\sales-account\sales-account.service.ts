import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../core/models';
import { HttpClient } from '@angular/common/http';
import {BranchService} from "../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class SalesAccountService {

  private branchId: string = '';
  constructor(private httpClient:HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }
  getsalesaccount() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/SalesAccounts');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/SalesAccounts?branchId=${this.branchId}`);
    }
  }
  updatesalesaccount(id:any,data:any) {
    return this.httpClient.put<IBaseResponse<any>>(`/SalesAccounts/${id}`,data);
  }
  addsalesaccount(data:any) {
    if (this.branchId) {
      data.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/SalesAccounts`,data);
  }
  deletesalesaccount(id:any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/SalesAccounts/${id}`);
  }

}
