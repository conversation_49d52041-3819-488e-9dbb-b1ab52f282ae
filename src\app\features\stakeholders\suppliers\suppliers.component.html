<div class="erp-card erp-shadow-end">
    <div class="row my-3 row px-3 text-align-end">
        <div class="col-sm-12 col-md-6">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    {{'SHARED.SHOW' |translate}}
                </div>
                <div class="mx-3">
                    <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-3">
            <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                name="search" [(ngModel)]="searchValue">
        </div>
        <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end">
            <button (click)="addSupplierModal()" class="btn btn-primary">+
                {{'STAKEHOLDERS.ADDSUPPLIER' |translate}}</button>
        </div>

    </div>
    <nz-table #basicTable #sortTable [nzPageSize]="pageSize" [nzData]="suppliersList | tablefilters: {code: searchValue, name:searchValue, isStopped:searchValue
        , firstMobile:searchValue, email:searchValue, target:searchValue , notes:searchValue}" nzTableLayout="fixed">
        <thead>
            <tr>
                <th nzColumnKey="name">{{'STAKEHOLDERS.SUPPLIERNUMBER'|translate}}</th>
                <th nzColumnKey="name">{{'STAKEHOLDERS.NAME'|translate}}</th>
                <th nzColumnKey="name">{{'STAKEHOLDERS.ISSTOPPED'|translate}}</th>
                <th nzColumnKey="name">{{'STAKEHOLDERS.MOBILE1'|translate}}</th>
                <th nzColumnKey="name">{{'STAKEHOLDERS.EMAIL'|translate}}</th>
                <th nzColumnKey="name">{{'STAKEHOLDERS.COMMENTS'|translate}}</th>
                <th nzColumnKey="name">{{'SHARED.ACTIONS'|translate}}</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTable.data">
                <td>{{ data.serial }}</td>
                <td>{{ data.name }}</td>
                <td>{{ data.isStopped}}</td>
                <td>{{ data.firstMobile}}</td>
                <td>{{ data.email}}</td>
                <td>{{ data.notes}}</td>
                <td><span data-bs-toggle="dropdown" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg"
                            width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical"
                            viewBox="0 0 16 16">
                            <path
                                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                        </svg></span>
                    <ul class="dropdown-menu">
                        <li class="p-2"><button (click)="edit(data)"
                                class="erp-btn erp-btn-primary">{{"SHARED.EDIT"|translate}} </button>
                        </li>
                        <li class="p-2"><button (click)="delete(data.id)"
                                class="erp-btn erp-btn-danger">{{"SHARED.DELETE"|translate}} </button>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </nz-table>


  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h2>{{ 'PDF.SUPPLIERREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr class="text-blue-500">
        <th>{{'STAKEHOLDERS.SUPPLIERNUMBER'|translate}}</th>
        <th>{{'STAKEHOLDERS.NAME'|translate}}</th>
        <th>{{'STAKEHOLDERS.ISSTOPPED'|translate}}</th>
        <th>{{'STAKEHOLDERS.MOBILE1'|translate}}</th>
        <th>{{'STAKEHOLDERS.EMAIL'|translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let supplier of flattenedSuppliers">
        <td>{{ supplier.serial }}</td>
        <td>{{ lang == "en" ? supplier.name : supplier.nameAr }}</td>
        <td>{{ supplier.isStopped}}</td>
        <td>{{ supplier.firstMobile}}</td>
        <td>
          {{ supplier.email}}
        </td>
      </tr>
      </tbody>
    </table>

  </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>

</div>
