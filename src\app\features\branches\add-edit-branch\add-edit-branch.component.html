<div *nzModalTitle><span *ngIf="isUpdate">{{'BRANCHES.EDITBRANCH'|translate}}</span> <span
        *ngIf="!isUpdate">{{'BRANCHES.ADDBRANCH'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.BRANCHNAME'|translate}}</label>
                <input type="text" id="branch_Name" class="form-control" formControlName="branch_Name"
                    name="branch_Name">
                <ng-container *ngIf="form && form?.get('branch_Name')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('branch_Name')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.BRANCHNAMEEN'|translate}}</label>
                <input type="text" id="branch_Name_latin" class="form-control" formControlName="branch_Name_latin"
                    name="branch_Name_latin">
                <ng-container *ngIf="form && form?.get('branch_Name_latin')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('branch_Name_latin')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.ADDRESS'|translate}}</label>
                <input type="text" id="adress" class="form-control" formControlName="adress" name="adress">
                <ng-container *ngIf="form && form?.get('adress')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('adress')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.TELEPHONE'|translate}}</label>
                <input type="text" id="telephone" class="form-control" formControlName="telephone" name="telephone">
                <ng-container *ngIf="form && form?.get('telephone')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('telephone')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.FAX'|translate}}</label>
                <input type="text" id="fax" class="form-control" formControlName="fax" name="fax">
                <ng-container *ngIf="form && form?.get('fax')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('fax')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.MOBILE'|translate}}</label>
                <input type="text" id="mobile" class="form-control" formControlName="mobile" name="mobile">
                <ng-container *ngIf="form && form?.get('mobile')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('mobile')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.EMAIL'|translate}}</label>
                <input type="text" id="email" class="form-control" formControlName="email" name="email">
                <ng-container *ngIf="form && form?.get('email')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.CONTACTNAME'|translate}}</label>
                <input type="text" id="contact_Name" class="form-control" formControlName="contact_Name"
                    name="contact_Name">
                <ng-container *ngIf="form && form?.get('contact_Name')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('contact_Name')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.ACCOUNTNO'|translate}}</label>
                <input type="text" id="account_No" class="form-control" formControlName="account_No" name="account_No">
                <ng-container *ngIf="form && form?.get('account_No')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('account_No')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.VATREG'|translate}}</label>
                <input type="text" id="vat_Reg" class="form-control" formControlName="vat_Reg" name="vat_Reg">
                <ng-container *ngIf="form && form?.get('vat_Reg')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('vat_Reg')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.COMMERRCIALREGISTER'|translate}}</label>
                <input type="text" id="commercial_Register" class="form-control" formControlName="commercial_Register"
                    name="commercial_Register">
                <ng-container *ngIf="form && form?.get('commercial_Register')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('commercial_Register')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.COUNTRYCODE'|translate}}</label>
                <input type="text" id="country_Code" class="form-control" formControlName="country_Code"
                    name="country_Code">
                <ng-container *ngIf="form && form?.get('country_Code')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('country_Code')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.CITY'|translate}}</label>
                <input type="text" id="city" class="form-control" formControlName="city" name="city">
                <ng-container *ngIf="form && form?.get('city')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('city')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.REGION'|translate}}</label>
                <input type="text" id="region" class="form-control" formControlName="region" name="region">
                <ng-container *ngIf="form && form?.get('region')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('region')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.DISTRICT'|translate}}</label>
                <input type="text" id="district" class="form-control" formControlName="district" name="district">
                <ng-container *ngIf="form && form?.get('district')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('district')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.STREET'|translate}}</label>
                <input type="text" id="street" class="form-control" formControlName="street" name="street">
                <ng-container *ngIf="form && form?.get('street')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('street')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.POSTALCODE'|translate}}</label>
                <input type="text" id="postalCode" class="form-control" formControlName="postalCode" name="postalCode">
                <ng-container *ngIf="form && form?.get('postalCode')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('postalCode')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.BUILDINGNO'|translate}}</label>
                <input type="text" id="building_No" class="form-control" formControlName="building_No"
                    name="building_No">
                <ng-container *ngIf="form && form?.get('building_No')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('building_No')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'BRANCHES.ADDITIONALSTREETADDRESS'|translate}}</label>
                <input type="text" id="additionalStreetAdress" class="form-control"
                    formControlName="additionalStreetAdress" name="additionalStreetAdress">
                <ng-container *ngIf="form && form?.get('additionalStreetAdress')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('additionalStreetAdress')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
              <label>{{ 'STAKEHOLDERS.IDENTITY'|translate }}</label>
              <app-reusable-dropdown
                [options]="identityList"
                [displayKey]="'identity_Name'"
                [valueKey]="'identity_Code'"
                [formControl]="identityControl"
                [selectedId]="form?.get('identity_No')?.value"
                (selectedValue)="onSelectedIdentity($event)">
              </app-reusable-dropdown>
            </div>
        </div>
    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="addorEditBranch()"><span
                *ngIf="isUpdate">{{'BRANCHES.EDITBRANCH'|translate}}</span> <span
                *ngIf="!isUpdate">{{'BRANCHES.ADDBRANCH'|translate}}</span></button>
    </div>
</form>
