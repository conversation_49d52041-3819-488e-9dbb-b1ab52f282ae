<nz-layout class="app-layout">
  <nz-sider
    class="menu-sidebar"
    nzCollapsible
    nzWidth="256px"
    nzBreakpoint="md"
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
  >
    <!-- Logo -->
    <div class="sidebar-logo" (click)="isCollapsed = !isCollapsed">
      <img *ngIf="!isCollapsed" src="../../../../assets/images/logo-lg.png" alt="logo" />
      <img *ngIf="isCollapsed" src="../../../../assets/images/logo-sm.png" alt="logo" />
    </div>
    <div class="white-divider"></div>

    <div class="sider-container">
      <!-- Superadmin Section -->
      <ul nz-menu class="menu" nzMode="inline" *ngIf="roleService.hasRole('superadmin')">
        <!-- === Main Settings === -->
        <li nz-submenu [nzTitle]="mainSettingsTitle">
          <ng-template #mainSettingsTitle>
            <span class="submenu-title" >
                <span nz-icon nzType="setting"></span>
                <span>{{ 'SIDEBAR.MAIN_SETTINGS' | translate }}</span>
            </span>
          </ng-template>
          <ul>
            <li nz-menu-item routerLink="/users" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="user"></span>
              <span>{{ 'SIDEBAR.USERS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/accounts" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="account-book"></span>
              <span>{{ 'SIDEBAR.ACCOUNTS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/branches"
                routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="branches"></span>
              <span>{{ 'SIDEBAR.BRANCHES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/currencies" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="dollar"></span>
              <span>{{ 'SIDEBAR.CURRENCIES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/facilities" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-left"></span>
              <span>{{ 'SIDEBAR.FACILITIES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/projects" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="project"></span>
              <span>{{ 'SIDEBAR.PROJECTS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/cost" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="wallet"></span>
              <span>{{ 'SIDEBAR.COST' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/activity" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.ACTIVITIES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/sales-account" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.SALESACCOUNT' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/clients" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.CLIENTS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/suppliers" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.SUPPLIERS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/delegates" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.DELEGATES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/system-settings" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.SYSTEMSETTINGS' | translate }}</span>
            </li>
          </ul>
        </li>

        <!-- === Daily Transactions === -->
        <li nz-submenu [nzTitle]="dailyTxTitle">
          <ng-template #dailyTxTitle>
            <span class="submenu-title">
                <span nz-icon nzType="calendar"></span>
                <span>{{ 'SIDEBAR.DAILY_TRANSACTIONS' | translate }}</span>
            </span>
          </ng-template>
          <ul>
            <li nz-menu-item routerLink="/note-payable" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.NOTEPAYABLE' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/cash-receipt" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.CASHRECEIPT' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/credit-note" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.CREDITNOTE' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/debit-note" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.DEBITNOTE' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/journal-entries" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.JOURNALENTRIES' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/all-bonds" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.ALLBONDS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/invoices" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="pic-center"></span>
              <span>{{ 'SIDEBAR.INVOICES' | translate }}</span>
            </li>
          </ul>
        </li>

        <!-- === Reports === -->
        <li nz-submenu [nzTitle]="reportsTitle">
          <ng-template #reportsTitle>
            <span class="submenu-title">
                <span nz-icon nzType="bar-chart"></span>
                <span>{{ 'SIDEBAR.REPORTS' | translate }}</span>
            </span>
          </ng-template>
          <ul>
            <li nz-menu-item routerLink="/reports/all-final-reports" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="file-done"></span>
              <span>{{ 'REPORTS.ALLFINALREPORTS' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/reports/estimated-budget-report" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="fund"></span>
              <span>{{ 'REPORTS.ESTIMATEDBUDGETREPORT' | translate }}</span>
            </li>
            <li nz-menu-item routerLink="/reports/analytical-reports" routerLinkActive="ant-menu-item-selected">
              <span nz-icon nzType="line-chart"></span>
              <span>{{ 'REPORTS.ANALYTICALREPORTS' | translate }}</span>
            </li>
          </ul>
        </li>
      </ul>

      <!-- === Shared Section for Master AND Superadmin === -->
      <ul nz-menu class="menu" nzMode="inline" *ngIf="roleService.hasRole('Master')">
        <li nz-menu-item routerLink="/tenants" routerLinkActive="ant-menu-item-selected">
          <span nz-icon nzType="user"></span>
          <span>{{ 'SIDEBAR.TENANTS' | translate }}</span>
        </li>
        <li nz-menu-item routerLink="/identity" routerLinkActive="ant-menu-item-selected">
          <span nz-icon nzType="pic-center"></span>
          <span>{{ 'SIDEBAR.IDENTITY' | translate }}</span>
        </li>
      </ul>
    </div>
  </nz-sider>

  <!-- Right Layout -->
  <nz-layout>
    <div class="p-navbar">
      <div
        [ngClass]="isMaster === false ? 'd-flex justify-content-center align-items-center gap-5' : 'd-flex justify-content-end align-items-center'">
        @if (isMaster === false) {
          <div
            class="d-flex align-items-center col-6 col-md-4 col-lg-3 justify-content-start px-0">
            <label for="branch" class="form-label mb-0 me-2"
                   style="min-width: 90px;">
              {{ 'BRANCHES.SELECT_BRANCH' | translate }}
            </label>
            <select id="branch"
                    class="form-select form-select-sm flex-grow-1"
                    [formControl]="branchControl"
                    style="max-width: 160px; border-radius: 10px;">
              <option *ngFor="let branch of branchList" [value]="branch.id">
                {{ branch.branch_Name }}
              </option>
            </select>
          </div>
        }
        <div class="d-flex gap-1">
          <app-lang-dropdown></app-lang-dropdown>
          <div class="d-flex mx-2 cursor-pointer" (click)="logout()"
               style="transform: rotate(-90deg);">
            <span nz-icon nzType="logout" nzTheme="outline"></span>
          </div>
        </div>
      </div>
      <app-navbar></app-navbar>
    </div>

    <nz-content>
      <div class="inner-content">
        <router-outlet></router-outlet>
      </div>
    </nz-content>
  </nz-layout>
</nz-layout>
