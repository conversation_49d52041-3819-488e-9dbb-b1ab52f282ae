export interface AnalyticalReport {
  accountIds?: string[];
  brancheIds?: string[];
  costCenterIds?: string[];
  projectIds?: string[];
  activityIds?: string[];
  startDate?: string;
  endDate?: string;
}

export interface EstimatedBudgetReport {
  accountIds?: string[];
  brancheIds?: string[];
  costCenterIds?: string[];
  projectIds?: string[];
  activityIds?: string[];
  startDate?: string;
  endDate?: string;
}

export interface AnalyticReport {
  documentNumber?: string;
  totalCreditYearToDate?: number;
  totalDebitYearToDate?: number;
}


// for pdf
export interface Account {
  id: number;
  accountName: string;
  accountNumber: string;
  label?: string;
}

export interface Report {
  accountName: string;
  totalDebitFromStartDateToEndDate: number;
  totalCreditFromStartDateToEndDate: number;
}
