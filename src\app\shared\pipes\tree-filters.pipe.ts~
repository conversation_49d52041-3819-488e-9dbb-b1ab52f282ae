import { Pipe, PipeTransform } from '@angular/core';
import { NzTreeNode } from 'ng-zorro-antd/tree';

interface FilterConfig {
  [key: string]: string;
}

@Pipe({
  name: 'treeFilters',
})
export class TreeFiltersPipe implements PipeTransform {
  transform(nodes: any[], filters: FilterConfig): any[] {
    if (!nodes || !nodes.length || !filters) {
      return nodes;
    }

    // Create a deep copy to avoid modifying the original data
    const clonedNodes = this.deepCloneNodes(nodes);
    return this.filterNodes(clonedNodes, filters);
  }

  private filterNodes(nodes: any[], filters: FilterConfig): any[] {
    return nodes.filter(node => {
      const matches = this.nodeMatchesFilters(node, filters);

      // If the node has children, recursively filter them
      if (node.children && node.children.length) {
        const filteredChildren = this.filterNodes(node.children, filters);
        node.children = filteredChildren;

        // Show parent if it matches filters or has matching children
        return matches || filteredChildren.length > 0;
      }

      return matches;
    });
  }

  private nodeMatchesFilters(node: any, filters: FilterConfig): boolean {
    return Object.entries(filters).some(([key, filterValue]) => {
      if (!filterValue) return true;

      const nodeValue = node.originalData?.[key];
      if (nodeValue === undefined || nodeValue === null) return false;

      // Convert both values to lowercase strings for comparison
      const normalizedFilterValue = String(filterValue).toLowerCase().trim();
      const normalizedNodeValue = String(nodeValue).toLowerCase().trim();

      return normalizedNodeValue.includes(normalizedFilterValue);
    });
  }

  private deepCloneNodes(nodes: any[]): any[] {
    return nodes.map(node => ({
      ...node,
      children: node.children ? this.deepCloneNodes(node.children) : [],
      originalData: { ...node.originalData }
    }));
  }
}
