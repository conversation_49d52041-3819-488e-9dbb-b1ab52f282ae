import {NgModule} from "@angular/core";
import {CommonModule} from "@angular/common";
import {SystemSettingsRoutingModule} from "../../../features/system-settings/system-settings-routing.module";
import {SharedModule} from "../../../shared/shared.module";
import {ReusableDropdownComponent} from "../../../shared/components/reusable-dropdown/reusable-dropdown.component";
import {RegisterComponent} from "./register.component";

@NgModule({
  declarations: [
    RegisterComponent
  ],
  imports: [
    CommonModule,
    SystemSettingsRoutingModule,
    SharedModule,
    ReusableDropdownComponent,

  ]
})

export class RegisterComponentModule { }
