import {Component} from '@angular/core';
import { NzModalRef } from 'ng-zorro-antd/modal';
import { UsersService } from '../services/users.service';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";
import {BranchService} from "../../branches/branch.service";
import {branch} from "../../../core/models";

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrl: './add-user.component.scss'
})
export class AddUserComponent {
  regsiterForm: FormGroup;
  branchList: branch[] = []
  visible = false;
  inputType = 'password';
  constructor(private modal: NzModalRef, private user: UsersService, private fb: FormBuilder, private  branches : BranchService, private translate: TranslateService, private messageService: MessageService) {
    this.regsiterForm = this.fb.group({
      userName: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      password: ['DefaultPassword!123', [Validators.required, this.passwordValidator]],
      phoneNumber: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      branchId: ['', Validators.required],
    });
  }

  ngOnInit(): void {
    this.getAllBranches();
    throw new Error('Method not implemented.');
  }

  getAllBranches () {
    this.branches.getBranches().subscribe(res => {
      this.branchList = res.data;
    })
  }

  destroyModal(): void {
    this.modal.destroy();
  }
  toggleVisibility() {
    if (this.visible) {
      this.inputType = 'password';
      this.visible = false;

    } else {
      this.inputType = 'text';
      this.visible = true;

    }
  }

  // controlling identity
  get branchControl(): FormControl {
    return this.regsiterForm.get('branchId') as FormControl;
  }

  onSelectedBranch(selected: any): void {
    this.regsiterForm.patchValue({
      branchId: selected.id,
    });
  }

  onSubmit() {
    if (this.regsiterForm.valid) {
      let data = this.regsiterForm.getRawValue();
      data.phoneNumber.toString()
      this.user.addUser(data).subscribe(res => {
        if (res.message == "User registered successfully") {
          this.translate.get('TOAST.USER_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(true);
      })
    } else {
      Object.values(this.regsiterForm.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  passwordValidator(control: FormControl): { [key: string]: boolean } | null {
    const value = control.value;
    if (!value) {
      return { required: true };
    }
    const hasMinLength = value.length >= 6;
    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const errors: any = {};
    if (!hasMinLength) {
      errors.minLength = true;
    }
    if (!hasUpperCase) {
      errors.upperCase = true;
    }
    if (!hasLowerCase) {
      errors.lowerCase = true;
    }
    return Object.keys(errors).length ? errors : null;
  }

}
