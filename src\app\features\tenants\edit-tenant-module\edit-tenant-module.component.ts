import { Component, inject, OnInit } from '@angular/core';
import { TenantsService } from '../services/tenants.service';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';

@Component({
  selector: 'app-edit-tenant-module',
  templateUrl: './edit-tenant-module.component.html',
  styleUrl: './edit-tenant-module.component.scss'
})
export class EditTenantModuleComponent implements OnInit {
  moduleList: any;
  assignedPermissionsList: any;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private tenants: TenantsService) {

  }
  ngOnInit(): void {
    this.getModules();
  }
  getModules() {
    this.tenants.getModules().subscribe(res => {
      this.moduleList = res.data;
      this.getAssignedPermissions()
    })
  }
  getAssignedPermissions() {
    this.tenants.getAssignedPermissions(this.nzModalData.tenantId).subscribe(res => {
      this.assignedPermissionsList = res.data;
      for (const item1 of this.moduleList) {
        for (const item2 of this.assignedPermissionsList) {
          if (item1.id === item2.id) {
            item1.isChecked = true
          }
        }
      }
    })
  }

  destroyModal(): void {
    this.modal.destroy();
  }


  onSubmit(data: any) {
    let checkedModules: any = []
    this.moduleList.forEach((element:any) => {
      if(element.isChecked){
        checkedModules.push(element.id)
      }
    });
    data = { "moduleIds": checkedModules, "tenantId": this.nzModalData.tenantId }
    this.tenants.editModules(data).subscribe(res => {
      this.modal.destroy();
    })
  }

}
