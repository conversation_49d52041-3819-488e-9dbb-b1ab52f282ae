import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { CurrencyService } from './services/currency.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { AddCurrencyComponent } from './add-currency/add-currency.component';
import {MessageService} from "primeng/api";

@Component({
  selector: 'app-currencies',
  templateUrl: './currencies.component.html',
  styleUrl: './currencies.component.scss'
})
export class CurrenciesComponent implements OnInit {
  currenciesList: any;
  @ViewChild('pdfTable', { static: false }) pdfTable!: ElementRef;
  searchValue: string = '';
  pageSize: number = 5;
  lang: string = "en";
  langDirection: 'ltr' | 'rtl' = 'ltr';

  currentDate: Date = new Date()
  currentRef: string
  flatCurrenciesList: any[]

  constructor(private currency: CurrencyService, private modalService: NzModalService, private translate: TranslateService, private messageService: MessageService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'

    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getCurrencies()
      this.getDirection(this.lang)

    })
  }
  ngOnInit(): void {
    this.getCurrencies();
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  getCurrencies() {
    this.currency.getCurrencies().subscribe(res => {
      this.currenciesList = res.data;
      if (this.lang == 'en') {
      }
      this.flatCurrenciesList = this.flattenCurrencies(res.data)
    })
  }

  delete(id: any) {
    this.currency.deleteCurrencies(id).subscribe(res => {
      if (res.message == "Currency deleted") {
        this.translate.get('TOAST.CURRENCY_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        })
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        })
      }
      this.getCurrencies();
    })
  }
  EditCurrency(data: any) {
    this.modalService.create({
      nzContent: AddCurrencyComponent,
      nzDirection: this.langDirection,
      nzData: data
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getCurrencies();
      }
    })
  }

  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  sortFnCurrencyCode = (a: any, b: any) => {
    return a.currencyCode- b.currencyCode
  }

  sortFnCrEN = (a: any, b: any) => {
    return a.currencyNameEn.localeCompare(b.currencyNameEn)
  }



  sortFnCrAR = (a: any, b: any) => {
    return a.currencyNameAr.localeCompare(b.currencyNameAr)
  }
  sortFncrnicknameen = (a: any, b: any) => {
    return a.currencyNickNameEn.localeCompare(b.currencyNickNameEn)
  }
  sortFncrnicknamear = (a: any, b: any) => {
    return a.currencyNickNameAr.localeCompare(b.currencyNickNameAr)
  }
  sortFnConversionRate = (a: any, b: any) => {
    return a.conversionRate - b.conversionRate
  }
  sortFnNActive = (a: any, b: any) => {
    return a.isActive - b.isActive
  }
  sortFnNotes = (a: any, b: any) => {
    return a.notes.localeCompare(b.notes)
  }

  addNewCurrency(): void {
    this.modalService.create({
      nzContent: AddCurrencyComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res) {

        this.getCurrencies();
      }
    })
  }

  flattenCurrencies(currencies: any[]): any[] {
    let result: any[] = [];
    currencies.forEach((currency) => {
      result.push(currency);
    });
    return result;
  }
}
