import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';

@Injectable({
  providedIn: 'root'
})
export class CurrencyService {

  constructor(private httpClient:HttpClient) { }
  getCurrencies() {
    return this.httpClient.get<IBaseResponse<any>>(`/Currencies`);
  }
  updateCurrencies(id:any,data:any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Currencies/${id}`,data);
  }
  addCurrencies(data:any) {
    return this.httpClient.post<IBaseResponse<any>>(`/Currencies`,data);
  }
  deleteCurrencies(id:any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Currencies/${id}`);
  }
}
