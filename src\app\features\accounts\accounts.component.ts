import {ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddAccountComponent } from './add-account/add-account.component';
import { AccountService } from './services/account.service';
import { NzFormatEmitEvent, NzTreeComponent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import {MessageService} from "primeng/api";
import {BranchService} from "../branches/branch.service";

@Component({
  selector: 'app-accounts',
  templateUrl: './accounts.component.html',
  styleUrl: './accounts.component.scss'
})
export class AccountsComponent implements OnInit {
  accountsList: any;
  showARDate: boolean = false;
  @ViewChild('nzTreeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;
  @ViewChild('pdfTable', { static: false }) pdfTable!: ElementRef;
  selectedAccount: any;
  child: any;
  flatAccountsList: any[]
  currentDate: Date = new Date()
  currentRef: string

  constructor(private modalService: NzModalService, private translate: TranslateService, private account: AccountService, private branches: BranchService,
    private cd: ChangeDetectorRef, private messageService: MessageService
  ) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getAccounts()
      if (this.lang === 'ar') {
        this.showARDate = true;
      }
      else {
        this.showARDate = false;
      }
      this.getDirection(this.lang)

    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getAccounts();
    })
    this.getAccounts();
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  getAccounts() {
    this.account.getAccounts().subscribe(res => {
      this.accountsList = res.data;
      this.accountsList.forEach((element: any) => {
        if (element.accountOpeningDate) {
          element.arabicAccountOpeningDate = new Date(element.accountOpeningDate).toLocaleDateString('ar-EG-u-nu-latn', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })
        }
      });
      this.accountsList = this.transformToNzTreeNodeOptions(this.accountsList)
      this.flatAccountsList = this.flattenAccounts(res.data);
    })
  }

  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string = "en";
  langDirection: 'ltr' | 'rtl' = 'ltr';
  addAccountModal() {
    if (this.selectedAccount) {
      this.child = true
    }
    this.modalService.create({
      nzContent: AddAccountComponent,
      nzDirection: this.langDirection,
      nzData: { child: this.child, ...this.selectedAccount },
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedAccount = null;
        this.getAccounts();
      }
    })
  }
  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';
    }
  }

  shouldShowAddButton(): boolean {
    // Show the button if no account is selected
    if (!this.selectedAccount) {
      return true;
    }

    // Check if the selected account has isDetailed === true
    // Look in originalData first, then direct property
    const isDetailed = this.selectedAccount.originalData?.isDetailed || this.selectedAccount.isDetailed;

    // Hide button if isDetailed is true, show otherwise
    return isDetailed !== true;
  }


  mycheck(event: any, origin: any) {
    event.stopPropagation();

    // If the node is already checked, uncheck it (toggle behavior)
    if (origin.checked) {
      origin.checked = false;
      this.selectedAccount = null;  // Deselect the account
    } else {
      // Uncheck all nodes first
      this.uncheckAllNodes();

      // Check the selected node
      origin.checked = true;
      this.selectedAccount = origin;  // Set the selected account
    }

    // Ensure UI reflects changes
    this.cd.detectChanges();
  }

  uncheckAllNodes() {
    this.nzTreeComponent.getTreeNodes().forEach(node => this.uncheckNodeRecursively(node));

    // Reset selectedAccount when no nodes are selected
    this.selectedAccount = null;
    this.cd.detectChanges();
  }

  uncheckNodeRecursively(node: any) {
    // Uncheck the node and its children
    node.origin.checked = false;
    if (node.children) {
      node.children.forEach((child: any) => this.uncheckNodeRecursively(child));
    }
  }

  uncheckOtherNodes(e: any) {
    e.children.forEach((c: any) => {
      c.checked = false;
      this.uncheckOtherNodes(c)
    })
  }

  updateAccountModal() {
    this.modalService.create({
      nzContent: AddAccountComponent,
      nzDirection: this.langDirection,
      nzData: this.selectedAccount,
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedAccount = null;
        this.getAccounts();
      }
    })

  }
  deleteAccount() {

    this.account.deleteAccount(this.selectedAccount.id).subscribe(res => {
      if (res.message == "Account deleted") {
        this.translate.get('TOAST.ACCOUNT_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        })
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        })
      }
      this.selectedAccount = null;
      this.getAccounts();
    })
  }
  transformToNzTreeNodeOptions(data: any): NzTreeNodeOptions[] {
    return data.map((node: any) => ({
      title: node.accountName, // map name to title
      id: node.id, // map id to key
      accountNumber: node.accountNumber,
      email: node.email,
      disabled: false,
      checked: false,
      notes: node.notes,
      originalData: { ...node },
      children: this.transformToNzTreeNodeOptions(node.accounts) // recursively transform children
    }));
  }

  flattenAccounts(accounts: any[]): any[] {
    let result: any[] = [];
    accounts.forEach((account) => {
      result.push(account);
      if (account.accounts && account.accounts.length > 0) {
        result = result.concat(this.flattenAccounts(account.accounts));
      }
    });
    return result;
  }
}
