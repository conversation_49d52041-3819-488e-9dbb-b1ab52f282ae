<div *nzModalTitle><span *ngIf="!isUpdate">{{'PROJECTS.ADDPROJECT'|translate}}</span> <span
        *ngIf="isUpdate">{{'PROJECTS.UPDATEPROJECT'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="form-group">
            <label>{{'PROJECTS.PROJECTNUMBER'|translate}}</label>
            <input type="text" id="projectNumber" class="form-control" formControlName="projectNumber"
                name="projectNumber">

        </div>
        <div class="form-group">
            <label>{{'PROJECTS.PARENTNUMBER'|translate}}</label>
            <input type="text" id="parentProjectId" class="form-control" formControlName="parentProjectNumber"
                name="parentProjectId">

        </div>
        <div class="form-group">
            <label>{{'PROJECTS.ABBREVIATIONNAME'|translate}}</label>
            <input type="text" id="shortName" class="form-control" formControlName="shortName" name="shortName">
            <ng-container *ngIf="form && form?.get('shortName')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('shortName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'PROJECTS.PROJECTNAME'|translate}}</label>
            <input type="text" id="projectName" class="form-control" formControlName="projectName" name="projectName">
            <ng-container *ngIf="form && form?.get('projectName')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('projectName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'PROJECTS.PROJECTNAMEEN'|translate}}</label>
            <input type="text" id="shortName" class="form-control" formControlName="project_Latin_Name" name="project_Latin_Name">
            <ng-container *ngIf="form && form?.get('project_Latin_Name')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('project_Latin_Name')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'PROJECTS.ACCOUNTOPENINGDATE'|translate}}</label>
                    <br />
                    <nz-date-picker (ngModelChange)="dateHandler()" formControlName="accountOpeningDate"
                        class="datepicker"></nz-date-picker>
                    <ng-container *ngIf="form && form?.get('accountOpeningDate')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('accountOpeningDate')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'PROJECTS.ACCOUNTOPENINGDATE'|translate}}
                        ({{'SHARED.HIJRI'|translate}})</label>
                    <input [value]="form?.get('accountOpeningDatehijri')?.value |arabicNumerals" [attr.disabled]="true"
                        class="form-control" type="text" />
                </div>
            </div>
        </div>


        <div class="form-group">
            <label>{{'PROJECTS.CREDITLIMITS'|translate}}</label>
            <input type="number" id="creditLimit" class="form-control" formControlName="creditLimit" name="creditLimit">
            <ng-container *ngIf="form && form?.get('creditLimit')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('creditLimit')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>


        <div class="row">
            <div class="col-12 col-md-4">
                <div class="form-group">
                    <label>{{'PROJECTS.CURRENTBALANCE'|translate}}</label>
                    <input type="number" id="openingBalance" class="form-control" formControlName="openingBalance"
                        name="openingBalance">
                    <ng-container *ngIf="form && form?.get('openingBalance')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('openingBalance')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>
            <div class="col-12 col-md-4">
                <div class="form-group">
                    <label>{{'PROJECTS.CURRENTBALANCEDATE'|translate}}</label>
                    <br />
                    <nz-date-picker formControlName="openingBalanceDate" class="datepicker"></nz-date-picker>
                    <ng-container *ngIf="form && form?.get('openingBalanceDate')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('openingBalanceDate')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>


            <div class="col-12 col-md-4 radio-style">
                <div class="d-flex">
                    <div class="radio-style-padding"><input formControlName="status" class=" form-check-input"
                            type="radio" [value]="'0'">
                        <label class=" form-check-label ">{{'PROJECTS.CREDITOR'|translate}}</label>
                    </div>
                    <div>
                        <input formControlName="status" class=" form-check-input" type="radio" [value]="1">
                        <label class=" form-check-label">{{'PROJECTS.DEBTOR'|translate}}</label>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="ol-12 col-md-6 form-group">
                <label>{{'PROJECTS.ESTIMATEDBUDGET'|translate}}</label>
                <input type="number" id="estimatedBudget" class="form-control" formControlName="estimatedBudget"
                    name="estimatedBudget">
                <ng-container *ngIf="form && form?.get('estimatedBudget')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('estimatedBudget')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-12 col-md-3 text-center  p-0 radio-style">
                <div class="mb-3">
                    <label for="isDetaileds" class="form-label">{{'PROJECTS.ISCONGREGATION'|translate}}</label>
                    <input type="radio" class=" form-check-input" id="isDetaileds" formControlName="isDetailed"
                        [value]="false" [attr.disabled]="form?.get('level')?.value < 4? '' : null">
                </div>
            </div>

            <div class="col-12 col-md-3 text-center  p-0 radio-style">
                <div class="mb-3">
                    <label for="isDetailed" class="form-label">{{'PROJECTS.ISDETAILED'|translate}}</label>
                    <input type="radio" class=" form-check-input" id="isDetailed" formControlName="isDetailed"
                        [value]="true" [attr.disabled]="form?.get('level')?.value < 4? '' : null"
                        (change)="onRadioChange(true)">
                </div>
            </div>
        </div>
        <div *ngIf="(form.errors?.['atLeastOneCheckboxRequired'] && form.get('isPrivate')?.dirty )|| (form.errors?.['atLeastOneCheckboxRequired'] && form.get('isCostCenter')?.dirty)"
            class="alert alert-danger d-flex align-items-center" role="alert">

            <span>{{'PROJECTS.ISPRIVATEISCOSTCENTERREQUIRED'|translate}}</span>
        </div>

        <div class="form-group">
            <label>{{'PROJECTS.NOTES'|translate}}</label>
            <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
            <ng-container *ngIf="form && form?.get('notes')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('notes')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'PROJECTS.EMAIL'|translate}}</label>
            <input type="text" id="email" class="form-control" formControlName="email" name="email">
            <ng-container *ngIf="form && form?.get('email')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
                <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('email')  ">
                    {{'SHARED.INVALIDEMAIL'|translate}}
                </p>
            </ng-container>
        </div>




    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" *ngIf="!isUpdate"
            (click)="onSubmit()">{{'PROJECTS.ADDPROJECT'|translate}}</button>
        <button class="btn btn-primary" type="submit" *ngIf="isUpdate"
            (click)="update()">{{'PROJECTS.UPDATEPROJECT'|translate}}</button>
    </div>
</form>
