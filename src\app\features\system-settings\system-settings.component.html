<div class="erp-card erp-shadow-end">
  <div class="row my-3 row px-3">
    <div [formGroup]="settingsForm">
      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.ASSETSACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="assetAccountControl"
            [selectedId]="settingsForm?.get('assetsAccountId')?.value"
            (selectedValue)="onSelectedAssetAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.DEDUCTACCOUNT' | translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="deductAccountControl"
            [selectedId]="settingsForm?.get('deductAccountId')?.value"
            (selectedValue)="onSelectedDeductAccount($event)">
          </app-reusable-dropdown>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.PROPHETACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="prophitAccountControl"
            [selectedId]="settingsForm?.get('prophitAccountId')?.value"
            (selectedValue)="onSelectedProphitAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.YEARPROPHETACCOUNT' | translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="yearProphitAccountControl"
            [selectedId]="settingsForm?.get('yearProphitAccountId')?.value"
            (selectedValue)="onSelectedYearProphitAccount($event)">
          </app-reusable-dropdown>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.INCOMINGACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="incomingAccountControl"
            [selectedId]="settingsForm?.get('incomingAccountId')?.value"
            (selectedValue)="onSelectedIncomingAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.COSTACCOUNT' | translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="costsAccountControl"
            [selectedId]="settingsForm?.get('costsAccountId')?.value"
            (selectedValue)="onSelectedCostsAccount($event)">
          </app-reusable-dropdown>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.OUTCOMEACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="outComeAccountControl"
            [selectedId]="settingsForm?.get('outComeAccountId')?.value"
            (selectedValue)="onSelectedOutComeAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.EXPENSESACCOUNT' | translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="expensesAccountControl"
            [selectedId]="settingsForm?.get('expensesAccountId')?.value"
            (selectedValue)="onSelectedExpensesAccount($event)">
          </app-reusable-dropdown>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.PURCHASESACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="purchasesAccountControl"
            [selectedId]="settingsForm?.get('purchasesAccountId')?.value"
            (selectedValue)="onSelectedPurchasesAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.BOXACCOUNT' | translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="boxAccountControl"
            [selectedId]="settingsForm?.get('boxAccountId')?.value"
            (selectedValue)="onSelectedBoxAccount($event)">
          </app-reusable-dropdown>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.SELLSACCOUNT'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="sellsAccountControl"
            [selectedId]="settingsForm?.get('sellsAccountId')?.value"
            (selectedValue)="onSelectedSellsAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'SYSTEMSETTINGS.BANKNUMBER'|translate }}</label>
          <input type="number" class="form-control" (change)="onBankNumberChange($event)" formControlName="bankNumber">
      </div>
      </div>

      <div class="row">
        <div class="row">
          <div class="col-md-6">
            <label>{{'SYSTEMSETTINGS.STARTDATE'|translate}}</label>
            <br />
            <nz-date-picker formControlName="startYear" class="datepicker"></nz-date-picker>
          </div>
          <div class="col-md-6">
            <label>{{'SYSTEMSETTINGS.ENDDATE'|translate}}</label>
            <br />
            <nz-date-picker formControlName="endYear" class="datepicker"></nz-date-picker>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="mt-3 flex-row gap-md-5">
            <label for="isHijri" class="form-label">{{'SYSTEMSETTINGS.ISHIJRI'|translate}}</label>
            <input
              type="checkbox"
              class="form-check-input"
              formControlName="isHijriDate"
              (change)="onCheckboxChange($event)"
            />
          </div>
        </div>
      </div>

      <div class="mx-auto col-md-4 justify-content-center">
        <button
          class="btn col-md-3 btn-primary w-100 w-md-20"
          type="submit"
          (click)="createOrUpdateSystemSettings()"
        >
          <span>{{ 'SYSTEMSETTINGS.CREATEORUPDATESETTINGS' | translate }}</span>
        </button>
      </div>

    </div>
  </div>
</div>
