import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AddEditSalesAccountComponent } from './add-edit-sales-account/add-edit-sales-account.component';
import { SalesAccountComponent } from './sales-account.component';
import { SalesAccountRoutingModule } from './sales-account-routing.module';
import { SharedModule } from '../../shared/shared.module';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    AddEditSalesAccountComponent,
    SalesAccountComponent,


  ],
    imports: [
        CommonModule,
        SalesAccountRoutingModule,
        SharedModule,
        ReusableDropdownComponent
    ]
})
export class SalesAccountModule { }
