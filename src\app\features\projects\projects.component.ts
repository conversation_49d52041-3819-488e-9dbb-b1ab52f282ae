import {ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { ProjectsService } from './services/projects.service';
import { TranslateService } from '@ngx-translate/core';
import { NzTreeComponent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddEditProjectComponent } from './add-edit-project/add-edit-project.component';
import {MessageService} from "primeng/api";
import {BranchService} from "../branches/branch.service";

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrl: './projects.component.scss'
})
export class ProjectsComponent implements OnInit {
  projectsList: any;

  showARDate: boolean = false;
  @ViewChild('nzTreeComponent', { static: false }) nzTreeComponent!: NzTreeComponent;
  @ViewChild('pdfTable', { static: false }) pdfTable!: ElementRef;
  selectedProject: any;
  child: any;
  currentDate: Date = new Date()
  currentRef: string
  flattenedProjectsList: any[] = [];
  constructor(private projects: ProjectsService, private modalService: NzModalService, private translate: TranslateService, private messageService: MessageService, private cd : ChangeDetectorRef, private branches: BranchService
  ) {
    this.getDirection();
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {

      this.getProjects();

      this.lang = langObject.lang;
      if (this.lang === 'ar') {
        this.showARDate = true;
      }
      else {
        this.showARDate = false;
      }
      this.getDirection()

    })
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;
  langDirection: 'ltr' | 'rtl' = 'ltr';
  addAccountModal() {
    if (this.selectedProject) {
      this.child = true
    }
    this.modalService.create({
      nzContent: AddEditProjectComponent,
      nzDirection: this.langDirection,
      nzData: { child: this.child, ...this.selectedProject },
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedProject = null;
        this.getProjects();
      }
    })
  }
  getDirection() {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';
    }
  }

  shouldShowAddButton(): boolean {
    // Show the button if no account is selected
    if (!this.selectedProject) {
      return true;
    }

    // Check if the selected account has isDetailed === true
    // Look in originalData first, then direct property
    const isDetailed = this.selectedProject.originalData?.isDetailed || this.selectedProject.isDetailed;

    // Hide button if isDetailed is true, show otherwise
    return isDetailed !== true;
  }

  mycheck(event: any, origin: any) {
    event.stopPropagation();

    // If the node is already checked, uncheck it (toggle behavior)
    if (origin.checked) {
      origin.checked = false;
      this.selectedProject = null;  // Deselect the project
    } else {
      // Uncheck all nodes first
      this.uncheckAllNodes();

      // Check the selected node
      origin.checked = true;
      this.selectedProject = origin;  // Set the selected project
    }

    // Ensure UI reflects changes
    this.cd.detectChanges();
  }

  uncheckAllNodes() {
    // Use the nzTreeComponent for projects
    this.nzTreeComponent.getTreeNodes().forEach(node => this.uncheckNodeRecursively(node));

    // Reset selectedProject when no nodes are selected
    this.selectedProject = null;
    this.cd.detectChanges();
  }

  uncheckNodeRecursively(node: any) {
    // Uncheck the node and its children
    node.origin.checked = false;
    if (node.children) {
      node.children.forEach((child: any) => this.uncheckNodeRecursively(child));
    }
  }

  uncheckOtherNodes(e: any) {
    e.children.forEach((c: any) => {
      c.checked = false;
      this.uncheckOtherNodes(c)
    })
  }


  updateProjectModal() {
    this.modalService.create({
      nzContent: AddEditProjectComponent,
      nzDirection: this.langDirection,
      nzData: this.selectedProject,
    }).afterClose.subscribe((res) => {
      if (res) {
        this.selectedProject = null;
        this.getProjects();
      }
    })

  }
  deleteProject() {

    this.projects.deleteProject(this.selectedProject.id).subscribe(res => {
      if (res.message == "Project deleted") {
        this.translate.get('TOAST.PROJECT_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
            })
        })
      }
      this.selectedProject = null;
      this.getProjects();
    })
  }
  transformToNzTreeNodeOptions(data: any): NzTreeNodeOptions[] {
    return data.map((node: any) => ({
      id: node.id, // map id to key
      disabled: false,
      checked: false,
      originalData: { ...node },
      children: this.transformToNzTreeNodeOptions(node.childProjects) // recursively transform children
    }));
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getProjects();
    });
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  getProjects() {
    this.projects.getProjects().subscribe(res => {
      this.projectsList = res.data

      this.projectsList.forEach((element: any) => {
        if (element.accountOpeningDate) {
          element.arabicAccountOpeningDate = new Date(element.accountOpeningDate).toLocaleDateString('ar-EG-u-nu-latn', { weekday: 'long', year: 'numeric', month: 'short', day: 'numeric' })
        }
      });
      this.projectsList = this.transformToNzTreeNodeOptions(this.projectsList)
      this.flattenedProjectsList = this.flattenProjects(res.data);
    })
  }

  flattenProjects(projects: any[]): any[] {
    let result: any[] = [];
    projects.forEach((project) => {
      result.push(project);
      if (project.childProjects && project.childProjects.length > 0) {
        result = result.concat(this.flattenProjects(project.childProjects));
      }
    });
    return result;
  }

}
