<div class="users-container">
  <div class="table-container erp-card erp-shadow-end">
    <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
    <h3 class="page-title">{{ "SIDEBAR.USERS" | translate }}</h3>

    <div class="table-controls">
      

      <div class="controls-row">
       
        <div class="search-input">
          <input type="text" id="search" class="form-control"
                 placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                 name="search" [(ngModel)]="searchValue">
        </div>
        <button (click)="addUserModal()" class="add-button">
          + {{'USERS.TABLE.ADDUSER' | translate}}
        </button>
      </div>
    </div>
    <nz-table #basicTable #sortTable [nzPageSize]="pageSize"
              [nzData]="usersList | tablefilters: {userName: searchValue, email:searchValue, phoneNumber:searchValue, isActive:searchValue}"
              nzTableLayout="fixed">
      <thead>
        <tr>
          <th nzColumnKey="name" [nzSortFn]="sortFnName">{{'USERS.TABLE.USERNAME'|translate}}</th>
          <th nzColumnKey="name" [nzSortFn]="sortFnEmail">{{'USERS.TABLE.EMAIL'|translate}}</th>
          <th nzColumnKey="name" [nzSortFn]="sortFnNumber">{{'USERS.TABLE.PHONENUMBER'|translate}}</th>
          <th nzColumnKey="name" [nzSortFn]="sortFnNActive">{{'USERS.TABLE.STATUS'|translate}}</th>
          <th nzColumnKey="name">{{'USERS.TABLE.ACTIONS'|translate}}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of basicTable.data">
          <td>{{ data.userName }}</td>
          <td>{{ data.email }}</td>
          <td>{{ data.phoneNumber}}</td>
          <td>
            <span *ngIf="data.isActive" class="status-badge active">
              {{'SHARED.ACTIVE'|translate}}
            </span>
            <span *ngIf="!data.isActive" class="status-badge inactive">
              {{'SHARED.INACTIVE'|translate}}
            </span>
          </td>
          <td>
            <div class="action-dropdown">
              <span class="dropdown-trigger" data-bs-toggle="dropdown" aria-expanded="false">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor"
                     class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                  <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                </svg>
              </span>
              <ul class="dropdown-menu">
                <li>
                  <button (click)="editUser(data)" class="erp-btn erp-btn-primary">
                    {{"USERS.DROPDOWN.EDITTENANTUSER"|translate}}
                  </button>
                </li>
                <li>
                  <button (click)="editPassword(data.email)" class="erp-btn erp-btn-babyblue">
                    {{"USERS.DROPDOWN.RESETPASSWORD"|translate}}
                  </button>
                </li>
                <li>
                  <button (click)="showModal(data.userId)" class="erp-btn erp-btn-gray">
                    {{"USERS.DROPDOWN.EDITUSERPERMISSIONS"|translate}}
                  </button>
                </li>
              </ul>
            </div>
          </td>
        </tr>
      </tbody>
    </nz-table>
     <div class="show-entries">
          <span>{{'SHARED.SHOW' | translate}}</span>
          <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="50">50</option>
          </select>
        </div>
  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h2>{{ 'PDF.USERSREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr class="text-blue-500">
        <th>{{'USERS.TABLE.USERNAME'|translate}}</th>
        <th>{{'USERS.TABLE.EMAIL'|translate}}</th>
        <th>{{'USERS.TABLE.PHONENUMBER'|translate}}</th>
        <th>{{'USERS.TABLE.STATUS'|translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let user of flattenedUsers">
        <td>{{ user.userName }}</td>
        <td>{{ user.email }}</td>
        <td>{{ user.phoneNumber}}</td>
        <td><button *ngIf=" user.isActive" class="btn btn-danger btn-sm text-white" disabled>
          {{'SHARED.ACTIVE'|translate}}</button>
          <button *ngIf="!user.isActive" class="btn btn-primary btn-sm text-white"
                  disabled>{{'SHARED.INACTIVE'|translate}}</button></td>
      </tr>
      </tbody>
    </table>

  </div>

    <!-- Export Section -->
    <div class="export-section">
      <app-export-pdf-button [tableElement]="pdfTable"></app-export-pdf-button>
    </div>
  </div>
</div>
