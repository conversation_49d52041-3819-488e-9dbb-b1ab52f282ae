import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CostComponent } from './cost.component';
import { SharedModule } from '../../shared/shared.module';
import { CostRoutingModule } from './cost-routing.module';
import { AddEditCostComponent } from './add-edit-cost/add-edit-cost.component';



@NgModule({
  declarations: [
    CostComponent,
    AddEditCostComponent
  ],
  imports: [
    CommonModule,
    CostRoutingModule,
    SharedModule
  ]
})
export class CostModule { }
