import {
  Component,
  Input,
  OnInit,
  output,
  OnDestroy,
  ChangeDetectorRef,
  OnChanges,
  SimpleChanges,
  NgZone
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup } from '@angular/forms';
import moment from 'moment-hijri';
import { CurrencyService } from '../../currencies/services/currency.service';
import { BondsService } from '../../bonds/services/bonds.service';
import { CostService } from '../../cost/services/cost.service';
import { ProjectsService } from '../../projects/services/projects.service';
import { LangChangeEvent, TranslateService } from '@ngx-translate/core';
import { Subject, forkJoin, takeUntil } from 'rxjs';
import { NgxSpinnerService } from "ngx-spinner";
import {MessageService} from "primeng/api";
import {filterDetailedCostCenters, filterDetailedProjects} from "../../../shared/utils/filters";
import {ActivityService} from "../../activity/services/activity.service";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-bond-form',
  templateUrl: './bond-form.component.html',
  styleUrl: './bond-form.component.scss'
})
export class BondFormComponent implements OnInit, OnDestroy, OnChanges  {
  getDataTable = output<boolean>();
  @Input() type: string;
  @Input() refNumber: number | undefined;
  form: FormGroup;
  currencyList: any;
  converssionRateValue: any;
  isCheck: boolean = false;
  isDebtorAdded: boolean = false;
  isCreditorAdded: boolean = false;
  accountsList: any;
  costList: any;
  projectList: any;
  activityList: any;
  showAddDebtorButton = true;
  showAddCreditorButton = false;
  lang: string = 'en';
  formAddCashFormat: FormGroup;
  isInitialized = false;
  translatedSave = '';
  private destroy$ = new Subject<void>();

  // Track loading states for debit and credit sections
  isDebtorLoading = false;
  isCreditorLoading = false;

  constructor(
    private fb: FormBuilder,
    private currency: CurrencyService,
    private bonds: BondsService,
    private cost: CostService,
    private projects: ProjectsService,
    private activity: ActivityService,
    private translate: TranslateService,
    private spinner: NgxSpinnerService,
    private cdr: ChangeDetectorRef,
    private messageService: MessageService,
    private ngZone: NgZone,
    private branches: BranchService
  ) {
    this.createForm();
    this.createFormFormat();

    this.translate.onLangChange
      .pipe(takeUntil(this.destroy$))
      .subscribe((langObject) => {
        this.lang = langObject.lang;
        this.loadData();
      });
    console.log(this.refNumber)
  }

  @Input() bondDataToEdit: any;

  ngOnInit(): void {
    this.branches.selectedBranchId$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.loadData();
      });

    // Initial translation
    this.translate.get('BONDS.SAVE').subscribe(translated => {
      this.translatedSave = translated || 'Save';
    });

    // Update when language changes
    this.translate.onLangChange.subscribe((event: LangChangeEvent) => {
      this.translatedSave = event.translations?.['BONDS.SAVE'] || 'Save';
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['refNumber'] && this.form) {
      this.form.get('refNumber')?.setValue(this.refNumber);
    }

    if (changes['bondDataToEdit'] && changes['bondDataToEdit'].currentValue && this.form) {
      this.patchFormWithEditData(changes['bondDataToEdit'].currentValue);
    }
  }

  private loadData() {
    this.spinner.show();

    // Use forkJoin to make parallel requests
    forkJoin({
      currencies: this.currency.getCurrencies(),
      accounts: this.bonds.getAccounts(),
      costs: this.cost.getCost(),
      projects: this.projects.getProjects(),
      activity: this.activity.getActivities()
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.currencyList = response.currencies.data.filter((item: any) => item.isActive == true);
          this.accountsList = response.accounts.data;
          this.activityList = response.activity.data
          this.costList = filterDetailedCostCenters(response.costs.data);
          this.projectList =  filterDetailedProjects(response.projects.data);
        },
        error: (error) => {
          console.error('Error loading data:', error);
        },
        complete: () => {
          this.isInitialized = true;
          this.spinner.hide();
        }
      });
  }

  onCheckboxChange() {
    this.isCheck = !this.isCheck;
  }

  private clearFormArrays(): void {
    this.debtors.clear();
    this.creditors.clear();
  }


  patchFormWithEditData(data: any) {
    if (!data) return;

    this.form.patchValue({
      refNumber: data.refNumber,
      amount: data.amount,
      checkNum: data.checkNum,
      checkDate: data.checkDate,
      bankName: data.bankName,
      currencyId: data.currencyId,
      conversionFactor: data.conversionFactor,
      movementDate: data.movementDate,
      hijriDate: data.hijriDate,
    });

    this.updateConversionRate();

    this.clearFormArrays();


    // Patch debtors
    (data.debtors || []).forEach((debtor: any, index: number) => {
      console.log(data.creditors)
      console.log(data.debtors)
      this.addDebit();
      this.debtors.at(index).patchValue({
        accountNumber: debtor.accountNumber,
        activityNumber: debtor.activityNumber,
        descriptionAr: debtor.descriptionAr,
        descriptionEn: debtor.descriptionEn,
        centerNumber: debtor.centerNumber,
        projectNumber: debtor.projectNumber,
        isDebitAccount: true
      });
    });

    // Patch creditors
    (data.creditors || []).forEach((creditor: any, index: number) => {
      this.addCredit();
      this.creditors.at(index).patchValue({
        accountNumber: creditor.accountNumber,
        activityNumber: creditor.activityNumber,
        descriptionAr: creditor.descriptionAr,
        descriptionEn: creditor.descriptionEn,
        centerNumber: creditor.centerNumber,
        projectNumber: creditor.projectNumber,
        isDebitAccount: false
      });
    });

    this.isDebtorAdded = data.debtors?.length > 0;
    this.isCreditorAdded = data.creditors?.length > 0;
    this.showAddDebtorButton = !this.isDebtorAdded;
    this.showAddCreditorButton = this.isDebtorAdded;
  }

  createForm() {
    this.form = this.fb.group({
      refNumber: [{value: this.refNumber, disabled: true}],
      amount: [''],
      checkNum: [null],
      checkDate: [null],
      bankName: [null],
      currencyId: [''],
      conversionFactor: [''],
      movementDate: [''],
      hijriDate: [''],
      debtors: this.fb.array([]),
      creditors: this.fb.array([])
    });
    this.dateHandler();
    console.log(this.refNumber)
  }

  createFormFormat() {
    this.formAddCashFormat = this.fb.group({
      format: ['']
    });
  }

  async addDebit() {
    if (!this.isDebtorAdded && !this.isDebtorLoading) {
      this.isDebtorLoading = true;
      this.spinner.show('debtorSpinner');

      try {
        const debtorGroup = this.fb.group({
          accountNumber: [''],
          activityNumber: [''],
          descriptionAr: [''],
          descriptionEn: [''],
          centerNumber: [''],
          projectNumber: [''],
          isDebitAccount: [true]
        });

        // Create and store form controls for the new debtor
        const newIndex = this.debtors.length;

        // Add the group to the form array
        this.debtors.push(debtorGroup);

        // Force change detection to update the view
        this.cdr.detectChanges();

        // Update flags after a brief delay to ensure DOM is ready
        await new Promise(resolve => setTimeout(resolve, 0));

        this.isDebtorAdded = true;
        this.showAddCreditorButton = true;
        this.showAddDebtorButton = false;
      } finally {
        this.showAddCreditorButton = true;
        this.isDebtorLoading = false;
        this.isDebtorAdded = true;
        this.spinner.hide('debtorSpinner');
        this.cdr.detectChanges();
      }
    }
  }

  async addCredit() {
    if (!this.isCreditorAdded && !this.isCreditorLoading) {
      this.isCreditorLoading = true;
      this.spinner.show('creditorSpinner');

      try {
        const creditorGroup = this.fb.group({
          accountNumber: [''],
          centerNumber: [''],
          projectNumber: [''],
          activityNumber: [''],
          descriptionAr: [''],
          descriptionEn: [''] ,
          isDebitAccount: [false]
        });

        // Create and store form controls for the new creditor
        const newIndex = this.creditors.length;

        // Add the group to the form array
        this.creditors.push(creditorGroup);

        await this.ngZone.run(() =>
          new Promise((resolve) => setTimeout(resolve, 1000))
        );

        this.cdr.detectChanges();

        // // Force change detection to update the view
        // this.cdr.detectChanges();

        // Update flags after a brief delay to ensure DOM is ready
        // await new Promise(resolve => setTimeout(resolve, 0));

        this.isCreditorAdded = true;
        this.showAddCreditorButton = true;
      } finally {
        this.isCreditorLoading = false;
        this.isCreditorAdded = true;
        this.spinner.hide('creditorSpinner');
        this.cdr.detectChanges();
      }
    }
  }

  // Getter methods for form arrays and controls
  get debtors(): FormArray {
    return this.form.get('debtors') as FormArray;
  }

  get creditors(): FormArray {
    return this.form.get('creditors') as FormArray;
  }

  // Form control getters for debtors
  getDebtorAccountControl(index: number): FormControl {
    return this.debtors.at(index).get('accountNumber') as FormControl;
  }

  getDebtorCostCenterControl(index: number): FormControl {
    return this.debtors.at(index).get('centerNumber') as FormControl;
  }

  getDebtorProjectControl(index: number): FormControl {
    return this.debtors.at(index).get('projectNumber') as FormControl;
  }

  getDebtorActivityControl(index: number): FormControl {
    return this.debtors.at(index).get('activityNumber') as FormControl;
  }

  // Form control getters for creditors
  getCreditorAccountControl(index: number): FormControl {
    return this.creditors.at(index).get('accountNumber') as FormControl;
  }

  getCreditorCostCenterControl(index: number): FormControl {
    return this.creditors.at(index).get('centerNumber') as FormControl;
  }

  getCreditorProjectControl(index: number): FormControl {
    return this.creditors.at(index).get('projectNumber') as FormControl;
  }

  getCreditorActivityControl(index: number): FormControl {
    return this.creditors.at(index).get('activityNumber') as FormControl;
  }

  // Currency control
  get currencyIdControl(): FormControl {
    return this.form.get('currencyId') as FormControl;
  }

  // Event handlers
  dateHandler() {
    if (this.form?.get('movementDate')?.value) {
      let date = moment(this.form?.get('movementDate')?.value).format('iYYYY-iMM-iDD');
      this.form.patchValue({
        hijriDate: date
      });
    }
  }

  onCurrencySelected(selected: any): void {
    this.form.patchValue({
      currencyId: selected.id,
    });
    this.updateConversionRate();
  }

  onSelectedAccount(index: number, type: 'debtor' | 'creditor', selected: any): void {
    const targetArray = type === 'debtor' ? this.debtors : this.creditors;
    targetArray.at(index).patchValue({
      accountNumber: selected.accountNumber
    });
  }

  onCostCenterSelected(index: number, type: 'debtor' | 'creditor', selected: any): void {
    const targetArray = type === 'debtor' ? this.debtors : this.creditors;
    targetArray.at(index).patchValue({
      centerNumber: selected.costCenterNumber.toString()
    });
  }

  onProjectSelected(index: number, type: 'debtor' | 'creditor', selected: any): void {
    const targetArray = type === 'debtor' ? this.debtors : this.creditors;
    targetArray.at(index).patchValue({
      projectNumber: selected.projectNumber.toString()
    });
  }

  onActivitySelected(index: number, type: 'debtor' | 'creditor', selected: any): void {
    const targetArray = type === 'debtor' ? this.debtors : this.creditors;
    targetArray.at(index).patchValue({
      activityNumber: selected.activityNumber.toString()
    });
  }


  updateConversionRate() {
    if (this.form?.get('currencyId')?.value) {
      this.currencyList.forEach((element: any) => {
        if (this.form?.get('currencyId')?.value == element.id)
          this.converssionRateValue = element.conversionRate;
      });
      this.form.patchValue({
        conversionFactor: this.converssionRateValue
      });
    }
  }

  submit() {
    const formValue = this.form.value;
    const formattedHijriDate = moment(this.form?.get('movementDate')?.value).format('iYYYY-iMM-iDD')

    const mainData = {
      refNumber: this.refNumber,
      amount: formValue.amount,
      currencyId: formValue.currencyId,
      conversionFactor: formValue.conversionFactor,
      movementDate: formValue.movementDate,
      hijriDate: formattedHijriDate,
      checkNum: formValue.checkNum,
      checkDate: formValue.checkDate,
      bankName: formValue.bankName,
    };

    const debtorsPayload = formValue.debtors.map((debtor: any) => ({
      ...mainData,
      accountNumber: debtor.accountNumber,
      descriptionAr: debtor.descriptionAr,
      descriptionEn: debtor.descriptionEn,
      centerNumber: debtor.centerNumber,
      activityNumber: debtor.activityNumber,
      projectNumber: debtor.projectNumber,
      isDebitAccount: true
    }));

    const creditorsPayload = formValue.creditors.map((creditor: any) => ({
      ...mainData,
      accountNumber: creditor.accountNumber,
      descriptionAr: creditor.descriptionAr,
      descriptionEn: creditor.descriptionEn,
      centerNumber: creditor.centerNumber,
      activityNumber: creditor.activityNumber,
      projectNumber: creditor.projectNumber,
      isDebitAccount: false
    }));

    const data: { [key: number]: any } = [];
    data[0] = debtorsPayload[0] || {};
    data[1] = creditorsPayload[0] || {};

    if (!this.form.valid) return;

    // ✅ Determine mode
    const isEditMode = !!this.bondDataToEdit;

    const request$ = isEditMode
      ? this.bonds.editReport(data, this.bondDataToEdit.id)
      : this.bonds.addReport(data, this.type);

    request$.subscribe(res => {
      if (
        res.message === "Reports created successfully" ||
        res.message === "Report updated successfully"
      ) {
        this.form.reset();
        this.form.markAsPristine();
        this.form.markAsUntouched();
        this.createForm();
        this.isDebtorAdded = false;
        this.isCreditorAdded = false;
        this.isDebtorLoading = false;
        this.isCreditorLoading = false;
        this.showAddDebtorButton = true;
        this.showAddCreditorButton = false;

        const toastKey = isEditMode ? 'TOAST.REPORT_UPDATED' : 'TOAST.REPORT_CREATED';

        this.translate.get(toastKey).subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }

      this.getDataTable.emit(true);
    });
  }

}
