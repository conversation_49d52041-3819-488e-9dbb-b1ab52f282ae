import {Component, OnInit} from '@angular/core';
import { UsersService } from './services/users.service';
import { IBaseResponse, Users } from '../../core/models';
import { NzModalService } from 'ng-zorro-antd/modal';
import { EditPermissionsComponent } from './edit-permissions/edit-permissions.component';
import { AddUserComponent } from './add-user/add-user.component';
import { EditPassowrdComponent } from './edit-passowrd/edit-passowrd.component';
import { TranslateService } from '@ngx-translate/core';
import { EditUserComponent } from './edit-user/edit-user.component';
@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrl: './users.component.scss'
})
export class UsersComponent implements OnInit{
  usersList: Users[];

  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;
  langDirection: 'ltr' | 'rtl' = 'ltr';
  flattenedUsers: any[] = [];
  currentDate: Date = new Date()
  currentRef: string


  constructor(private users: UsersService, private modalService: NzModalService, private translate: TranslateService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getUsers()
      this.getDirection(this.lang)

    })
  }

  ngOnInit(): void {
    this.getUsers();
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }


  getUsers() {
    this.users.getUsers().subscribe(res => {
      this.usersList = res.data;
      this.flattenedUsers = this.flattenUsers(res.data)

    })
  }
  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }

  sortFnName = (a: Users, b: Users) => {
    return a.userName.localeCompare(b.userName)
  }

  sortFnEmail = (a: Users, b: Users) => {
    return a.email.localeCompare(b.email)
  }
  sortFnNumber = (a: Users, b: Users) => {
    return a.phoneNumber - b.phoneNumber
  }
  sortFnNActive = (a: Users, b: Users) => {
    return a.isActive - b.isActive
  }

  showModal(id: string): void {
    this.modalService.create({
      nzTitle: 'Role Permissions',
      nzData: {
        id: id,
      },
      nzContent: EditPermissionsComponent
    });
  }
  addUserModal(): void {

    this.modalService.create({
      nzContent: AddUserComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res) {

        this.getUsers();
      }
    })
  }
  editUser(data: {}) {
    this.modalService.create({
      nzData: {
        data: data,
      },
      nzContent: EditUserComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res) {

        this.getUsers();
      }
    })
  }
  editPassword(email: string): void {
    this.modalService.create({
      nzData: {
        email: email,
      },
      nzContent: EditPassowrdComponent,
      nzDirection: this.langDirection
    });
  }

  flattenUsers(users: any[]): any[] {
    let result: any[] = [];
    users.forEach((user) => {
      result.push(user);
    });
    return result;
  }

}


