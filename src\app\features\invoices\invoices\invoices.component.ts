import { Component } from '@angular/core';
import { InvoiceService } from '../invoice.service';
import { Router } from '@angular/router';
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-invoices',
  templateUrl: './invoices.component.html',
  styleUrl: './invoices.component.scss'
})
export class InvoicesComponent {
  invoiceList: any;
  currentDate: Date = new Date()
  currentRef: string
  flattenedInvoices: any[] = [];
  constructor(private router: Router, private invoice: InvoiceService, private translate: TranslateService, private messageService: MessageService, private branches: BranchService
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getInvoices()

    })
  }

  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getInvoices();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;

  addInvoiceModal() {
    this.router.navigate(['/add-invoice']);

  }
  
  getInvoices() {
    this.invoice.getInvoice().subscribe((res: any) => {
      if (res.statusCode === 404 || !res.data || res.data.length === 0) {
        this.invoiceList = [];
        this.flattenedInvoices = [];
      } else {
        this.invoiceList = res.data;
        this.flattenedInvoices = this.flattenInvoices(res.data);
      }
    });
  }


  flattenInvoices(invoices: any[]): any[] {
    let result: any[] = [];
    invoices.forEach((invoice) => {
      result.push(invoice);
    });
    return result;
  }

  edit(data: any) {
    this.router.navigate(['/edit-invoice', data.id]);
  }
  delete(id: any) {
    this.invoice.deleteInvoice(id).subscribe(res => {
      if (res.message == "Invoice deleted") {
        this.translate.get('TOAST.INVOICE_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getInvoices();
    })
  }
}
