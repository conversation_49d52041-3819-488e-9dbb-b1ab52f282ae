import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';

@Injectable({
  providedIn: 'root'
})
export class TenantsService {

  constructor(private httpClient: HttpClient) { }
  getTenants() {
    return this.httpClient.get<IBaseResponse<any>>(`/Tenants/AllTenants`);
  }

  changeActivation(data: any) {
    return this.httpClient.post<IBaseResponse<any>>(`/Tenants/ChangeActivation`, data);
  }
  getModules() {
    return this.httpClient.get<IBaseResponse<any>>(`/ModulesAssignments/modules`);
  }

  editModules(data: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/ModulesAssignments/edit`, data);
  }
  getAssignedPermissions(id:any) {
    return this.httpClient.get<IBaseResponse<any>>(`/ModulesAssignments/tenant/${id}`);
  }

}
