import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class BondsService {
  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });

  }
  getAccounts() {
    if (this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>(`/Account/AccountsDrop?branchId=${this.branchId}`);
    }
    return this.httpClient.get<IBaseResponse<any>>(`/Account/AccountsDrop`);
  }
  addBondnumber(data: any) {
    return this.httpClient.post<IBaseResponse<any>>(`/FinancialReportsTypes`, data);
  }
  addReport(data: any,docType:any) {
    return this.httpClient.post<IBaseResponse<any>>(`/Reports/${docType}`, data);
  }
  getReports(doctype:any) {
    return this.httpClient.get<IBaseResponse<any>>(`/Reports?DocType=${doctype}`);
  }

  editReport(data: any, docType:any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Reports/${docType}`, data);
  }

  changeEntries(reportId: string) {
    return this.httpClient.post<IBaseResponse<any>>(`/Reports/PostFinancialReport/${reportId}`,null);
  }

  changeReportType(reportId: string) {
    return this.httpClient.post<IBaseResponse<any>>(`/Reports/ChangeToReport/${reportId}`,null);
  }

  gettingAllReports() {
    return this.httpClient.get<IBaseResponse<any>>(`/Reports`);
  }

}
