import {Component, OnInit} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {SystemSettingsService} from "./services/system-settings.service";
import {FormBuilder, FormControl, FormGroup} from "@angular/forms";
import {SystemSettings} from "../../core/models/system-settings";
import {BondsService} from "../bonds/services/bonds.service";
import {NgxSpinnerService} from "ngx-spinner";
import {forkJoin, Subject, takeUntil} from "rxjs";
import {MessageService} from "primeng/api";

@Component({
  selector: 'app-system-settings',
  templateUrl: './system-settings.component.html',
  styleUrl: './system-settings.component.scss'
})
export class SystemSettingsComponent implements OnInit {
  systemSettings: SystemSettings
  lang: string = 'en';
  settingsForm: FormGroup
  accountsList: []
  isInitialized = false;
  private destroy$ = new Subject<void>();


  constructor(private systemSettingsService: SystemSettingsService, private spinner: NgxSpinnerService ,private bonds: BondsService, private fb: FormBuilder , private translate: TranslateService, private messageService: MessageService) {
    this.initializeEmptyForm();

    this.translate.onLangChange
      .pipe(takeUntil(this.destroy$))
      .subscribe((langObject) => {
        this.lang = langObject.lang;
        this.loadData();
      });

  }

  ngOnInit() {
    this.loadData();
    this.getAccounts();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeEmptyForm() {
    this.settingsForm = this.fb.group({
      id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
      assetsAccountId: [''],
      assetsAccountNumber: [''],
      deductAccountId: [''],
      deductAccountNumber: [''],
      prophitAccountId: [''],
      prophitAccountNumber: [''],
      yearProphitAccountId: [''],
      yearProphitAccountNumber: [''],
      incomingAccountId: [''],
      incomingAccountNumber: [''],
      costsAccountId: [''],
      costsAccountNumber: [''],
      outComeAccountId: [''],
      outComeAccountNumber: [''],
      expensesAccountId: [''],
      expensesAccountNumber: [''],
      purchasesAccountId: [''],
      purchasesAccountNumber: [''],
      boxAccountId: [''],
      boxAccountNumber: [''],
      sellsAccountId: [''],
      sellsAccountNumber: [''],
      bankNumber: [''],
      isHijriDate: [false],
      startYear: [null],
      endYear: [null],
    });
  }

  private loadData() {
    this.spinner.show();

    // Use forkJoin to make parallel requests
    forkJoin({
      settings: this.systemSettingsService.getSystemSettings(),
      accounts: this.bonds.getAccounts()
    })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          this.systemSettings = response.settings.data;
          this.accountsList = response.accounts.data;
          this.updateFormWithSettings();
        },
        error: (error) => {
          console.error('Error loading data:', error);
        },
        complete: () => {
          this.isInitialized = true;
          this.spinner.hide();
        }
      });
  }

  // Getting Accounts dropdown List
  getAccounts() {
    this.bonds.getAccounts().subscribe(res => {
      this.accountsList = res.data;
    })
  }

  // Calling API For getting System Settings
  getSystemSettings () {
    this.systemSettingsService.getSystemSettings().subscribe(res => {
      this.systemSettings = res.data
    })
  }

  // Calling API for Creating or Updating System Settings
  private updateFormWithSettings() {
    if (!this.systemSettings) return;

    this.settingsForm.patchValue({
      id: this.systemSettings.id || '3fa85f64-5717-4562-b3fc-2c963f66afa6',
      assetsAccountId: this.systemSettings.assetsAccountId || '',
      assetsAccountNumber: this.systemSettings.assetsAccountNumber || '',
      deductAccountId: this.systemSettings.deductAccountId || '',
      deductAccountNumber: this.systemSettings.deductAccountNumber || '',
      prophitAccountId: this.systemSettings.prophitAccountId || '',
      prophitAccountNumber: this.systemSettings.prophitAccountNumber || '',
      yearProphitAccountId: this.systemSettings.yearProphitAccountId || '',
      yearProphitAccountNumber: this.systemSettings.yearProphitAccountNumber || '',
      incomingAccountId: this.systemSettings.incomingAccountId || '',
      incomingAccountNumber: this.systemSettings.incomingAccountNumber || '',
      costsAccountId: this.systemSettings.costsAccountId || '',
      costsAccountNumber: this.systemSettings.costsAccountNumber || '',
      outComeAccountId: this.systemSettings.outComeAccountId || '',
      outComeAccountNumber: this.systemSettings.outComeAccountNumber || '',
      expensesAccountId: this.systemSettings.expensesAccountId || '',
      expensesAccountNumber: this.systemSettings.expensesAccountNumber || '',
      purchasesAccountId: this.systemSettings.purchasesAccountId || '',
      purchasesAccountNumber: this.systemSettings.purchasesAccountNumber || '',
      boxAccountId: this.systemSettings.boxAccountId || '',
      boxAccountNumber: this.systemSettings.boxAccountNumber || '',
      sellsAccountId: this.systemSettings.sellsAccountId || '',
      sellsAccountNumber: this.systemSettings.sellsAccountNumber || '',
      bankNumber: this.systemSettings.bankNumber || '',
      isHijriDate: this.systemSettings.isHijriDate || false,
      startYear: this.systemSettings.startYear || null,
      endYear: this.systemSettings.endYear || null,
    });
  }

  // Calling API for Creating or Updating System Settings
  createOrUpdateSystemSettings () {
    if (this.settingsForm.valid) {
      let data = this.settingsForm?.getRawValue();
      this.systemSettingsService.createOrUpdateSystemSettings(data).subscribe(res => {
        if (res.message == "Settings created") {
          this.translate.get('TOAST.SETTINGS_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.getSystemSettings()
      })
    } else {
      console.log("form a7a")
      Object.values(this.settingsForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  // Custom Functions

  // Assets Account
  get assetAccountControl(): FormControl {
    return this.settingsForm?.get('assetsAccountId') as FormControl
  }

  onSelectedAssetAccount(selected: any) {
    this.settingsForm?.patchValue({
      assetsAccountId: selected.id,
      assetsAccountNumber: selected.accountNumber,
    })
  }

  // Deduct Account
  get deductAccountControl(): FormControl {
    return this.settingsForm?.get('deductAccountId') as FormControl
  }

  onSelectedDeductAccount(selected: any) {
    this.settingsForm?.patchValue({
      deductAccountId: selected.id,
      deductAccountNumber: selected.accountNumber,
    })
  }

  // Prophit Account
  get prophitAccountControl(): FormControl {
    return this.settingsForm?.get('prophitAccountId') as FormControl
  }

  onSelectedProphitAccount(selected: any) {
    this.settingsForm?.patchValue({
      prophitAccountId: selected.id,
      prophitAccountNumber: selected.accountNumber,
    })
  }

  // Year Prophit Account
  get yearProphitAccountControl(): FormControl {
    return this.settingsForm?.get('yearProphitAccountId') as FormControl
  }

  onSelectedYearProphitAccount(selected: any) {
    this.settingsForm.patchValue({
      yearProphitAccountId: selected.id,
      yearProphitAccountNumber: selected.accountNumber,
    })
  }

  // Incoming Account
  get incomingAccountControl(): FormControl {
    return this.settingsForm?.get('incomingAccountId') as FormControl
  }

  onSelectedIncomingAccount(selected: any) {
    this.settingsForm?.patchValue({
      incomingAccountId: selected.id,
      incomingAccountNumber: selected.accountNumber,
    })
  }

  // Costs Account
  get costsAccountControl(): FormControl {
    return this.settingsForm?.get('costsAccountId') as FormControl
  }

  onSelectedCostsAccount(selected: any) {
    this.settingsForm?.patchValue({
      costsAccountId: selected.id,
      costsAccountNumber: selected.accountNumber,
    })
  }

  // OutCome Account
  get outComeAccountControl(): FormControl {
    return this.settingsForm?.get('outComeAccountId') as FormControl
  }

  onSelectedOutComeAccount(selected: any) {
    this.settingsForm?.patchValue({
      outComeAccountId: selected.id,
      outComeAccountNumber: selected.accountNumber,
    })
  }

  // Expenses Account
  get expensesAccountControl(): FormControl {
    return this.settingsForm?.get('expensesAccountId') as FormControl
  }

  onSelectedExpensesAccount(selected: any) {
    this.settingsForm?.patchValue({
      expensesAccountId: selected.id,
      expensesAccountNumber: selected.accountNumber,
    })
  }

  // Purchases Account
  get purchasesAccountControl(): FormControl {
    return this.settingsForm?.get('purchasesAccountId') as FormControl
  }

  onSelectedPurchasesAccount(selected: any) {
    this.settingsForm?.patchValue({
      purchasesAccountId: selected.id,
      purchasesAccountNumber: selected.accountNumber,
    })
  }

  // Box Account
  get boxAccountControl(): FormControl {
    return this.settingsForm?.get('boxAccountId') as FormControl
  }

  onSelectedBoxAccount(selected: any) {
    this.settingsForm?.patchValue({
      boxAccountId: selected.id,
      boxAccountNumber: selected.accountNumber,
    })
  }

  // Sells Account
  get sellsAccountControl(): FormControl {
    return this.settingsForm?.get('sellsAccountId') as FormControl
  }

  onSelectedSellsAccount(selected: any) {
    this.settingsForm?.patchValue({
      sellsAccountId: selected.id,
      sellsAccountNumber: selected.accountNumber,
    })
  }

  // for is Hijri
  onCheckboxChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    this.settingsForm?.get('isHijriDate')?.setValue(checkbox.checked);
  }

  // bank number changes
  onBankNumberChange(event: Event) {
    const value = (event.target as HTMLInputElement).value.toString();
    this.settingsForm?.get('bankNumber')?.setValue(value);
  }

}
