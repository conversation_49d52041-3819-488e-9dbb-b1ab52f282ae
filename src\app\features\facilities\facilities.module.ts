import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FacilitiesComponent } from './facilities.component';
import { FacilitiesRoutingModule } from './facilities-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { AddEditFacilityComponent } from './add-edit-facility/add-edit-facility.component';



@NgModule({
  declarations: [
    FacilitiesComponent,
    AddEditFacilityComponent
  ],
  imports: [
    CommonModule,
    FacilitiesRoutingModule,
    SharedModule
  ]
})
export class FacilitiesModule { }
