import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InvoicesComponent } from './invoices/invoices.component';
import { AddEditInvoiceComponent } from './add-edit-invoice/add-edit-invoice.component';





const routes: Routes = [
    {
        path: 'invoices', component: InvoicesComponent
    }, {
        path: 'edit-invoice/:id', component: AddEditInvoiceComponent,
    },{
        path: 'add-invoice', component: AddEditInvoiceComponent,
    }];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})

export class InvoicesRoutingModule { }

