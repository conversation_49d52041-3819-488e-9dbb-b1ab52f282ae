import {ChangeDetectorRef, Component, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from "@angular/forms";
import {TranslateService} from "@ngx-translate/core";
import {ReportsService} from "../services/reports.service";
import {MenuItem} from "primeng/api";
import {dateRangeValidator} from "../../../shared/utils/validators";
import {Account, Report} from "../../../core/models/reports";

@Component({
  selector: 'app-estimated-budget-report',
  templateUrl: './estimated-budget-report.component.html',
  styleUrl: './estimated-budget-report.component.scss'
})
export class EstimatedBudgetReportComponent implements OnInit{
  form: FormGroup;
  lang: string;
  accountsList: Account[];
  breadcrumbItems: MenuItem[] = [];
  reportsList: Report[];
  currentDate: Date = new Date()
  currentRef: string

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private reports: ReportsService,
    private cdr: ChangeDetectorRef
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.translate
        .get(['SIDEBAR.REPORTS', 'REPORTS.ESTIMATEDBUDGETREPORT'])
        .subscribe((translations) => {
          this.breadcrumbItems = [
            { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
            { label: translations['REPORTS.ESTIMATEDBUDGETREPORT'] },
          ];
        });
    });
  }

  ngOnInit() {
    this.createForm();
    this.translate
      .get(['SIDEBAR.REPORTS', 'REPORTS.ESTIMATEDBUDGETREPORT'])
      .subscribe((translations) => {
        this.breadcrumbItems = [
          { label: translations['SIDEBAR.REPORTS'], routerLink: '/reports' },
          { label: translations['REPORTS.ESTIMATEDBUDGETREPORT'] },
        ];
      });
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }

  createForm() {
    this.form = this.fb.group({
      startDate: [null],
      endDate: [null]
    }, { validators: dateRangeValidator() });
  }

  onSubmit() {
    if (this.form.invalid) {
      return;
    }

    const formValue = {
      ...this.form.value,
    };

    this.reports.getEstimatedBudgetReport(formValue).subscribe((res: any) => {
      this.reportsList = res.data;
      this.cdr.detectChanges();
    });
  }
}
