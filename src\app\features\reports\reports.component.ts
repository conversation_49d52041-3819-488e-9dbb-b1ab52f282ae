import { Component } from '@angular/core';
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.scss'
})
export class ReportsComponent {
  lang: string = 'en'
  langDirection: string

  constructor(private translate: TranslateService) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDirection()

    })
  }

  getDirection() {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';
    }
  }
}
