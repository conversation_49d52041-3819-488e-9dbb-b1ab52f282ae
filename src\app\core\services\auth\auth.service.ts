import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map } from 'rxjs';
import { UserService } from '../user/user.service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private http: HttpClient, private user: UserService, private router: Router) { }
  isAuthenticated(): boolean {
    return !!this.token;
  }
  login(username: string, password: string) {

    const data = {
      username,
      password

    };
    // let options = {
    //   headers: new HttpHeaders({
    //     "ignoreNotification@client": "ignoreNotification"
    //   })
    // }
    return this.http.post<any>('/Auth/login', data)
      .pipe(map(res => {
        if (res.statusCode == 200) {
          const token = res.data.token;
          const user = res.data;
          this.user.set(user);
          localStorage.setItem('Token', token);


        }
        return res;
      }));
  }
  get token() {

    return this.user.token;
  }
  deleteToken() {
    localStorage.removeItem('Token');
  }
  logout() {
    this.deleteToken();
    this.user.delete();
    this.router.navigate(['/login'])

  }
}
