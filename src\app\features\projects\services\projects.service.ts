import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class ProjectsService {

  private branchId: string = '';


  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }


  addProject(data: any) {
    if (this.branchId){
      data.branchId = this.branchId;
      return this.httpClient.post<IBaseResponse<any>>(`/Projects`, data);
    } else {
      return this.httpClient.post<IBaseResponse<any>>(`/Projects`, data);
    }
  }
  getProjects() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Projects');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Projects?branchId=${this.branchId}`);
    }
  }
  updateProject(id: any, data: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Projects/${id}`, data);
  }
  deleteProject(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Projects/${id}`);
  }


}
