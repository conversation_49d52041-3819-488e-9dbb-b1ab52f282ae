import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { AuthService } from '../../services/auth/auth.service';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {MessageService} from "primeng/api";


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent  {

  form: FormGroup
  visible = false;

  inputType = 'password';
  constructor(private auth: AuthService, private router: Router, private fb: FormBuilder, private messageService: MessageService) {
    this.form = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    });
  }

  toggleVisibility() {
    if (this.visible) {
      this.inputType = 'password';
      this.visible = false;

    } else {
      this.inputType = 'text';
      this.visible = true;

    }
  }

  onSubmit() {
    if (this.form.valid) {
      const user = this.form.getRawValue();
      this.auth.login(user.username, user.password).subscribe(res => {
        if (res.statusCode != 200 || res.message == "Logged In Failed" || res.message == "Login failed") {
          this.messageService.add({ severity:'error', summary: 'Error', detail: res.message, life: 3000, });
        }

        if(res.data.role == "Master"){
          this.router.navigate(['/tenants']);

        }else{

          this.router.navigate(['']);
        }
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }


  }



}
