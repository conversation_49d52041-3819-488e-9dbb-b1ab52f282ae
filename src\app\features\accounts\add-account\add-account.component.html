<div *nzModalTitle><span *ngIf="!isUpdate">{{'ACCOUNT.ADDACCOUNT'|translate}}</span> <span
        *ngIf="isUpdate">{{'ACCOUNT.UPDATEACCOUNT'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNUMBER'|translate}}</label>
                    <input type="text" id="accountNumber" class="form-control" formControlName="accountNumber"
                        name="accountNumber">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.PARENTNUMBER'|translate}}</label>
                    <input type="text" id="parentAccountNum" class="form-control" formControlName="parentAccountNum"
                        name="parentAccountNum">
                </div>
            </div>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.ABBREVIATIONNAME'|translate}}</label>
            <input type="text" id="shortName" class="form-control" formControlName="shortName" name="shortName">
            <ng-container *ngIf="form && form?.get('shortName')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('shortName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME'|translate}}</label>
            <input type="text" id="shortName" class="form-control" formControlName="accountName" name="accountName">
            <ng-container *ngIf="form && form?.get('accountName')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('accountName')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAMEEN'|translate}}</label>
            <input type="text" id="shortName" class="form-control" formControlName="account_Name_Latin"
                name="account_Name_Latin">
            <ng-container *ngIf="form && form?.get('account_Name_Latin')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="form && form?.get('account_Name_Latin')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTOPENINGDATE'|translate}}</label>
                    <br />
                    <nz-date-picker (ngModelChange)="dateHandler()" formControlName="accountOpeningDate"
                        class="datepicker"></nz-date-picker>
                    <ng-container *ngIf="form && form?.get('accountOpeningDate')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('accountOpeningDate')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.ACCOUNTOPENINGDATE'|translate}}
                        ({{'SHARED.HIJRI'|translate}})</label>
                    <input [value]="form?.get('accountOpeningDatehijri')?.value |arabicNumerals" [attr.disabled]="true"
                        class="form-control" type="text" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 col-md-6">
                <div class="form-group">
                    <label>{{ 'ACCOUNT.ADDACCOUNTFORM.CURRENCY' | translate }}</label>
                    <app-reusable-dropdown
                      [options]="currencyList"
                      [displayKey]="lang == 'en' ? 'currencyNameEn - currencyCode' : lang == 'ar' ? 'currencyNameAr - currencyCode' : 'currencyNameAr - currencyCode'"
                      [valueKey]="'id'"
                      [formControl]="currencyIdControl"
                      [selectedId]="form?.get('currencyId')?.value"
                      (selectedValue)="onCurrencySelected($event)">
                    </app-reusable-dropdown>
                    <ng-container *ngIf="form && form?.get('currencyId')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('currencyId')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>
            <div class="col-12 col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.CURRENCYRATE'|translate}}</label>
                    <input type="text" id="conversionRate" class="form-control" formControlName="conversionRate"
                        name="conversionRate">
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                  <label>{{ 'ACCOUNT.ADDACCOUNTFORM.RELATEDACCOUNT'|translate }}</label>
                  <app-reusable-dropdown
                    [options]="accountsList"
                    [displayKey]="'accountName - accountNumber'"
                    [valueKey]="'id'"
                    [formControl]="accountNameControl"
                    [selectedId]="form?.get('relatedAccount')?.value"
                    (selectedValue)="onSelectedAccount($event)">
                  </app-reusable-dropdown>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.CREDITLIMITS'|translate}}</label>
                    <input type="number" id="creditLimit" class="form-control" formControlName="creditLimit"
                        name="creditLimit">
                    <ng-container *ngIf="form && form?.get('creditLimit')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('creditLimit')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>

        </div>

        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.ESTIMATEDBUDGET'|translate}}</label>
            <input type="number" id="estimatedBudget" class="form-control" formControlName="estimatedBudget"
                name="estimatedBudget">
            <ng-container *ngIf="form && form?.get('estimatedBudget')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('estimatedBudget')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.OPENINGBALANCEBYINFOREIGNCURRENCY'|translate}}</label>
            <input type="number" id="openingBalanceByInForeignCurrency" class="form-control"
                formControlName="openingBalanceByInForeignCurrency" name="openingBalanceByInForeignCurrency">
            <ng-container *ngIf="form && form?.get('openingBalanceByInForeignCurrency')?.dirty">
                <p class="text-danger text-error"
                    *ngIf="form && form?.get('openingBalanceByInForeignCurrency')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>

        <div class="row">
            <div class="col-12 col-md-4">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.CURRENTBALANCE'|translate}}</label>
                    <input type="number" id="openingBalance" class="form-control" formControlName="openingBalance"
                        name="openingBalance">
                    <ng-container *ngIf="form && form?.get('openingBalance')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('openingBalance')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>
            <div class="col-12 col-md-4">
                <div class="form-group">
                    <label>{{'ACCOUNT.ADDACCOUNTFORM.CURRENTBALANCEDATE'|translate}}</label>
                    <br />
                    <nz-date-picker formControlName="openingBalanceDate" class="datepicker"></nz-date-picker>
                    <ng-container *ngIf="form && form?.get('openingBalanceDate')?.dirty">
                        <p class="text-danger text-error"
                            *ngIf="form && form?.get('openingBalanceDate')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container>
                </div>
            </div>


            <div class="col-12 col-md-4 radio-style">
                <div class="d-flex">
                    <div class="radio-style-padding"><input formControlName="status" class=" form-check-input"
                            type="radio" [value]="'0'">
                        <label class=" form-check-label ">{{'ACCOUNT.ADDACCOUNTFORM.CREDITOR'|translate}}</label>
                    </div>
                    <div>
                        <input formControlName="status" class=" form-check-input" type="radio" [value]="1">
                        <label class=" form-check-label">{{'ACCOUNT.ADDACCOUNTFORM.DEBTOR'|translate}}</label>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-12 col-md-3 text-center pr-0">
                <div class="mb-3">
                    <label for="isPrivate" class="form-label">{{'ACCOUNT.ADDACCOUNTFORM.ISPRIVATE'|translate}}</label>
                    <input type="checkbox" class=" form-check-input" formControlName="isPrivate" value="true">

                </div>
            </div>

            <div class="col-12 col-md-3 text-center p-0">
                <div class="mb-3">
                    <label for="isCostCenter"
                        class="form-label">{{'ACCOUNT.ADDACCOUNTFORM.ISCOSTCENTER'|translate}}</label>
                    <input type="checkbox" class=" form-check-input" formControlName="isCostCenter" value="true">

                </div>
            </div>

            <div class="col-12 col-md-3 text-center  p-0">
                <div class="mb-3">
                    <label for="isDetaileds"
                        class="form-label">{{'ACCOUNT.ADDACCOUNTFORM.ISCONGREGATION'|translate}}</label>
                    <input type="radio" class=" form-check-input" id="isDetaileds" formControlName="isDetailed"
                        [value]="false" [attr.disabled]="form?.get('level')?.value < 5? '' : null">
                </div>
            </div>

            <div class="col-12 col-md-3 text-center  p-0">
                <div class="mb-3">
                    <label for="isDetailed" class="form-label">{{'ACCOUNT.ADDACCOUNTFORM.ISDETAILED'|translate}}</label>
                    <input type="radio" class=" form-check-input" id="isDetailed" formControlName="isDetailed"
                        [value]="true" [attr.disabled]="form?.get('level')?.value < 5? '' : null"
                        (change)="onRadioChange(true)">
                </div>
            </div>
        </div>
        <div *ngIf="(form.errors?.['atLeastOneCheckboxRequired'] && form.get('isPrivate')?.dirty )|| (form.errors?.['atLeastOneCheckboxRequired'] && form.get('isCostCenter')?.dirty)"
            class="alert alert-danger d-flex align-items-center" role="alert">

            <span>{{'ACCOUNT.ADDACCOUNTFORM.ISPRIVATEISCOSTCENTERREQUIRED'|translate}}</span>
        </div>

        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.COMMENTS'|translate}}</label>
            <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
            <ng-container *ngIf="form && form?.get('notes')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('notes')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.EMAIL'|translate}}</label>
            <input type="text" id="email" class="form-control" formControlName="email" name="email">
            <ng-container *ngIf="form && form?.get('email')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
                <p class="text-danger text-error" *ngIf="form && form?.get('email')?.hasError('email')  ">
                    {{'SHARED.INVALIDEMAIL'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'ACCOUNT.ADDACCOUNTFORM.VATREGISTER'|translate}}</label>
            <input type="text" id="vatRegister" class="form-control" formControlName="vatRegister" name="vatRegister">
            <ng-container *ngIf="form && form?.get('vatRegister')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('vatRegister')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>




    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" *ngIf="!isUpdate"
            (click)="onSubmit()">{{'ACCOUNT.ADDACCOUNT'|translate}}</button>
        <button class="btn btn-primary" type="submit" *ngIf="isUpdate"
            (click)="update()">{{'ACCOUNT.UPDATEACCOUNT'|translate}}</button>
    </div>
</form>
