import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { ProjectsService } from '../services/projects.service';
import moment from 'moment-hijri';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";

@Component({
  selector: 'app-add-edit-project',
  templateUrl: './add-edit-project.component.html',
  styleUrl: './add-edit-project.component.scss'
})
export class AddEditProjectComponent {

  form: FormGroup;
  data: any
  currencyList: any;

  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  isUpdate: boolean = false;
  ischild: any;
  parentProjectId: any;
  childLevel: any;
  converssionRateValue: any;

  constructor(private modal: NzModalRef, private fb: FormBuilder, private project: ProjectsService, private translate: TranslateService, private messageService: MessageService

  ) {

  }
  ngOnInit() {
    this.createFormAdd();

  }

  dateHandler() {

    if (this.form?.get('accountOpeningDate')?.value) {

      let date = moment(this.form?.get('accountOpeningDate')?.value).format('iD iMMMM iYYYY')

      this.form.patchValue({
        accountOpeningDatehijri: date
      });
    }
  }

  createFormAdd() {
    this.ischild = this.nzModalData?.child;
    if (this.ischild) {

      this.parentProjectId = this.nzModalData?.id;
      this.childLevel = this.nzModalData?.originalData?.level + 1;
    }

    if (!this.ischild) {
      this.data = this.nzModalData?.originalData;
      if (this.data) {
        this.isUpdate = true;
      }
    }
    let level = this.data?.level || this.childLevel || null
    let status = this.data?.status == 0 ? '0' : 1
    this.form = this.fb.group({
      shortName: [this.data?.shortName || '', Validators.required],

      projectName: [this.data?.projectName || '', Validators.required],
      project_Latin_Name: [this.data?.project_Latin_Name || '', Validators.required],
      accountOpeningDate: [this.data?.accountOpeningDate || '', Validators.required],
      creditLimit: [this.data?.creditLimit || ''],
      openingBalance: [this.data?.openingBalance || ''],
      openingBalanceDate: [this.data?.openingBalanceDate || ''],
      estimatedBudget: [this.data?.estimatedBudget || '', [Validators.required, Validators.min(0)]],
      notes: [this.data?.notes || '',],
      email: [this.data?.email || '', [Validators.email]],
      status: [status || ''],
      level: [level || '1'],
      isDetailed: [this.data?.isDetailed || false],
      parentProjectId: [this.parentProjectId || this.data?.parentProjectId || null], // Set parentProjectId to selected id
      accountOpeningDatehijri: [],
      projectNumber: [this.data?.projectNumber],
      parentProjectNumber: [ this.nzModalData?.originalData?.projectNumber || this.data?.projectNumber]
    });
    this.dateHandler();


    this.form.controls['parentProjectNumber'].disable();
    this.form.controls['projectNumber'].disable();
  }

  destroyModal(): void {
    this.modal.destroy();
  }

  onRadioChange(value: boolean) {
    this.form.get('isDetailed')?.setValue(value);
  }

  onSubmit() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      this.project.addProject(data).subscribe(res => {
        if (res.message == "Project created") {
          this.translate.get('TOAST.PROJECT_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  update() {

    if (this.form.valid) {
      let dataForm = this.form.getRawValue();
      dataForm == '0' ? 0 : dataForm
      this.project.updateProject(this.data.id, dataForm).subscribe(res => {
        if (res.message == "Project updated") {
          this.translate.get('TOAST.PROJECT_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
