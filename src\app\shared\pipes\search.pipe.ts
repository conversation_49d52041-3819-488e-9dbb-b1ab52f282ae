import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'tablefilters'
})
export class TablefiltersPipe implements PipeTransform {

  transform(items: any, filter: any, defaultFilter: boolean = false): any {
    if (!filter) {
      return items;
    }

    if (!Array.isArray(items)) {
      return items;
    }

    if (filter && Array.isArray(items)) {
      let filterKeys = Object.keys(filter);

      return items.filter(item => {
        return filterKeys.every((keyName) => {
          const filterValue = filter[keyName];

          // Special logic for isChanged
          if (keyName === 'isChanged') {
            if (filterValue === null || filterValue === '') return true;
            return item[keyName] === filterValue;
          }

          // For text search filters
          if (filterValue === null || filterValue === '') return true;
          return new RegExp(filterValue, 'gi').test(item[keyName]);
        });
      });
    }

    return items;
  }
}
