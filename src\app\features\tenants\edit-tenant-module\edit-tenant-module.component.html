<div *ngFor="let item of moduleList">
    <input class="form-check-input" type="checkbox" [id]="'checkbox-' + item.id" [(ngModel)]="item.isChecked"
        [checked]="item.isChecked"> {{item.name}}
</div>
<div *nzModalFooter>
    <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
    <button class="btn btn-primary" type="submit" (click)="onSubmit(moduleList)">{{'SHARED.UPDATE'|translate}}</button>
</div>