import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class FacilitiesService {

  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }
  getFacilities() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Facilities');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Facilities?branchId=${this.branchId}`);
    }
  }

  addFacilities(data: any) {
    if (this.branchId) {
      if (data instanceof FormData) {
        data.append('branchId', this.branchId);
      } else {
        data.branchId = this.branchId;
      }
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Facilities`, data);
  }
  deleteFacilities(id:any){
    return this.httpClient.delete<IBaseResponse<any>>(`/Facilities/${id}`);
  }
  updateFacility(id:any,data:any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Facilities/${id}`,data);
  }
}
