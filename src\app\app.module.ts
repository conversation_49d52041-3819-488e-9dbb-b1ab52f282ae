import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

import { ar_EG, NZ_I18N } from 'ng-zorro-antd/i18n';
import { en_US } from 'ng-zorro-antd/i18n';
import { registerLocaleData } from '@angular/common';
import en from '@angular/common/locales/en';
import ar from '@angular/common/locales/ar';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient } from '@angular/common/http';
import { CoreModule } from './core/core.module';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { SharedModule } from './shared/shared.module';
import { NgxSpinnerModule } from 'ngx-spinner';
import { LoaderInterceptor } from './core/interceptors/loader.interceptor';
import {ToastModule} from "primeng/toast";
import {MessageService} from "primeng/api";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {RegisterComponent} from "./core/components/register/register.component";

registerLocaleData(en);
registerLocaleData(ar);
// AoT requires an exported function for factories
export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http);
}

@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    CoreModule,
    RegisterComponent,
    SharedModule,
    NgxSpinnerModule,
    BreadcrumbModule,
    ToastModule,
    // NgxSpinnerModule.forRoot({ type: 'ball-spin-fade' }),
    TranslateModule.forRoot(
      {
        loader: {
          provide: TranslateLoader,
          useFactory: HttpLoaderFactory,
          deps: [HttpClient]
        }
      }
    )

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [MessageService, {provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true},
    {
    provide: NZ_I18N,
    useFactory: (localId: string) => {
      switch (localId) {
        case 'en':
          return en_US;
        /* keep the same with angular.json/i18n/locales configuration */
        case 'ar_EG':
          return ar_EG;
        default:
          return en_US;
      }
    },
    deps: [LOCALE_ID]
  },
  // { provide: NZ_I18N, useValue: en_US },
   provideAnimationsAsync(),
  provideHttpClient()
],

  bootstrap: [AppComponent]
})
export class AppModule { }
