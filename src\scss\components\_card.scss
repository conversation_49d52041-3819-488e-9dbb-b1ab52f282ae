.erp-card {
    border: 1px solid #F9F0FF;
    border-radius: 13px;
    z-index: 2;
    background: #fff;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }
}

/* Enhanced card styles */
.enhanced-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8e9f3;
    transition: box-shadow 0.3s ease;
    overflow: hidden;

    &:hover {
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    @media (max-width: 768px) {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    .card-header {
        background: linear-gradient(135deg, #fafbff 0%, #f0f4ff 100%);
        border-bottom: 1px solid #e8e9f3;
        padding: 1.5rem;

        @media (max-width: 768px) {
            padding: 1rem;
        }

        .card-title {
            color: var(--color-primary);
            font-weight: 600;
            font-size: 1.5rem;
            margin: 0;

            @media (max-width: 768px) {
                font-size: 1.3rem;
            }
        }

        .card-subtitle {
            color: var(--color-grey);
            font-size: 1rem;
            margin: 0.5rem 0 0 0;
        }
    }

    .card-body {
        padding: 1.5rem;

        @media (max-width: 768px) {
            padding: 1rem;
        }
    }

    .card-footer {
        background: #fafbff;
        border-top: 1px solid #e8e9f3;
        padding: 1rem 1.5rem;

        @media (max-width: 768px) {
            padding: 0.75rem 1rem;
        }
    }
}

/* Page container styles */
.page-container {
    padding: 1.5rem;
    min-height: 100vh;
    background: #f8fafc;

    @media (max-width: 768px) {
        padding: 1rem;
    }

    .page-header {
        margin-bottom: 2rem;

        @media (max-width: 768px) {
            margin-bottom: 1.5rem;
        }

        .page-title {
            color: var(--color-primary);
            font-weight: 600;
            font-size: 2rem;
            margin: 0 0 0.5rem 0;

            @media (max-width: 768px) {
                font-size: 1.5rem;
            }
        }

        .page-description {
            color: var(--color-grey);
            font-size: 1rem;
            margin: 0;
        }
    }
}