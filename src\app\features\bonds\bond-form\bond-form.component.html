<div class="erp-card erp-shadow-end">
    <div class="row my-3 row px-3">
        <div [formGroup]="form">
            <div class="row">

              <div class="col-md-4">
                <div class="form-group">
                  <label>{{'BONDS.REFNUMBER'|translate}}</label>
                  <input type="number" id="refNumber" class="form-control" formControlName="refNumber" name="refNumber" [value]="form?.get('refNumber')?.value" [attr.disabled]="true">
                </div>
              </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'BONDS.DATE'|translate}}</label>
                        <br />
                        <nz-date-picker (ngModelChange)="dateHandler()" formControlName="movementDate"
                            class="datepicker"></nz-date-picker>
                        <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                            <p class="text-danger text-error"
                                *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                                {{'SHARED.THISFIELDISREQUIRED'|translate}}
                            </p>
                        </ng-container>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'BONDS.DATEHIJRI'|translate}}</label>
                        <input [value]="form?.get('hijriDate')?.value |arabicNumerals" [attr.disabled]="true"
                            class="form-control" type="text" />
                    </div>
                </div>
            </div>

            <hr />
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'BONDS.AMOUNT'|translate}}</label>
                        <input type="number" id="amount" class="form-control" formControlName="amount" name="amount">
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                      <label>{{ 'ACCOUNT.ADDACCOUNTFORM.CURRENCY' | translate }}</label>
                      <app-reusable-dropdown
                        [options]="currencyList"
                        [displayKey]="lang == 'en' ? 'currencyNameEn - currencyCode' : lang == 'ar' ? 'currencyNameAr - currencyCode' : 'currencyNameAr - currencyCode'"
                        [valueKey]="'id'"
                        [formControl]="currencyIdControl"
                        [selectedId]="form?.get('currencyId')?.value"
                        (selectedValue)="onCurrencySelected($event)">
                      </app-reusable-dropdown>
                        <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                        <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container> -->
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label>{{'BONDS.CONVERSIONRATE'|translate}}</label>
                        <input type="text" id="conversionFactor" class="form-control" formControlName="conversionFactor" [disabled]="true"
                            name="conversionFactor">
                        <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container> -->
                    </div>
                </div>
            </div>
            <div *ngIf="type !='B5AD7DF1-7E88-4FC1-97DC-2A7A94337EFC'">
                <hr />
                <label for="check">{{'BONDS.ISCHECK'|translate}}</label>
                <input type="checkbox" class="mx-3" (change)="onCheckboxChange()" id="check">
            </div>

            <div [hidden]="!isCheck" class="row">
                <div class="mb-3 col-md-6">
                    <label class="form-label">{{'BONDS.CHECKNUMBER'|translate}}</label>
                    <input type="text" formControlName="checkNum" class="form-control">
                </div>
                <div class="mb-3 col-md-6">
                    <label class="form-label">{{'BONDS.BANKNAME'|translate}}</label>
                    <input type="text" formControlName="bankName" class="form-control">
                </div>
                <div class="mb-3 col-md-6">
                    <label class="form-label">{{'BONDS.CHECKDATE'|translate}}</label>

                    <br />
                    <nz-date-picker  formControlName="checkDate"
                        class="datepicker"></nz-date-picker>

                </div>
            </div>
            <hr />
            <button
              class="btn btn-primary"
              *ngIf="!isDebtorAdded && !isDebtorLoading"
              (click)="addDebit()">
              {{'BONDS.ADDDEBIT'|translate}}
            </button>
            <div formArrayName="debtors">
                <div *ngFor="let debtor of debtors.controls; let i = index" [formGroupName]="i" class="row">
                    <p>{{'BONDS.DEBIT'|translate}}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                              <label>{{ 'BONDS.ACCOUNTNUMBER'|translate }}</label>
                              <app-reusable-dropdown
                                [options]="accountsList"
                                [displayKey]="'accountName - accountNumber'"
                                [valueKey]="'accountNumber'"
                                [formControl]="getDebtorAccountControl(i)"
                                [selectedId]="debtor?.get('accountNumber')?.value"
                                (selectedValue)="onSelectedAccount(i, 'debtor' ,$event)">
                              </app-reusable-dropdown>
                                <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                                <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                            <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                                {{'SHARED.THISFIELDISREQUIRED'|translate}}
                            </p>
                        </ng-container> -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                              <label>{{ 'BONDS.COSTCENTER'|translate }}</label>
                              <app-reusable-dropdown
                                [options]="costList"
                                [displayKey]="'costCenterName - costCenterNumber'"
                                [valueKey]="'costCenterNumber'"
                                [formControl]="getDebtorCostCenterControl(i)"
                                [selectedId]="debtor?.get('centerNumber')?.value"
                                (selectedValue)="onCostCenterSelected(i, 'debtor', $event)">
                              </app-reusable-dropdown>
                                <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                                <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                            <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                                {{'SHARED.THISFIELDISREQUIRED'|translate}}
                            </p>
                        </ng-container> -->
                            </div>
                        </div>
                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="form-group">
                          <label>{{ 'BONDS.PROJECT' | translate }}</label>
                          <app-reusable-dropdown
                            [options]="projectList"
                            [displayKey]="'projectName - projectNumber'"
                            [valueKey]="'projectNumber'"
                            [formControl]="getDebtorProjectControl(i)"
                            [selectedId]="debtor?.get('projectNumber')?.value"
                            (selectedValue)="onProjectSelected(i, 'debtor' ,$event)">
                          </app-reusable-dropdown>
                          <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                          <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                      <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                          {{'SHARED.THISFIELDISREQUIRED'|translate}}
                      </p>
                  </ng-container> -->
                        </div>
                    </div>
                      <div class="col-md-6">
                          <div class="form-group">
                            <label>{{ 'BONDS.ACTIVITY' | translate }}</label>
                            <app-reusable-dropdown
                              [options]="activityList"
                              [displayKey]="'activityName - activityNumber'"
                              [valueKey]="'activityNumber'"
                              [formControl]="getDebtorActivityControl(i)"
                              [selectedId]="debtor.get('activityNumber')?.value"
                              (selectedValue)="onActivitySelected(i, 'debtor' ,$event)">
                            </app-reusable-dropdown>
                            <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                            <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                        <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                            {{'SHARED.THISFIELDISREQUIRED'|translate}}
                        </p>
                    </ng-container> -->
                          </div>
                      </div>
                    <div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label">{{'BONDS.EXPLANATIONAR'|translate}}</label>
                            <textarea formControlName="descriptionAr" class="form-control"></textarea>
                        </div>

                        <div class="mb-3 col-md-12">
                            <label class="form-label">{{'BONDS.EXPLANATIONEN'|translate}}</label>
                            <textarea formControlName="descriptionEn" class="form-control"></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <button
              class="btn btn-primary"
              *ngIf="isDebtorAdded && !isCreditorAdded && !isCreditorLoading && showAddCreditorButton"
              (click)="addCredit()">
              {{'BONDS.ADDCREDIT'|translate}}
            </button>
            <div formArrayName="creditors">
                <div *ngFor="let creditor of creditors.controls; let i = index" [formGroupName]="i" class="row">
                    <p>{{'BONDS.CREDIT'|translate}}</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                              <label>{{ 'BONDS.ACCOUNTNUMBER'|translate }}</label>
                              <app-reusable-dropdown
                                [options]="accountsList"
                                [displayKey]="'accountName - accountNumber'"
                                [valueKey]="'accountNumber'"
                                [formControl]="getCreditorAccountControl(i)"
                                [selectedId]="creditor.get('accountNumber')?.value"
                                (selectedValue)="onSelectedAccount(i,'creditor',$event)">
                              </app-reusable-dropdown>
                                <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                                <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                                <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                                </p>
                            </ng-container> -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                              <label>{{ 'BONDS.COSTCENTER'|translate }}</label>
                              <app-reusable-dropdown
                                [options]="costList"
                                [displayKey]="'costCenterName - costCenterNumber'"
                                [valueKey]="'costCenterNumber'"
                                [formControl]="getCreditorCostCenterControl(i)"
                                [selectedId]="creditor?.get('centerNumber')?.value"
                                (selectedValue)="onCostCenterSelected(i, 'creditor' ,$event)">
                              </app-reusable-dropdown>
                                <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                                <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                                <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                                </p>
                            </ng-container> -->
                            </div>
                        </div>

                    </div>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="form-group">
                          <label>{{ 'BONDS.PROJECT' | translate }}</label>
                          <app-reusable-dropdown
                            [options]="projectList"
                            [displayKey]="'projectName - projectNumber'"
                            [valueKey]="'projectNumber'"
                            [formControl]="getCreditorProjectControl(i)"
                            [selectedId]="creditor?.get('projectNumber')?.value"
                            (selectedValue)="onProjectSelected(i, 'creditor',$event)">
                          </app-reusable-dropdown>
                          <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                          <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                          <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                              {{'SHARED.THISFIELDISREQUIRED'|translate}}
                          </p>
                      </ng-container> -->
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="form-group">
                          <label>{{ 'BONDS.ACTIVITY' | translate }}</label>
                          <app-reusable-dropdown
                            [options]="activityList"
                            [displayKey]="'activityName - activityNumber'"
                            [valueKey]="'activityNumber'"
                            [formControl]="getCreditorActivityControl(i)"
                            [selectedId]="creditor?.get('activityNumber')?.value"
                            (selectedValue)="onActivitySelected(i, 'creditor' ,$event)">
                          </app-reusable-dropdown>
                          <!-- <input type="text" id="currencyId" class="form-control" formControlName="currencyId" name="refNumber"> -->
                          <!-- <ng-container *ngIf="form && form?.get('movementDate')?.dirty">
                          <p class="text-danger text-error" *ngIf="form && form?.get('movementDate')?.hasError('required')  ">
                              {{'SHARED.THISFIELDISREQUIRED'|translate}}
                          </p>
                      </ng-container> -->
                        </div>
                      </div>
                    </div>
                    <div>
                        <div class="mb-3 col-md-12">
                            <label class="form-label">{{'BONDS.EXPLANATIONAR'|translate}}</label>
                            <textarea formControlName="descriptionAr" class="form-control"></textarea>
                        </div>

                        <div class="mb-3 col-md-12">
                            <label class="form-label">{{'BONDS.EXPLANATIONEN'|translate}}</label>
                            <textarea formControlName="descriptionEn" class="form-control"></textarea>
                        </div>
                    </div>
                </div>
                <div>
                  <ng-container *ngIf="'BONDS.SAVE' | translate as saveLabel">
                    @if (isCreditorAdded && isDebtorAdded) {
                      <button class="mt-3 btn btn-primary" (click)="submit()">
                        {{ saveLabel }}
                      </button>
                    }
                  </ng-container>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
