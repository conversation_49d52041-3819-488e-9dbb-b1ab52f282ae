import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BondFormComponent } from './bond-form/bond-form.component';
import { AddEditNotePayableComponent } from './add-edit-note-payable/add-edit-note-payable.component';
import { SharedModule } from '../../shared/shared.module';
import { BondsRoutingModule } from './bonds-routing.module';
import { CashReceiptComponent } from './cash-receipt/cash-receipt.component';
import { CreditNoteComponent } from './credit-note/credit-note.component';
import { DebitNoteComponent } from './debit-note/debit-note.component';
import { JournalEntriesComponent } from './journal-entries/journal-entries.component';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";
import { AddJournalEntiresComponent } from './journal-entries/add-journal-entires/add-journal-entires.component';
import { AllBondsComponent } from './all-bonds/all-bonds.component';



@NgModule({
  declarations: [
    BondFormComponent,
    AddEditNotePayableComponent,
    CashReceiptComponent,
    CreditNoteComponent,
    DebitNoteComponent,
    JournalEntriesComponent,
    AddJournalEntiresComponent,
    AllBondsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    BondsRoutingModule,
    ReusableDropdownComponent
  ]
})
export class BondsModule { }
