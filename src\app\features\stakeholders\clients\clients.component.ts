import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { StakeholdersService } from '../stakeholders.service';
import {TranslateService} from "@ngx-translate/core";
import {MessageService} from "primeng/api";
import {BranchService} from "../../branches/branch.service";

@Component({
  selector: 'app-clients',
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.scss'
})
export class ClientsComponent {
  clientList: any;
  flattenedClients: any[] = [];
  currentDate: Date = new Date()
  currentRef: string

  constructor(private router: Router, private stakeholder: StakeholdersService, private translate: TranslateService, private messageService: MessageService, private branches: BranchService
  ) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getClients()
    })
  }


  ngOnInit(): void {
    this.branches.selectedBranchId$.subscribe(() => {
      this.getClients();
    })
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }


  searchValue: string = '';
  pageSize: number = 5;
  isVisible = false;
  lang: string;

  addClientModal() {
    this.router.navigate(['/add-client']);

  }
  getClients() {
    this.stakeholder.getClient().subscribe((res: any) => {
      this.clientList = res.data;
      this.flattenedClients = this.flattenClients(res.data);
    })
  }


  edit(data: any) {
    this.router.navigate(['/edit-client', data.id]);
  }
  delete(id: any) {
    this.stakeholder.deleteClient(id).subscribe(res => {
      if (res.message == "Client Deleted!") {
        this.translate.get('TOAST.CLIENT_DELETED').subscribe((message) => {
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('TOAST.SUCCESS'),
            detail: message,
            life: 3000,
          });
        });
      } else {
        this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('TOAST.ERROR'),
            detail: message,
            life: 3000,
          });
        });
      }
      this.getClients();
    })
  }

  flattenClients(clients: any[]): any[] {
    let result: any[] = [];
    clients.forEach((client) => {
      result.push(client);
    });
    return result;
  }
}
