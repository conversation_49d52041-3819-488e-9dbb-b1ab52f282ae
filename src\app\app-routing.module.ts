import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LayoutComponent } from './core/components/layout/layout.component';
import { AuthGuard } from './core/guards/auth.guard';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { RoleGuard } from './core/guards/role.guard';

const routes: Routes = [
  { path: '', pathMatch: 'full', redirectTo: '/users' },
  {
    path: '',
    component: LayoutComponent,
    canActivate: [AuthGuard],
    children: [
      { path: 'users', loadChildren: () => import('./features/users/users.module').then(m => m.UsersModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'accounts', loadChildren: () => import('./features/accounts/accounts.module').then(m => m.AccountsModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'currencies', loadChildren: () => import('./features/currencies/currencies.module').then(m => m.CurrenciesModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'facilities', loadChildren: () => import('./features/facilities/facilities.module').then(m => m.FacilitiesModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'projects', loadChildren: () => import('./features/projects/projects.module').then(m => m.ProjectsModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'cost', loadChildren: () => import('./features/cost/cost.module').then(m => m.CostModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: '', loadChildren: () => import('./features/bonds/bonds.module').then(m => m.BondsModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: '', loadChildren: () => import('./features/stakeholders/stakeholders.module').then(m => m.StakeholdersModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'profile', loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'identity', loadChildren: () => import('./features/identity/identity.module').then(m => m.IdentityModule), canActivate: [RoleGuard], data: { expectedRole: 'Master' } },
      { path: 'branches', loadChildren: () => import('./features/branches/branches.module').then(m => m.BranchesModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'activity', loadChildren: () => import('./features/activity/activity.module').then(m => m.ActivityModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'sales-account', loadChildren: () => import('./features/sales-account/sales-account.module').then(m => m.SalesAccountModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: '', loadChildren: () => import('./features/invoices/invoices.module').then(m => m.InvoicesModule), canActivate: [RoleGuard], data: { expectedRole: 'superadmin' } },
      { path: 'system-settings' , loadChildren: () => import('./features/system-settings/system-settings.module').then(m => m.SystemSettingsModule), data: {expectedRole: 'superadmin'}},
      { path: 'reports', loadChildren: () => import('./features/reports/reports.module').then(m => m.ReportsModule), data: {expectedRole: 'superadmin'}},
      { path: '', loadChildren: () => import('./features/tenants/tenants.module').then(m => m.TenantsModule), canActivate: [RoleGuard], data: { expectedRole: 'Master' } }

    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: true })]
  ,
  providers: [
    { provide: LocationStrategy, useClass: HashLocationStrategy } // Set HashLocationStrategy
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
