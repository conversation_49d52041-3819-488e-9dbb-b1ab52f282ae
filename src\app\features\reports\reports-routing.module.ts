import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {ReportsComponent} from "./reports.component";
import {AnalyticalReportsComponent} from "./analytical-reports/analytical-reports.component";
import {EstimatedBudgetReportComponent} from "./estimated-budget-report/estimated-budget-report.component";
import {AllFinalReportsComponent} from "./all-final-reports/all-final-reports.component";

const routes: Routes = [
  {path: '', component: ReportsComponent},
  {path: 'analytical-reports', component: AnalyticalReportsComponent},
  {path: 'estimated-budget-report', component: EstimatedBudgetReportComponent},
  {path: 'all-final-reports', component: AllFinalReportsComponent}
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportsRoutingModule { }
