import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CurrenciesComponent } from './currencies.component';
import { AccountsRoutingModule } from './currencies-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { AddCurrencyComponent } from './add-currency/add-currency.component';

@NgModule({
  declarations: [
    CurrenciesComponent,
    AddCurrencyComponent
  ],
  imports: [
    CommonModule,
    AccountsRoutingModule,
    SharedModule
  ]
})
export class CurrenciesModule { }
