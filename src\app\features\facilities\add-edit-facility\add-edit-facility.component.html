<div *nzModalTitle><span *ngIf="isUpdate">{{'FACILITIES.UPDATEFACILITY'|translate}}</span> <span
        *ngIf="!isUpdate">{{'FACILITIES.ADDFACILITY'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">

        <label>{{'FACILITIES.FACILITYNAMEAR'|translate}}</label>
        <input type="text" id="FacilityNameAr" class="form-control" formControlName="FacilityNameAr"
            name="FacilityNameAr">
        <ng-container *ngIf="form && form?.get('FacilityNameAr')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('FacilityNameAr')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>


        <label>{{'FACILITIES.FACILITYNAMEEN'|translate}}</label>
        <input type="text" id="FacilityNameEn" class="form-control" formControlName="FacilityNameEn"
            name="FacilityNameEn">
        <ng-container *ngIf="form && form?.get('FacilityNameEn')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('FacilityNameEn')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>


        <label>{{'FACILITIES.ADMINISTRATORNAME'|translate}}</label>
        <input type="text" id="AdministratorName" class="form-control" formControlName="AdministratorName"
            name="AdministratorName">
        <ng-container *ngIf="form && form?.get('AdministratorName')?.dirty">
            <p class="text-danger text-error" *ngIf="form && form?.get('AdministratorName')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
        </ng-container>
        <div class="form-group">
            <label>{{'FACILITIES.EMAIL'|translate}}</label>
            <input type="text" id="Email" class="form-control" formControlName="Email" name="Email">
            <ng-container *ngIf="form && form?.get('Email')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('Email')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'FACILITIES.ADDRESS'|translate}}</label>
            <input type="text" id="Address" class="form-control" formControlName="Address" name="Address">
            <ng-container *ngIf="form && form?.get('Address')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('Address')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'FACILITIES.POSTALCODE'|translate}}</label>
                <input type="number" id="PostalCode" class="form-control" formControlName="PostalCode"
                    name="PostalCode">
                <ng-container *ngIf="form && form?.get('PostalCode')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('PostalCode')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'FACILITIES.POSTALBOX'|translate}}</label>
                <input type="text" id="PostalBox" class="form-control" formControlName="PostalBox" name="PostalBox">
                <ng-container *ngIf="form && form?.get('PostalBox')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('PostalBox')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>

        </div>

        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'FACILITIES.PHONE'|translate}}</label>
                <input type="number" id="Phone" class="form-control" formControlName="Phone" name="Phone">
                <ng-container *ngIf="form && form?.get('Phone')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('Phone')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'FACILITIES.FAX'|translate}}</label>
                <input type="text" id="FAX" class="form-control" formControlName="FAX" name="FAX">
                <ng-container *ngIf="form && form?.get('FAX')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('FAX')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <label>{{'FACILITIES.FIRSTMOBILE'|translate}}</label>
                <input type="number" id="FirstMobile" class="form-control" formControlName="FirstMobile"
                    name="FirstMobile">
                <ng-container *ngIf="form && form?.get('FirstMobile')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('FirstMobile')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="form-group">
                <label>{{'FACILITIES.SECONDMOBILE'|translate}}</label>
                <input type="number" id="SecondMobile" class="form-control" formControlName="SecondMobile"
                    name="SecondMobile">
                <ng-container *ngIf="form && form?.get('SecondMobile')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('SecondMobile')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="row">
            <div class="form-group">
                <label>{{'FACILITIES.VATREGISTER'|translate}}</label>
                <input type="number" id="VatRegister" class="form-control" formControlName="VatRegister"
                    name="VatRegister">
                <ng-container *ngIf="form && form?.get('VatRegister')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('VatRegister')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="form-group">
                <label>{{'FACILITIES.VATACCOUNTNUMBER'|translate}}</label>
                <input type="number" id="VATAccountNumber" class="form-control" formControlName="VATAccountNumber"
                    name="VATAccountNumber">
                <ng-container *ngIf="form && form?.get('VATAccountNumber')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('VATAccountNumber')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
        <div class="form-group">
            <label>{{'FACILITIES.NOTES'|translate}}</label>
            <input type="text" id="Notes" class="form-control" formControlName="Notes" name="Notes">
            <ng-container *ngIf="form && form?.get('Notes')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('Notes')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="addorEditFacility()"><span
                *ngIf="isUpdate">{{'FACILITIES.UPDATEFACILITY'|translate}}</span> <span
                *ngIf="!isUpdate">{{'FACILITIES.ADDFACILITY'|translate}}</span></button>
    </div>
</form>