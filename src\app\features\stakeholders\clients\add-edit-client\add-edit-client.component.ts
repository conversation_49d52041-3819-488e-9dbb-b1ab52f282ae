import { Component } from '@angular/core';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import { StakeholdersService } from '../../stakeholders.service';
import { ActivatedRoute, Router } from '@angular/router';
import { IdentityService } from '../../../identity/identity.service';
import moment from 'moment-hijri';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";
@Component({
  selector: 'app-add-edit-client',
  templateUrl: './add-edit-client.component.html',
  styleUrl: './add-edit-client.component.scss'
})
export class AddEditClientComponent {
  form: FormGroup;
  towns: any;
  countries: any;
  cities: any;
  regions: any;
  id: any;
  data: any;
  identityList: any;
  isSubmitting: boolean = false;
  constructor(private fb: FormBuilder, private stakeholders: StakeholdersService, private route: ActivatedRoute, private router: Router, private identity: IdentityService, private translate: TranslateService, private messageService: MessageService) {
    this.createForm();
  }
  ngOnInit(): void {
    this.getRegions();
    this.getCities();
    this.getTowns();
    this.getCountries();
    this.getIdentity()
    this.id = this.route.snapshot.paramMap.get('id');
    if (this.id) {
      this.getClientById(this.id)
    }
  }
  getClientById(id: any) {
    this.stakeholders.getClientById(id).subscribe(res => {
      this.data = res.data;
      this.createForm();
    })

  }
  createForm() {
    this.form = this.fb.group(
      {
        "serial": [this.data?.serial || null],
        "openDate": [this.data?.openDate || null],
        "openDateHijri": [],
        "identityNumber": [this.data?.identityNumber],
        "name": [this.data?.name || null],
        "nameAr": [this.data?.nameAr || null],
        "accountNumber": [this.data?.accountNumber],
        "isStopped": [this.data?.isStopped || false],
        "branchId": [this.data?.branchId || null],
        "responsibleName": [this.data?.responsibleName || null],
        "countryId": [this.data?.countryId || null],
        "cItyId": [this.data?.cItyId || null],
        "regionId": [this.data?.regionId || null],
        "townId": [this.data?.townId || null],
        "fullAddress": [this.data?.fullAddress || null],
        "streetName": [this.data?.streetName || null],
        "buildingId": [this.data?.buildingId || null],
        "zipCode": [this.data?.zipCode || null],
        "postalCode": [this.data?.postalCode || null],
        "firstPhone": [this.data?.firstPhone || null],
        "secondPhone": [this.data?.secondPhone || null],
        "firstMobile": [this.data?.firstMobile || null],
        "secondMobile": [this.data?.secondMobile || null],
        "firstFax": [this.data?.firstFax || null],
        "secondFax": [this.data?.secondFax || null],
        "email": [this.data?.email || null],
        "notes": [this.data?.notes || null],
        "boxNumber": [this.data?.boxNumber || null],
        "taxAccountNumber": [this.data?.taxAccountNumber || null],
        "bankName": [this.data?.bankName || null],
        "facilityNumber": [null],
        "buildingCode": [this.data?.buildingCode || null],
        "commercialCode": [this.data?.commercialCode || null],
        "iban": [this.data?.iban || null],
        "contractNo": [this.data?.contractNo || null],
        "vatRegNO": [this.data?.vatRegNO || null]
      }
    )
  }
  getTowns() {
    this.stakeholders.getTowns().subscribe(res => {
      this.towns = res.data;
    })
    if (this.data && this.data.townId) {
      const selectedTown = this.towns.find((town: any) => town.id === this.data.townId);
      if (selectedTown) {
        this.onSelectedTown(selectedTown)
      }
    }
  }

  onSelectedTown(selected: any): void {
    this.form.patchValue({
      townId: selected.id,
    });
  }

  get townControl(): FormControl {
    return this.form.get('townId') as FormControl;
  }
  getCountries() {
    this.stakeholders.getCountries().subscribe(res => {
      this.countries = res.data;
    })

    if (this.data && this.data.countryId) {
      const selectedCountry = this.countries.find((country: any) => country.id === this.data.countryId);
      if (selectedCountry) {
        this.onSelectedCountry(selectedCountry)
      }
    }
  }

  onSelectedCountry(selected: any): void {
    this.form.patchValue({
      countryId: selected.id,
    });
  }

  get countryControl(): FormControl {
    return this.form.get('countryId') as FormControl;
  }

  getCities() {
    this.stakeholders.getCities().subscribe(res => {
      this.cities = res.data;
    })
    console.log('selected')
    if (this.data && this.data.cItyId) {
      const selectedCity = this.cities.find((city: any) => city.id == this.data.cItyId);
      if (selectedCity) {
        this.onSelectedCity(selectedCity)
      }
    }
  }

  onSelectedCity(selected: any): void {
    this.form.patchValue({
      cItyId: selected.id,
    });
  }

  get cityControl(): FormControl {
    return this.form.get('cItyId') as FormControl;
  }

  getRegions() {
    this.stakeholders.getRegions().subscribe(res => {
      this.regions = res.data;
    })

    if (this.data && this.data.regionId) {
      const selectedRegion = this.regions.find((region: any) => region.id === this.data.regionId);
      if (selectedRegion) {
        this.onSelectedRegion(selectedRegion)
      }
    }
  }

  onSelectedRegion(selected: any): void {
    this.form.patchValue({
      regionId: selected.id,
    });
  }

  get regionControl(): FormControl {
    return this.form.get('regionId') as FormControl;
  }
  getIdentity() {
    this.identity.getIdentity().subscribe(res => {
      this.identityList = res.data;
    })

    if (this.data && this.data.identityNumber) {
      const selectedIdentity = this.identityList.find((identity: any) => identity.id === this.data.identityNumber);
      if (selectedIdentity) {
        this.onSelectedIdentity(selectedIdentity)
      }
    }
  }

  // controlling identity
  get identityControl(): FormControl {
    return this.form.get('identityNumber') as FormControl;
  }

  onSelectedIdentity(selected: any): void {
    this.form.patchValue({
      identityNumber: selected.id,
    });
  }

  dateHandler() {
    if (this.form?.get('openDate')?.value) {
      let date = moment(this.form?.get('openDate')?.value).format('iD iMMMM iYYYY')
      this.form.patchValue({
        openDateHijri: date
      });
    }
  }
  onSubmit() {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    let data = this.form.getRawValue();
    data.openDateHijri = null
    this.stakeholders.addClient(data).subscribe({
      next: (res) => {
        if (res.message == "Client created") {
          this.translate.get('TOAST.CLIENT_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }

        this.isSubmitting = false;
        this.router.navigate(['/clients']);
      },
      error: (err) => {
        this.isSubmitting = false;
        console.error('Error:', err);
      }
    });
  }
  edit() {
    if (this.isSubmitting) return;
    this.isSubmitting = true;

    let data = this.form.getRawValue();
    data.openDateHijri = null
    this.stakeholders.editClient(this.id, data).subscribe({
      next: (res) => {
        if (res.message == "Client updated") {
          this.translate.get('TOAST.CLIENT_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }
        this.isSubmitting = false;
        this.router.navigate(['/clients']);
      },
      error: (err) => {
        this.isSubmitting = false;
        console.error('Error:', err);
      }
    });
  }
}
