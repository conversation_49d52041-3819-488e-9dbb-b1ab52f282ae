.ant-table-thead>tr>th {
    text-align: center;


}

.ant-table table {
    text-align: center;
}

.ant-table-tbody>tr>td:not(.nz-disable-td.ant-table-cell) {
    padding: 0.6rem;
}

.ant-table-thead>tr>th {
    padding: 0.6rem;
}

.ant-table-row:nth-child(even) {
    background-color: var(--color-secondary);
}

.ant-table-row {
    color: var(--color-grey);
}

.ant-table-thead>tr>th {
    background-color: var(--color-secondary);
    color: var(--color-primary);
}

:lang(ar) .ant-table-column-title {
    padding-right: 1.2rem;
}

:lang(en) .ant-table-column-title {
    padding-left: 1.2rem;
}

// .ant-table-tbody > tr > td{
//     overflow-wrap: normal;
// }
// .ant-table-tbody > tr > td {
//     overflow-wrap: normal;
//     overflow: auto;
// }
:lang(ar) .ant-pagination-next {
    margin-right: 1rem;
}

@media screen and (max-width: 600px) {
    .ant-table-tbody>tr>td {
        font-size: 42%;
    }

    .ant-table-thead>tr>th {
        font-size: 42%;
    }
}