<div class="erp-card erp-shadow-end mt-5">
  <!-- Search Input -->
  <div class="row gx-5 gy-2 mb-3 py-3 px-3">
    <div class="col-12 col-md-6">
      <input type="text"
             id="search"
             class="form-control"
             placeholder="{{ 'SHARED.TYPEHERETOSEARCH' | translate }}"
             name="search"
             [(ngModel)]="searchValue">
    </div>

    <div class="col-12 col-md-5">
      <ul class="nav nav-tabs filter-tabs justify-content-md-end" role="tablist">
        <li class="nav-item" (click)="isChangedFilter = null">
          <a class="nav-link" [class.active]="isChangedFilter === null">
            {{ 'SHARED.ALL' | translate }}
          </a>
        </li>
        <li class="nav-item" (click)="isChangedFilter = true">
          <a class="nav-link" [class.active]="isChangedFilter === true">
            {{ 'SHARED.CHANGED' | translate }}
          </a>
        </li>
        <li class="nav-item" (click)="isChangedFilter = false">
          <a class="nav-link" [class.active]="isChangedFilter === false">
            {{ 'SHARED.NOTCHANGED' | translate }}
          </a>
        </li>
      </ul>
    </div>
  </div>
  <div class="my-3 row px-3">
    <nz-table #nestedTable #sortTable [nzPageSize]="pageSize" [nzData]="bondsList | tablefilters: {
        docDate: searchValue,
        amount: searchValue,
        branchName: searchValue,
        isChanged: isChangedFilter
      }"
        nzTableLayout="fixed" (nzPageIndexChange)="onPageChange($event)">
      <thead>
      <tr>
        <th></th>
        <th nzColumnKey="name">{{'BONDS.REFNUMBER'|translate}}</th>
        <th nzColumnKey="name">{{'BONDS.DATE'|translate}}</th>
        <th nzColumnKey="name">{{'BONDS.AMOUNT'|translate}}</th>
        <th nzColumnKey="name">{{'BONDS.TYPE'|translate}}</th>
        <th nzColumnKey="name">{{'SHARED.CHANGED'|translate}}</th>

      </tr>
      </thead>
      <tbody>
        @for (data of nestedTable.data; track data) {
          <tr>
            <td [(nzExpand)]="data.expand"></td>
            <td>{{ data.refNumber }}</td>
            <td>{{ data.docDate |date:'dd/MM/yyyy' }}</td>
            <td>{{ data.amount }}</td>
            <td>{{ data.financialReportTypeName }}</td>
            <td>{{ data.isChanged ? ('SHARED.CHANGED' | translate) : ('SHARED.NOTCHANGED' | translate) }}</td>

          </tr>
          <tr [nzExpand]="data.expand">
            <nz-table #innerTable [nzData]="data.financialReportLines" nzSize="middle"
                      [nzShowPagination]="false">
              <thead>
              <tr>
                <th>{{'BONDS.ACCOUNTNUMBER'|translate}}</th>
                <th>{{'BONDS.COSTCENTER'|translate}}</th>
                <th>{{'BONDS.EXPLANATIONAR'|translate}}</th>
                <th>{{'BONDS.EXPLANATIONEN'|translate}}</th>

                <th>{{'BONDS.PROJECT'|translate}}</th>
                <th>{{'BONDS.CREDIT'|translate}}</th>
                <th>{{'BONDS.DEBIT'|translate}}</th>

              </tr>
              </thead>
              <tbody>
                @for (data of innerTable.data; track data) {
                  <tr>
                    <td>{{ data.accountName }}</td>
                    <td>{{ data.centerName }}</td>
                    <td>{{ data.descriptionAr }}</td>
                    <td>{{ data.descriptionEn }}</td>

                    <td>{{ data.projectName }}</td>
                    <td>{{ data.totalCredit }}</td>
                    <td>{{ data.totalDebit }}</td>

                  </tr>
                }
              </tbody>
            </nz-table>
          </tr>
        }
      </tbody>
    </nz-table>
  </div>
  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h3>{{ 'PDF.CASHRECEIPTREPORTS' | translate }}</h3>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <table>
      <thead>
      <tr>
        <th>{{'BONDS.DATE' | translate}}</th>
        <th>{{'BONDS.AMOUNT' | translate}}</th>
        <th>{{'BONDS.TYPE' | translate}}</th>
        <th>{{'SHARED.CHANGED'|translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let row of flattenedBonds" [class.parent-row]="row.isParent" [class.child-row]="!row.isParent">
        <td>{{ row.date }}</td>
        <td>{{ row.amount }}</td>
        <td>{{ row.financialReportTypeName }}</td>
        <td>{{ row.isChanged ? ('SHARED.CHANGED' | translate) : ('SHARED.NOTCHANGED' | translate) }}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
