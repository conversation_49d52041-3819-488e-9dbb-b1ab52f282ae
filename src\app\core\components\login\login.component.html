<div class="container">
  <div class="pt-5"><app-lang-dropdown></app-lang-dropdown></div>
  <div class=" login-container ">
    <div class="login row justify-content-center">
      <div class="erp-card login-wd p-5 col-md-4 erp-shadow-center mb-5">
        <h5 class="text-center text-primary mb-5">{{ 'LOGINFORM.LOGIN' |translate}}</h5>
        <form [formGroup]="form" (ngSubmit)="onSubmit()">
          <div class="form-group">
            <input type="text" id="username" placeholder="{{ 'LOGINFORM.USERNAMEOREMAIL' |translate}}"
              class="form-control" formControlName="username" name="username">
            <ng-container *ngIf="form && form?.get('username')?.dirty">
              <p class="text-danger text-error" *ngIf="form && form?.get('username')?.hasError('required')  ">
                {{'LOGINFORM.USERNAMEREQUIRED'|translate}}
              </p>
            </ng-container>
          </div>
          <div class="input-group mt-3">
            <input [type]="inputType" id="password" placeholder="{{ 'LOGINFORM.PASSWORD' |translate}}" class="form-control"
              formControlName="password" name="password"  aria-label="Recipient's username" aria-describedby="basic-addon2">
              <span class="input-group-text" id="basic-addon2" (click)="toggleVisibility()">
              <span nz-icon *ngIf="visible" nzType="eye" nzTheme="outline"></span>
              <span nz-icon *ngIf="!visible" nzType="eye-invisible" nzTheme="outline"></span>
            </span>

          </div>
          <ng-container *ngIf="form && form?.get('password')?.dirty">
            <p class="text-danger text-error" *ngIf="form?.get('password')?.hasError('required')">
              {{'LOGINFORM.PASSWORDREQUIRED'|translate}}
            </p>
          </ng-container>

          <button type="submit" class="btn btn-primary btn-block mt-4">{{ 'LOGINFORM.LOGIN' |translate}}</button>

          <p class="text-center mt-3">{{ 'LOGINFORM.NEWONOURPLATFORM' |translate}} <a class="text-primary" routerLink="/register">{{
              'LOGINFORM.CREATEANACCOUNT'
              |translate}}</a></p>

        </form>
      </div>


    </div>
  </div>
</div>