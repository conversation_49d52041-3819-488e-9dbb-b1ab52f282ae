import { Component, inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { BondsService } from '../../bonds/services/bonds.service';
import { ActivityService } from '../services/activity.service';
import moment from 'moment-hijri';
import {MessageService} from "primeng/api";
import {TranslateService} from "@ngx-translate/core";
function atLeastOneCheckbox(control: FormGroup) {
  const isPrivate = control.get('isPrivate')?.value;
  const isCostCenter = control.get('isCostCenter')?.value;

  if (!isPrivate && !isCostCenter) {
    return { atLeastOneCheckboxRequired: true };
  }
  return null;
}

@Component({
  selector: 'app-add-edit-activity',
  templateUrl: './add-edit-activity.component.html',
  styleUrl: './add-edit-activity.component.scss'
})
export class AddEditActivityComponent {
  form: FormGroup;
  data: any


  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  isUpdate: boolean = false;
  ischild: any;
  parentAccountId: any;
  childLevel: any;
  converssionRateValue: any;
  accountsList: any;

  constructor(private modal: NzModalRef, private bonds: BondsService, private fb: FormBuilder, private activity: ActivityService, private translate: TranslateService, private messageService: MessageService
  ) {

  }
  ngOnInit() {

    this.createFormAdd();


  }

  dateHandler() {
    if (this.form?.get('activityOpeningDate')?.value) {

      let date = moment(this.form?.get('activityOpeningDate')?.value).format('iD iMMMM iYYYY')

      this.form.patchValue({
        activityOpeningDatehijri: date
      });
    }
  }
  // updateConversionRate() {
  //   if (this.form?.get('currencyId')?.value) {

  //     this.currencyList.forEach((element: any) => {

  //       if (this.form?.get('currencyId')?.value == element.id)
  //         this.converssionRateValue = element.conversionRate
  //     });
  //     this.form.patchValue({
  //       conversionRate: this.converssionRateValue
  //     });
  //   }
  // }
  createFormAdd() {
    this.ischild = this.nzModalData?.child;
    if (this.ischild) {

      this.parentAccountId = this.nzModalData?.id;
      this.childLevel = this.nzModalData?.originalData?.level + 1;
    }

    if (!this.ischild) {
      this.data = this.nzModalData?.originalData;
      if (this.data) {
        this.isUpdate = true;
      }
    }
    const level = this.data?.level || this.childLevel || 0;
    let status = this.data?.status == 0 ? '0' : 1
    this.form = this.fb.group({
      shortName: [this.data?.shortName || '', Validators.required],
      activityName: [this.data?.activityName || '', Validators.required],
      account_Name_Latin: [this.data?.account_Name_Latin || '', Validators.required],

      activityOpeningDate: [this.data?.activityOpeningDate || '', Validators.required],
      creditLimit: [this.data?.creditLimit || ''],
      openingBalance: [this.data?.openingBalance || ''],
      openingBalanceDate: [this.data?.openingBalanceDate || ''],
      estimatedBudget: [this.data?.estimatedBudget || '', [Validators.required, Validators.min(0)]],
      notes: [this.data?.notes || '',],
      email: [this.data?.email || '', [Validators.email]],
      status: [status || ''],

      isPrivate: [this.data?.isPrivate || false],
      isCostCenter: [this.data?.isCostCenter || false],

      level: [level],
      isDetailed: [this.data?.isDetailed || false],
      // vatRegister: [this.data?.vatRegister || ''],
      // openingBalanceByInForeignCurrency: [this.data?.openingBalanceByInForeignCurrency || ''],
      parentActivityId: [this.parentAccountId || this.data?.parentAccountId || null],
      activityOpeningDatehijri: [],
      // conversionRate: [this.data?.conversionRate],
      // accountNumber: [this.data?.accountNumber],
      // parentAccountNum: [this.data?.parentAccountNum]
    });
    this.dateHandler();

    // this.form.controls['parentAccountNum'].disable();
    // this.form.controls['accountNumber'].disable();
  }

  destroyModal(): void {
    this.modal.destroy();
  }
  getAccounts() {
    this.bonds.getAccounts().subscribe(res => {
      this.accountsList = res.data;
    })
  }
  // getCurrency() {
  //   this.account.getCurrency().subscribe(res => {
  //     this.currencyList = res.data;
  //   })
  // }
  onRadioChange(value: boolean) {
    this.form.get('isDetailed')?.setValue(value);
  }

  onSubmit() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      this.activity.addActivity(data).subscribe(res => {
        if (res.message == "Activity created") {
          this.translate.get('TOAST.ACTIVITY_CREATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          });
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          });
        }
        this.modal.destroy(res);
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  update() {
    if (this.form.valid) {
      let dataForm = this.form.getRawValue();
      dataForm == '0' ? 0 : dataForm
      this.activity.updateActivity(this.data.id, dataForm).subscribe(res => {
        if (res.message == "Activity updated") {
          this.translate.get('TOAST.ACTIVITY_UPDATED').subscribe((message) => {
            this.messageService.add({
              severity: 'success',
              summary: this.translate.instant('TOAST.SUCCESS'),
              detail: message,
              life: 3000,
            });
          })
        } else {
          this.translate.get('TOAST.SOMETHING_WENT_WRONG').subscribe((message) => {
            this.messageService.add({
              severity: 'error',
              summary: this.translate.instant('TOAST.ERROR'),
              detail: message,
              life: 3000,
            });
          })
        }
        this.modal.destroy(res);
      })
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
}
