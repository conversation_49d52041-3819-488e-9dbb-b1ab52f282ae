import { AfterViewInit, Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AuthService } from '../../services/auth/auth.service';
import { RoleService } from '../../services/role/role.service';
import { BranchService } from '../../../features/branches/branch.service';
import { branch } from '../../models';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrl: './layout.component.scss'
})
export class LayoutComponent implements OnInit, AfterViewInit {
  branchList: branch[] = [];
  selectedBranchId: string | undefined = '';
  isCollapsed = false;
  branchControl = new FormControl();

  isMaster: boolean = (() => {
    const userStr = localStorage.getItem('user');
    if (!userStr) return false;
    try {
      const user = JSON.parse(userStr);
      return user.role?.includes('Master') ?? false;
    } catch {
      return false;
    }
  })();

  constructor(
    private translate: TranslateService,
    private auth: AuthService,
    public roleService: RoleService,
    private branches: BranchService
  ) {
    const htmlTag = document.getElementsByTagName('html')[0];
    htmlTag.dir = 'ltr';
    htmlTag.lang = 'en';
  }

  ngOnInit(): void {
    this.branches.branchList$.subscribe(branches => {
      this.branchList = branches;
      if (branches.length > 0 && !this.isMaster) {
        const firstBranchId = branches[0].id;
        this.branchControl.setValue(firstBranchId);
        this.onChangedBranch(firstBranchId || "");
      }
    });

    // React to user manual changes
    this.branchControl.valueChanges.subscribe((selectedId: string) => {
      this.onChangedBranch(selectedId);
    });

    this.branches.loadBranches(); // Initial trigger
  }

  ngAfterViewInit(): void {
    this.changePageDirection(this.translate.currentLang);
  }

  logout() {
    this.auth.logout();
  }

  changePageDirection(lang: string) {
    const htmlTag = document.getElementsByTagName('html')[0];
    htmlTag.dir = lang === 'ar' ? 'rtl' : 'ltr';
    htmlTag.lang = lang;
  }

  onChangedBranch(selectedId: string) {
    if (selectedId === 'All') {
      this.selectedBranchId = '';
      this.branches.setBranchId('All');
      return;
    }

    const selectedBranch = this.branchList.find(branch => branch.id === selectedId);
    if (selectedBranch) {
      this.selectedBranchId = selectedBranch.id;
      this.branches.setBranchId(this.selectedBranchId);
    }
  }
}
