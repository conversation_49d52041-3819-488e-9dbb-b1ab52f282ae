import { Component, inject } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { IdentityService } from '../identity.service';

@Component({
  selector: 'app-add-edit-identity',
  templateUrl: './add-edit-identity.component.html',
  styleUrl: './add-edit-identity.component.scss'
})
export class AddEditIdentityComponent {
  isUpdate:boolean=false
  form: FormGroup;
  data: any;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  constructor(private modal: NzModalRef, private fb: FormBuilder, private identity:IdentityService ) {

  }
  ngOnInit(): void {
    this.createForm();
  }
  addorEditCurrency() {
    if (this.form.valid) {
      let data = this.form.getRawValue();
      // if(!this.isUpdate){

        this.identity.addIdentity(data).subscribe(res => {
          this.modal.destroy(res);
        })
      // }else{
        // this.identity.(data.id,data).subscribe(res => {
        //   this.modal.destroy(res);
        // })
      // }
    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }
  createForm() {
    this.data = this.nzModalData;
    if (this.data) {
      this.isUpdate = true;
    }
    this.form = this.fb.group({
      identity_Code: [this.data?.identity_Code],
      identity_Name_Latin: [this.data?.identity_Name_Latin],
      identity_Name: [this.data?.identity_Name],
 
    });
  }


  destroyModal(): void {
    this.modal.destroy();
  }
}
