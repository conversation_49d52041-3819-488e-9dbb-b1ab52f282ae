<app-bond-form (getDataTable)="getBonds()" [type]="type" [refNumber]="refNumber" ></app-bond-form>
<div class="erp-card erp-shadow-end mt-5">
    <!-- Search Input -->
    <div class="col-12 col-md-3 mb-2 mb-md-0">
      <input type="text" id="search" class="form-control"
             placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
             name="search" [(ngModel)]="searchValue">
    </div>
    <div class="my-3 row px-3">
        <nz-table #nestedTable #sortTable [nzPageSize]="pageSize" [nzData]="bondsList | tablefilters: {docDate: searchValue, amount:searchValue, branchName:searchValue}" nzTableLayout="fixed" (nzPageIndexChange)="onPageChange($event)">
            <thead>
                <tr>
                    <th></th>
                  <th nzColumnKey="name">{{'BONDS.REFNUMBER'|translate}}</th>
                  <th nzColumnKey="name">{{'BONDS.DATE'|translate}}</th>
                    <th nzColumnKey="name">{{'BONDS.AMOUNT'|translate}}</th>
                    <th nzColumnKey="name">{{'BONDS.BRANCHNAME'|translate}}</th>
                  <th nzColumnKey="name">{{'BONDS.ACTIONS'|translate}}</th>
                </tr>
            </thead>
            <tbody>
                @for (data of nestedTable.data; track data) {
                <tr>
                    <td [(nzExpand)]="data.expand"></td>
                  <td>{{ data.refNumber }}</td>
                  <td>{{ data.docDate |date:'dd/MM/yyyy' }}</td>
                    <td>{{ data.amount }}</td>
                    <td>{{ data.branchName }}</td>
                  <td><span data-bs-toggle="dropdown" aria-expanded="false">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical" viewBox="0 0 16 16">
                      <path
                        d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                    </svg>
                  </span>
                    <ul class="dropdown-menu">
                      <li class="p-2">
                        <button
                          [ngClass]="data.isChanged ? 'erp-btn erp-btn-primary' : 'btn btn-primary'"
                          [disabled]="data.isChanged"
                          (click)="onEditBond(data.id)">
                          {{"BONDS.CHANGE"|translate}}
                        </button>
                      </li>
                    </ul>
                  </td>

                </tr>
                <tr [nzExpand]="data.expand">
                    <nz-table #innerTable [nzData]="data.financialReportLines" nzSize="middle"
                        [nzShowPagination]="false">
                        <thead>
                            <tr>
                                <th>{{'BONDS.ACCOUNTNUMBER'|translate}}</th>
                                <th>{{'BONDS.COSTCENTER'|translate}}</th>
                                <th>{{'BONDS.EXPLANATIONAR'|translate}}</th>
                                <th>{{'BONDS.EXPLANATIONEN'|translate}}</th>

                                <th>{{'BONDS.PROJECT'|translate}}</th>
                                <th>{{'BONDS.CREDIT'|translate}}</th>
                                <th>{{'BONDS.DEBIT'|translate}}</th>

                            </tr>
                        </thead>
            <tbody>
                @for (data of innerTable.data; track data) {
                <tr>
                    <td>{{ data.accountName }}</td>
                    <td>{{ data.centerName }}</td>
                    <td>{{ data.descriptionAr }}</td>
                    <td>{{ data.descriptionEn }}</td>

                    <td>{{ data.projectName }}</td>
                    <td>{{ data.totalCredit }}</td>
                    <td>{{ data.totalDebit }}</td>

                </tr>
                }
            </tbody>
        </nz-table>
        </tr>
        }
        </tbody>
        </nz-table>
    </div>
  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h3>{{ 'PDF.DEBITNOTEREPORTS' | translate }}</h3>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <table>
      <thead>
      <tr>
        <th>{{'BONDS.DATE' | translate}}</th>
        <th>{{'BONDS.AMOUNT' | translate}}</th>
        <th>{{'BONDS.BRANCHNAME' | translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let row of flattenedBonds" [class.parent-row]="row.isParent" [class.child-row]="!row.isParent">
        <td>{{ row.date }}</td>
        <td>{{ row.amount }}</td>
        <td>{{ row.branchName }}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
