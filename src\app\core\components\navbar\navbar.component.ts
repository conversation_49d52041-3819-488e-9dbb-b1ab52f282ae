import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import moment from 'moment-hijri';
import { UserService } from '../../services/user/user.service';
@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss'
})
export class NavbarComponent implements OnInit{
  gregorianDate: Date;
  hijriDate: string;
  showARDate: boolean = false;
  userInfo: any;
  arabicDate: any;
  constructor(private translate: TranslateService, private user: UserService) {
    this.gregorianDate = new Date(); // Initialize the Gregorian date
    // let enNumber = 89000000;
    // console.log(enNumber.toLocaleString('ar-sa'));
    this.arabicDate = new Date().toLocaleDateString('ar-EG-u-nu-latn',{weekday: 'long', year: 'numeric', month: 'short', day: 'numeric'});

    this.hijriDate = moment(this.gregorianDate).format('iD iMMMM iYYYY'); // Initialize the Hijri date with month name
    this.translate.onLangChange.subscribe((langObject) => {
      let lang = langObject.lang;
      if (lang === 'ar') {
        this.showARDate = true;
      }
      else {
        this.showARDate = false;
      }

    })
  }
  ngOnInit(): void {
    this.getUserInfo() 
  }

  getUserInfo() {
    this.user.getUserInfo().subscribe(res => {
      this.userInfo=res.data
    })
  }


}
