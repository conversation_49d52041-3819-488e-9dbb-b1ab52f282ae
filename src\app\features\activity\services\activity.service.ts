import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IBaseResponse } from '../../../core/models';
import {BranchService} from "../../branches/branch.service";

@Injectable({
  providedIn: 'root'
})
export class ActivityService {

  private branchId: string = '';

  constructor(private httpClient: HttpClient, private branches: BranchService) {
    this.branches.selectedBranchId$.subscribe(id => {
      this.branchId = id;
    });
  }
  getCurrency() {
    return this.httpClient.get<IBaseResponse<any>>(`/Currencies`);
  }

  addActivity(data: any) {
    if (this.branchId) {
      data.branchId = this.branchId;
    }
    return this.httpClient.post<IBaseResponse<any>>(`/Activity`, data);
  }
  getActivities() {
    if (!this.branchId) {
      return this.httpClient.get<IBaseResponse<any>>('/Activity');
    } else {
      return this.httpClient.get<IBaseResponse<any>>(`/Activity?branchId=${this.branchId}`);
    }
  }
  updateActivity(id: any, data: any) {
    return this.httpClient.put<IBaseResponse<any>>(`/Activity/${id}`, data);
  }
  deleteActivity(id: any) {
    return this.httpClient.delete<IBaseResponse<any>>(`/Activity/${id}`);
  }
}
