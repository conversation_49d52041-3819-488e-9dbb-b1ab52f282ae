<div class="all-final-reports-container">
  <div class="erp-card erp-shadow-end">
    <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
    <h3 class="page-title">{{ "REPORTS.ALLFINALREPORTS" | translate }}</h3>

    <!-- Form Section -->
    <div class="form-section">
      <form [formGroup]="form" class="w-100">
        <!-- Accounts Selection -->
        <div class="row form-row">
          <div class="col-lg-4 col-md-6 col-12 mb-3">
            <label class="form-label">{{ "REPORTS.ACCOUNTS"|translate }}</label>
            <p-multiSelect
              [options]="accountsList"
              formControlName="accountIds"
              optionLabel="label"
              styleClass="w-full custom-multiselect"
              placeholder="Select accounts..."
            >
            </p-multiSelect>
          </div>
          <div class="col-lg-4 col-md-6 col-12 mb-3">
            <label class="form-label">{{ "REPORTS.ACTIVITY"|translate }}</label>
            <p-multiSelect
              [options]="activityList"
              formControlName="activityIds"
              optionLabel="label"
              styleClass="w-full custom-multiselect"
              placeholder="Select activities..."
            >
            </p-multiSelect>
          </div>
          <div class="col-lg-4 col-md-6 col-12 mb-3">
            <label class="form-label">{{ "REPORTS.COSTCENTER"|translate }}</label>
            <p-multiSelect
              [options]="costList"
              formControlName="costCenterIds"
              optionLabel="label"
              styleClass="w-full custom-multiselect"
              placeholder="Select cost centers..."
            >
            </p-multiSelect>
          </div>
        </div>

        <div class="row form-row">
          <div class="col-md-6 col-12 mb-3">
            <label class="form-label">{{ "REPORTS.PROJECT"|translate }}</label>
            <p-multiSelect
              [options]="projectList"
              formControlName="projectIds"
              optionLabel="label"
              styleClass="w-full custom-multiselect"
              placeholder="Select projects..."
            >
            </p-multiSelect>
          </div>

          <div class="col-md-6 col-12 mb-3">
            <label class="form-label">{{ "REPORTS.BRANCHES"|translate }}</label>
            <p-multiSelect
              [options]="branchesList"
              formControlName="brancheIds"
              optionLabel="label"
              styleClass="w-full custom-multiselect"
              placeholder="Select branches..."
            >
            </p-multiSelect>
          </div>
        </div>

        <!-- Date Pickers -->
        <div class="row form-row">
          <div class="col-md-6 col-12 mb-3">
            <label class="form-label">{{ "SYSTEMSETTINGS.STARTDATE" | translate }}</label>
            <nz-date-picker
              formControlName="startDate"
              class="datepicker"
              nzPlaceHolder="Select start date">
            </nz-date-picker>
          </div>
          <div class="col-md-6 col-12 mb-3">
            <label class="form-label">{{ "SYSTEMSETTINGS.ENDDATE" | translate }}</label>
            <nz-date-picker
              formControlName="endDate"
              class="datepicker"
              nzPlaceHolder="Select end date">
            </nz-date-picker>
            <div *ngIf="form.hasError('dateRange')" class="text-danger mt-1" style="font-size: 0.8rem;">
              {{ "VALIDATORS.DATERANGE" | translate }}
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="submit-section">
          <button
            type="button"
            class="btn-submit"
            (click)="onSubmit()"
            [disabled]="form.invalid">
            {{ "REPORTS.SUBMIT" | translate }}
          </button>
        </div>
      </form>
    </div>

    <!-- Table Section -->
    <div class="table-section" *ngIf="reportList && reportList.length > 0">
      <nz-table
        #basicTable
        [nzData]="reportList"
        [nzShowPagination]="false"
        nzTableLayout="fixed"
        [nzScroll]="{ x: '600px' }">
        <thead>
          <tr>
            <th nzWidth="120px">{{ 'REPORTS.DOCUMENTNUMBER' | translate }}</th>
            <th nzWidth="200px">{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}</th>
            <th nzWidth="120px">{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
            <th nzWidth="120px">{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
            <th nzWidth="150px">{{ 'REPORTS.DOCUMENTNAME' | translate }}</th>
            <th nzWidth="120px">{{ 'REPORTS.MOVEMENTDATE' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let i of reportList; let idx = index" [hidden]="idx % 2 !== 0">
            <td class="text-center">
              <span class="fw-medium">{{ reportList[idx].documentNumber }}</span>
            </td>
            <td>
              <div class="account-info">
                <span class="account-number">{{ reportList[idx].account.number }}</span>
                <span class="account-name">
                  {{ lang == "en" ? reportList[idx].account.nameEn : reportList[idx].account.nameAr }}
                </span>
              </div>
            </td>
            <td class="text-end">
              <span class="amount debit">{{ reportList[idx].totalDebit | number: '1.2-2' }}</span>
            </td>
            <td class="text-end" *ngIf="reportList[idx + 1]">
              <span class="amount credit">{{ reportList[idx + 1].totalCredit | number: '1.2-2' }}</span>
            </td>
            <td class="text-center">
              <span class="document-type">{{ reportList[idx].documentTypeName }}</span>
            </td>
            <td class="text-center" *ngIf="reportList[idx + 1]">
              <span class="date">{{ reportList[idx + 1].movementDate | date: "yyyy-MM-dd" }}</span>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <!-- Empty State -->
    <div class="empty-state" *ngIf="!reportList || reportList.length === 0">
      <div class="empty-icon">📊</div>
      <div class="empty-message">No data available</div>
      <div class="empty-description">Please submit the form to view reports</div>
    </div>
  </div>

  <!-- PDF Section -->
  <div class="pdf-section">
    <div #pdfTable id="pdfTable" class="pdf-container text-center" dir="{{ lang === 'en' ? 'ltr' : 'rtl' }}">
    <!-- PDF Header -->
    <div class="text-center mb-4 border-bottom pb-3">
      <h2>{{ "REPORTS.ALLFINALREPORTS" | translate }}</h2>
      <div class="d-flex justify-content-between mt-2">
        <div>{{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}</div>
        <div>{{ 'PDF.REF' | translate }}: {{ currentRef }}</div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section mb-3 p-3 rounded text-center">
      <div class="grid-container">
        <!-- Section 1 -->
        <div class="section">
          <div>
            <p>{{ 'SYSTEMSETTINGS.DATERANGE' | translate }}:</p>
            <span>
          {{ form.value.startDate ? (form.value.startDate | date:'yyyy-MM-dd') : '-' }} -
              {{ form.value.endDate ? (form.value.endDate | date:'yyyy-MM-dd') : '-' }}
        </span>
          </div>
          <div>
            <p>{{ 'REPORTS.ACTIVITY' | translate }}:</p>
            <span>{{ getSelectedActivityNames() || 'All' }}</span>
          </div>
        </div>

        <!-- Section 2 -->
        <div class="section">
          <div>
            <p>{{ 'REPORTS.COSTCENTER' | translate }}:</p>
            <span>{{ getSelectedCostNames() || 'All' }}</span>
          </div>
          <div>
            <p>{{ 'REPORTS.PROJECT' | translate }}:</p>
            <span>{{ getSelectedProjectNames() || 'All' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Table -->
    <div *ngFor="let account of groupedReportList">
      <!-- Account Title -->
      <div class="filters-section">
        <div class=" grid-container font-bold p-2">
          <div class="section">
            <div>
              <p>{{ 'REPORTS.PARENTACCOUNT' | translate }}:</p>
              <span>{{ account.parentAccountNumber }}</span>
            </div>
            <div>
              <p>{{ 'REPORTS.ACCOUNT' | translate }}:</p>
              <span>{{ account.accountNumber }}</span>
            </div>
          </div>
          <div class="section">
            <div class="">

            </div>
            <div>
              <p>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}:</p>
              <span>{{ lang === 'en' ? account.accountNameEn : account.accountNameAr }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="table-responsive">
        <table class="table table-bordered table-striped">
          <thead class="table-light">
          <tr>

            <th class="text-center align-middle">{{ 'REPORTS.DOCUMENTNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'SALESACCOUNT.ACCOUNTNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.DESCRIPTION' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.REFNUMBER' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
            <th class="text-center align-middle">{{ 'REPORTS.MOVEMENTDATE' | translate }}</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let record of account.pairedRecords">
            <td class="text-center">
              {{ record.documentNumber }}
            </td>
            <td class="text-center">
              {{ account.accountNumber }} - {{ lang === 'en' ? account.accountNameEn : account.accountNameAr }}
            </td>
            <td class="text-center">
              {{ lang === 'en' ? record.descriptionEn : lang === 'ar' && record.descriptionAr }}
            </td>
            <td class="text-center">
              {{ record.refNumber }}
            </td>
            <td class="text-center">
              {{ record.totalDebit != null ? (record.totalDebit | number: '1.2-2') : '-' }}
            </td>
            <td class="text-center">
              {{ record.totalCredit != null ? (record.totalCredit | number: '1.2-2') : '-' }}
            </td>
            <td class="text-center">
              {{ record.movementDate ? (record.movementDate | date:'yyyy-MM-dd') : '-' }}
            </td>
          </tr>
          <!-- If no records available -->
          <tr *ngIf="account.pairedRecords.length === 0">
            <td colspan="3" class="text-center text-muted">
              {{ 'REPORTS.NO_DATA' | translate }}
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

    <!-- Export Button -->
    <div class="export-section" *ngIf="reportList && reportList.length > 0">
      <app-export-pdf-button [tableElement]="pdfTable"></app-export-pdf-button>
    </div>
  </div>
</div>
