<div *nzModalTitle><span *ngIf="isUpdate">{{'CURRENCIES.UPDATECURRENCY'|translate}}</span> <span
        *ngIf="!isUpdate">{{'CURRENCIES.ADDNEWCURRENCY'|translate}}</span></div>
<form [formGroup]="form">
    <div class="container">
        <div class="row">
            <div class="col-md-6 form-group">
            <label>{{'CURRENCIES.CURRENCYNAMEEN'|translate}}</label>
            <input type="text" id="currencyNameEn" class="form-control" formControlName="currencyNameEn"
                   name="currencyNameEn">
            <ng-container *ngIf="form && form?.get('currencyNameEn')?.dirty">
              <p class="text-danger text-error"
                 *ngIf="form && form?.get('currencyNameEn')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
              </p>
            </ng-container>
          </div>
            <div class="col-md-6 form-group">
                <label>{{'CURRENCIES.CURRECENCYNAMEAR'|translate}}</label>
                <input type="text" id="currencyNameAr" class="form-control" formControlName="currencyNameAr"
                    name="currencyNameAr">
                <ng-container *ngIf="form && form?.get('currencyNameAr')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('currencyNameAr')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>

        </div>

        <div class="row">
          <div class="col-md-6 form-group">
            <label>{{'CURRENCIES.CURRENCYNICKNAMEEN'|translate}}</label>
            <input type="text" id="currencyNameAr" class="form-control" formControlName="currencyNickNameEn"
                   name="currencyNickNameEn">
            <ng-container *ngIf="form && form?.get('currencyNickNameEn')?.dirty">
              <p class="text-danger text-error"
                 *ngIf="form && form?.get('currencyNickNameEn')?.hasError('required')  ">
                {{'SHARED.THISFIELDISREQUIRED'|translate}}
              </p>
            </ng-container>
          </div>
            <div class="col-md-6 form-group">
                <label>{{'CURRENCIES.CURRENCYNICKNAMEAR'|translate}}</label>
                <input type="text" id="currencyNickNameAr" class="form-control" formControlName="currencyNickNameAr"
                    name="currencyNickNameAr">
                <ng-container *ngIf="form && form?.get('currencyNickNameAr')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('currencyNickNameAr')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
<!--            <div class="col-md-6 form-group">-->
<!--                <label>{{'CURRENCIES.CURRENCYNICKNAMEAR'|translate}}</label>-->
<!--                <input type="text" id="currencyNickNameAr" class="form-control" formControlName="currencyNickNameAr"-->
<!--                    name="currencyNickNameAr">-->
<!--                <ng-container *ngIf="form && form?.get('currencyNickNameAr')?.dirty">-->
<!--                    <p class="text-danger text-error"-->
<!--                        *ngIf="form && form?.get('currencyNickNameAr')?.hasError('required')  ">-->
<!--                        {{'SHARED.THISFIELDISREQUIRED'|translate}}-->
<!--                    </p>-->
<!--                </ng-container>-->
<!--            </div>-->

        </div>

        <div class="row">
            <div class="col-md-6 form-group">
                <label>{{'CURRENCIES.FRACTIONNAMEEN'|translate}}</label>
                <input type="text" id="currencyFrictionNameEn" class="form-control"
                    formControlName="currencyFrictionNameEn" name="currencyFrictionNameEn">
                <ng-container *ngIf="form && form?.get('currencyFrictionNameEn')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('currencyFrictionNameEn')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
            <div class="col-md-6 form-group">
                <label>{{'CURRENCIES.FRACTIONNAMEAR'|translate}}</label>
                <input type="text" id="currencyFrictionNameAr" class="form-control"
                    formControlName="currencyFrictionNameAr" name="currencyFrictionNameAr">
                <ng-container *ngIf="form && form?.get('currencyFrictionNameAr')?.dirty">
                    <p class="text-danger text-error"
                        *ngIf="form && form?.get('currencyFrictionNameAr')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
      <div class="row">
        <div class="form-group col-md-6">
          <label>{{'CURRENCIES.CONVERSIONRATE'|translate}}</label>
          <input type="number" id="conversionRate" class="form-control" formControlName="conversionRate"
                 name="conversionRate">
          <ng-container *ngIf="form && form?.get('conversionRate')?.dirty">
            <p class="text-danger text-error"
               *ngIf="form && form?.get('conversionRate')?.hasError('required')  ">
              {{'SHARED.THISFIELDISREQUIRED'|translate}}
            </p>
          </ng-container>
        </div>
        <div class="col-md-6 form-group d-flex align-items-center">
          <div class="mt-3">
            <label for="isActive" class="form-label">{{'CURRENCIES.ISACTIVE'|translate}}</label>
            <input
              type="checkbox"
              class="form-check-input"
              formControlName="isActive"
              (change)="onCheckboxChange($event)"
            />
          </div>
        </div>
      </div>
        <div class="row">
            <div class="form-group">
                <label>{{'CURRENCIES.NOTES'|translate}}</label>
                <input type="text" id="notes" class="form-control" formControlName="notes" name="notes">
                <ng-container *ngIf="form && form?.get('notes')?.dirty">
                    <p class="text-danger text-error" *ngIf="form && form?.get('notes')?.hasError('required')  ">
                        {{'SHARED.THISFIELDISREQUIRED'|translate}}
                    </p>
                </ng-container>
            </div>
        </div>
    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit"
            (click)="addorEditCurrency()"><span *ngIf="isUpdate">{{'CURRENCIES.UPDATECURRENCY'|translate}}</span> <span
            *ngIf="!isUpdate">{{'CURRENCIES.ADDNEWCURRENCY'|translate}}</span></button>
    </div>
</form>
