.erp-shadow-center {
    box-shadow: 0px 0px 15px rgba(221, 228, 245, 0.8), -20px 20px 0px rgba(221, 228, 245, 0.8), 20px 20px 0px rgba(221, 228, 245, 0.8);
}
:lang(ar) .erp-shadow-end {
    box-shadow: 
    
      20px 20px 0px rgba(221, 228, 245, 0.8);
}
:lang(en) .erp-shadow-end {
    box-shadow: 
    
    -20px 20px 0px rgba(221, 228, 245, 0.8)
}
.text-align-end{
    text-align: end;
}
.text-error{
    font-size: 1.3rem;
   
}
:lang(en) .text-error{
    margin-left: 1rem;
}
:lang(ar) .text-error{
    margin-right: 1rem;
}

/* Enhanced UI Utility Classes */

/* Spacing utilities */
.spacing-sm { margin: 0.5rem; }
.spacing-md { margin: 1rem; }
.spacing-lg { margin: 1.5rem; }
.spacing-xl { margin: 2rem; }

.spacing-y-sm { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.spacing-y-md { margin-top: 1rem; margin-bottom: 1rem; }
.spacing-y-lg { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.spacing-y-xl { margin-top: 2rem; margin-bottom: 2rem; }

.spacing-x-sm { margin-left: 0.5rem; margin-right: 0.5rem; }
.spacing-x-md { margin-left: 1rem; margin-right: 1rem; }
.spacing-x-lg { margin-left: 1.5rem; margin-right: 1.5rem; }
.spacing-x-xl { margin-left: 2rem; margin-right: 2rem; }

/* Padding utilities */
.padding-sm { padding: 0.5rem; }
.padding-md { padding: 1rem; }
.padding-lg { padding: 1.5rem; }
.padding-xl { padding: 2rem; }

/* Border radius utilities */
.rounded-sm { border-radius: 4px; }
.rounded-md { border-radius: 8px; }
.rounded-lg { border-radius: 12px; }
.rounded-xl { border-radius: 16px; }
.rounded-full { border-radius: 50%; }

/* Shadow utilities */
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-md { box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); }
.shadow-lg { box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12); }
.shadow-xl { box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15); }

/* Text utilities */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-grey { color: var(--color-grey) !important; }
.text-light-grey { color: var(--color-light-grey) !important; }
.text-danger { color: var(--color-danger) !important; }

.text-weight-light { font-weight: 300; }
.text-weight-normal { font-weight: 400; }
.text-weight-medium { font-weight: 500; }
.text-weight-semibold { font-weight: 600; }
.text-weight-bold { font-weight: 700; }

.text-size-xs { font-size: 0.75rem; }
.text-size-sm { font-size: 0.85rem; }
.text-size-md { font-size: 1rem; }
.text-size-lg { font-size: 1.125rem; }
.text-size-xl { font-size: 1.25rem; }

/* Background utilities */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-grey { background-color: var(--color-grey) !important; }
.bg-light-grey { background-color: var(--color-light-grey) !important; }
.bg-danger { background-color: var(--color-danger) !important; }
.bg-white { background-color: #ffffff !important; }
.bg-transparent { background-color: transparent !important; }

/* Flexbox utilities */
.flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-sm { gap: 0.5rem; }
.gap-md { gap: 1rem; }
.gap-lg { gap: 1.5rem; }
.gap-xl { gap: 2rem; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Display utilities */
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }
.hidden { display: none; }

/* Overflow utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Width and height utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Transition utilities */
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease; }
.transition-transform { transition: transform 0.3s ease; }
.transition-opacity { transition: opacity 0.3s ease; }

/* Hover effects */
.hover-lift:hover { transform: translateY(-2px); }
.hover-scale:hover { transform: scale(1.05); }
.hover-shadow:hover { box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); }