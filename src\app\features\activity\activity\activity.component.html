<div class="erp-card erp-shadow-end">
  <div class="row my-3 px-3 align-items-center">
    <!-- Search Input -->
    <div class="col-12 col-md-3 mb-2 mb-md-0">
      <input type="text" id="search" class="form-control"
             placeholder="{{'SHARED.TYPEHERETOSEARCH' | translate}}"
             name="search" [(ngModel)]="searchValue">
    </div>

    <!-- Buttons -->
    <div class="col-12 col-md-9 d-flex flex-wrap justify-content-md-end gap-2 text-center">

      <button *ngIf="selectedActivity" (click)="deleteActivity()"
              class="btn btn-danger text-white">{{'ACTIVITY.DELETE' | translate}}</button>

      <button *ngIf="selectedActivity" (click)="updateActivityModal()"
              class="btn btn-primary">{{'ACTIVITY.UPDATE' | translate}}</button>

      <button (click)="addActivityModal()" class="btn btn-primary">
        + {{'ACTIVITY.ADDACTIVITY' | translate}}
      </button>

    </div>
  </div>


  <nz-tree #nzTreeComponent [nzData]="activityList | treeFilters: {activityNumber: searchValue, activityName:searchValue}" [nzCheckStrictly]="true"
        [nzTreeTemplate]="nzTreeTemplate"></nz-tree>
    <ng-template #nzTreeTemplate let-node let-origin="origin">
        <div class="d-flex align-items-end">
            <input type="checkbox" [checked]="origin.checked" id={{origin.id}} name={{origin.id}} class="mb-3"
                (click)="mycheck($event,origin)">
            <nz-table #basicTable [nzData]="['']" [nzShowPagination]="false" nzTableLayout="fixed">
                <thead *ngIf="activityList[0].id == origin.id">
                    <tr>
                        <th nzColumnKey="name">{{'ACTIVITY.ACTIVITYNUMBER'|translate}}</th>
                        <th nzColumnKey="name">{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYNAME'|translate}}</th>
                        <th nzColumnKey="name">{{'ACTIVITY.ADDACTIVITYFORM.EMAIL'|translate}}</th>
                        <th nzColumnKey="name">{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYOPENINGDATE'|translate}}</th>
                        <th nzColumnKey="name">{{'ACTIVITY.ADDACTIVITYFORM.COMMENTS'|translate}}</th>


                    </tr>
                </thead>
                <tbody>
                <tr>
                  <td>{{ origin?.originalData?.activityNumber }}</td>
                  <td>{{ origin?.title }}</td>
                  <td>{{ origin?.originalData?.email }}</td>
                  <td>
                    <span *ngIf="!showARDate">{{ origin?.originalData?.activityOpeningDate | date: 'fullDate' }}</span>
                    <span *ngIf="showARDate">{{ origin?.originalData?.arabicActivityOpeningDate }}</span>
                  </td>
                  <td>{{ origin?.notes }}</td>
                </tr>
                </tbody>
            </nz-table>
        </div>
    </ng-template>

  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h2>{{ 'PDF.ACTIVITYREPORTS' | translate }}</h2>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <!-- PDF Table -->
    <table class="table table-bordered pdf-table">
      <thead>
      <tr class="text-blue-500">
        <th>{{'ACTIVITY.ACTIVITYNUMBER'|translate}}</th>
        <th>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYNAME'|translate}}</th>
        <th>{{'ACTIVITY.ADDACTIVITYFORM.EMAIL'|translate}}</th>
        <th>{{'ACTIVITY.ADDACTIVITYFORM.ACTIVITYOPENINGDATE'|translate}}</th>
        <th>{{ 'ACCOUNT.LEVEL' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let activity of flattenedActivities">
        <td>{{ activity.activityNumber }}</td>
        <td>{{ lang == "en" ? activity.activity_Latin_Name : activity?.activityName }}</td>
        <td>{{ activity.email }}</td>
        <td>{{activity.activityOpeningDate| date: 'yyyy-MM-dd'}}</td>
        <td>{{ activity.level }}</td>
      </tr>
      </tbody>
    </table>

  </div>

  <div class="row mt-5 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
