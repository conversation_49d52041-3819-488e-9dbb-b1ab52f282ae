import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UsersComponent } from './users.component';
import { UsersRoutingModule } from './users-routing.module';
import { SharedModule } from '../../shared/shared.module';
import { EditPermissionsComponent } from './edit-permissions/edit-permissions.component';
import { AddUserComponent } from './add-user/add-user.component';
import { EditPassowrdComponent } from './edit-passowrd/edit-passowrd.component';
import { EditUserComponent } from './edit-user/edit-user.component';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    UsersComponent,
    EditPermissionsComponent,
    AddUserComponent,
    EditPassowrdComponent,
    EditUserComponent,
  ],
  imports: [
    CommonModule,
    UsersRoutingModule,
    SharedModule,
    ReusableDropdownComponent,
  ]
})
export class UsersModule { }
