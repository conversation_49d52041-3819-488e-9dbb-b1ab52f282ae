import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from './components/layout/layout.component';
import { SharedModule } from '../shared/shared.module';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from '../app-routing.module';
import { LoginComponent } from './components/login/login.component';
import { CoreRoutingModule } from './core-routing.module';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { BaseApiUriInterceptor } from './interceptors/base-api-uri.interceptor';
import { JWTInterceptor } from './interceptors/jwt.interceptor';
import { NavbarComponent } from './components/navbar/navbar.component';
import { RegisterComponent } from './components/register/register.component';
import {ReusableDropdownComponent} from "../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    LayoutComponent,
    LoginComponent,
    NavbarComponent,
  ],
    imports: [
        CommonModule, BrowserModule, AppRoutingModule, SharedModule, CoreRoutingModule, HttpClientModule, ReusableDropdownComponent
    ],
  exports: [

  ],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: BaseApiUriInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: JWTInterceptor, multi: true },
  ]
})
export class CoreModule { }
