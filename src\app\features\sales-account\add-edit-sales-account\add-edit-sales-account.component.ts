import { Component, inject } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';
import { SalesAccountService } from '../sales-account.service';
import { BondsService } from '../../bonds/services/bonds.service';
import { MessageService } from 'primeng/api';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-add-edit-sales-account',
  templateUrl: './add-edit-sales-account.component.html',
  styleUrl: './add-edit-sales-account.component.scss'
})
export class AddEditSalesAccountComponent {

  form: FormGroup;
  data: any;
  isUpdate: boolean = false;
  readonly nzModalData: any = inject(NZ_MODAL_DATA);
  accountsList: any;

  constructor(
    private bonds: BondsService,
    private modal: NzModalRef,
    private fb: FormBuilder,
    private salesaccount: SalesAccountService,
    private translate: TranslateService,
    private messageService: MessageService
  ) {}

  ngOnInit(): void {
    this.createForm();
    this.getAccounts();
  }

  addorEditSalesAccount() {
    if (this.form.valid) {
      const data = this.form.getRawValue();

      if (!this.isUpdate) {
        this.salesaccount.addsalesaccount(data).subscribe(res => {
          if (res.message === 'Sales Account created') {
            this.showSuccessToast('TOAST.SALES_ACCOUNT_CREATED');
          } else {
            this.showErrorToast('TOAST.SOMETHING_WENT_WRONG');
          }
          this.modal.destroy(res);
        });
      } else {
        this.salesaccount.updatesalesaccount(data.id, data).subscribe(res => {
          if (res.message === 'Sales Account updated') {
            this.showSuccessToast('TOAST.SALES_ACCOUNT_UPDATED');
          } else {
            this.showErrorToast('TOAST.SOMETHING_WENT_WRONG');
          }
          this.modal.destroy(res);
        });
      }

    } else {
      Object.values(this.form.controls).forEach((control) => {
        if (control.invalid && !control.dirty) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  private showSuccessToast(detailKey: string): void {
    const summary = this.translate.instant('TOAST.SUCCESS');
    const detail = this.translate.instant(detailKey);
    this.messageService.add({ severity: 'success', summary, detail, life: 3000 });
  }

  private showErrorToast(detailKey: string): void {
    const summary = this.translate.instant('TOAST.ERROR');
    const detail = this.translate.instant(detailKey);
    this.messageService.add({ severity: 'error', summary, detail, life: 3000 });
  }

  createForm() {
    this.data = this.nzModalData;
    if (this.data) {
      this.isUpdate = true;
    }
    this.form = this.fb.group({
      id: [this.data?.id || "3fa85f64-5717-4562-b3fc-2c963f66afa6"],
      code: [this.data?.code],
      name: [this.data?.name],
      nameAr: [this.data?.nameAr],
      accountNumber: [this.data?.accountNumber || '', Validators.required],
      openDate: [this.data?.openDate],
      notes: [this.data?.notes],
      branchId: [this.data?.branchId],
      isActive: [this.data?.isActive || false],
    });
  }

  getAccounts() {
    this.bonds.getAccounts().subscribe(res => {
      this.accountsList = res.data;

      if (this.isUpdate && this.data && this.data.accountNumber) {
        const selectedAccount = this.accountsList.find(
          (account: any) => account.accountNumber === this.data.accountNumber
        );

        if (selectedAccount) {
          this.onSelectedAccount(selectedAccount);
        }
      }
    });
  }

  get accountNameControl(): FormControl {
    return this.form.get('accountNumber') as FormControl;
  }

  onSelectedAccount(selected: any): void {
    this.form.patchValue({
      accountNumber: selected.accountNumber,
    });
  }

  destroyModal(): void {
    this.modal.destroy();
  }
}
