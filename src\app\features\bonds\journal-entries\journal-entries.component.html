<div class="erp-card erp-shadow-end mt-5">
  <div class="row my-3 row px-3 text-align-end">
    <div class="col-sm-12 col-md-6">
      <div class="d-flex align-items-center">
        <div class="mr-3">
          {{'SHARED.SHOW' |translate}}
        </div>
        <div class="mx-3">
          <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-12 col-md-3">
      <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
             name="search" [(ngModel)]="searchValue">
    </div>
    <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end">
      <button (click)="addJournalEntry()" class="btn btn-primary">+
        {{'STAKEHOLDERS.ADDJOURNALENTRY' |translate}}</button>
    </div>

  </div>
  <div class="my-3 row px-3">
    <nz-table #nestedTable #sortTable [nzPageSize]="pageSize" [nzData]="bondsList | tablefilters: {docDate: searchValue, amount:searchValue, branchName:searchValue}" nzTableLayout="fixed">
      <thead>
      <tr>
        <th></th>
        <th nzColumnKey="name">{{'BONDS.DATE'|translate}}</th>
        <th nzColumnKey="name">{{'BONDS.AMOUNT'|translate}}</th>
        <th nzColumnKey="name">{{'BONDS.BRANCHNAME'|translate}}</th>
        <th>{{ 'BONDS.ACTIONS' | translate }}</th>
      </tr>
      </thead>
      <tbody>
        @for (data of nestedTable.data; track data) {
          <tr>
            <td [(nzExpand)]="data.expand"></td>
            <td>{{ data.docDate |date:'dd/MM/yyyy' }}</td>
            <td>{{ data.amount }}</td>
            <td>{{ data.branchName }}</td>
            <td>
              <button
                [ngClass]="data.isPost ? 'erp-btn erp-btn-primary' : 'btn btn-primary'"
                [disabled]="data.isPosted"
                (click)="onEditBond(data.id)">
                {{'BONDS.POST' | translate}}
              </button>
            </td>

          </tr>
          <tr [nzExpand]="data.expand">
            <nz-table #innerTable [nzData]="data.financialReportLines" nzSize="middle"
                      [nzShowPagination]="false">
              <thead>
              <tr>
                <th>{{'BONDS.ACCOUNTNUMBER'|translate}}</th>
                <th>{{'BONDS.COSTCENTER'|translate}}</th>
                <th>{{'BONDS.EXPLANATIONAR'|translate}}</th>
                <th>{{'BONDS.EXPLANATIONEN'|translate}}</th>

                <th>{{'BONDS.PROJECT'|translate}}</th>
                <th>{{'BONDS.CREDIT'|translate}}</th>
                <th>{{'BONDS.DEBIT'|translate}}</th>

              </tr>
              </thead>
              <tbody>
                @for (data of innerTable.data; track data) {
                  <tr>
                    <td>{{ data.accountName }}</td>
                    <td>{{ data.centerName }}</td>
                    <td>{{ data.descriptionAr }}</td>
                    <td>{{ data.descriptionEn }}</td>

                    <td>{{ data.projectName }}</td>
                    <td>{{ data.totalCredit }}</td>
                    <td>{{ data.totalDebit }}</td>

                  </tr>
                }
              </tbody>
            </nz-table>
          </tr>
        }
      </tbody>
    </nz-table>
  </div>
  <div #pdfTable id="pdfTable" class="pdf-container">
    <!-- PDF Header -->
    <div style="text-align: center; margin-bottom: 20px; border-bottom: 2px solid lightblue; padding-bottom: 10px;">
      <h3>{{ 'PDF.JOURNALREPORTS' | translate }}</h3>
      <div style="display: flex; justify-content: space-between; margin-top: 10px;">
        <div>
          {{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}
        </div>
        <div>
          {{ 'PDF.REF' | translate }}: {{ currentRef }}
        </div>
      </div>
    </div>

    <table>
      <thead>
      <tr>
        <th>{{'BONDS.DATE' | translate}}</th>
        <th>{{'BONDS.AMOUNT' | translate}}</th>
        <th>{{'BONDS.BRANCHNAME' | translate}}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let row of flattenedBonds" [class.parent-row]="row.isParent" [class.child-row]="!row.isParent">
        <td>{{ row.date }}</td>
        <td>{{ row.amount }}</td>
        <td>{{ row.branchName }}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <div class="row mt-3 text-center">
    <app-export-pdf-button
      [tableElement]="pdfTable"
    >
    </app-export-pdf-button>
  </div>
</div>
