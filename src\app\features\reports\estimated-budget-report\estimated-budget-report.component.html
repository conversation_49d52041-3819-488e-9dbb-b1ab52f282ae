<div class="erp-card erp-shadow-end">
  <app-breadcrumb [items]="breadcrumbItems"></app-breadcrumb>
  <h3 class="text-center my-3 text-primary">{{"REPORTS.ESTIMATEDBUDGETREPORT"|translate}}</h3>
  <div class="row my-3 row px-3">
    <div [formGroup]="form">
      <!-- Date Pickers -->
      <div class="row mb-3">
        <div class="col-md-6">
          <label class="form-label">{{"SYSTEMSETTINGS.STARTDATE" | translate}}</label>
          <nz-date-picker formControlName="startDate" class="datepicker"></nz-date-picker>
        </div>
        <div class="col-md-6">
          <label class="form-label">{{"SYSTEMSETTINGS.ENDDATE" | translate}}</label>
          <nz-date-picker formControlName="endDate" class="datepicker"></nz-date-picker>
          <p *ngIf="form.hasError('dateRange')" class="text-danger" style="font-size: 12px;">
            {{ "VALIDATORS.DATERANGE" | translate }}
          </p>
        </div>
      </div>

      <div class="col-md-4 mx-auto my-4">
        <button type="button" class="btn btn-primary w-100" (click)="onSubmit()">
          {{ "REPORTS.SUBMIT" | translate }}
        </button>
      </div>
    </div>
    <nz-table #basicTable [nzData]="reportsList" [nzShowPagination]="false" nzTableLayout="fixed">
      <thead>
      <tr>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let report of reportsList">
        <td>{{ report.accountName }}</td>
        <td>{{ report.totalDebitFromStartDateToEndDate | number: '1.2-2' }}</td>
        <td>{{ report.totalCreditFromStartDateToEndDate | number: '1.2-2' }}</td>
      </tr>
      </tbody>
    </nz-table>
  </div>

  <div #pdfTable id="pdfTable" class="pdf-container mt-4">
    <!-- PDF Header -->
    <div class="text-center mb-4 border-bottom pb-3">
      <h2>{{"REPORTS.ESTIMATEDBUDGETREPORT" | translate}}</h2>
      <div class="d-flex justify-content-between mt-2">
        <div>{{ 'PDF.DATE' | translate }}: {{ currentDate | date:'yyyy-MM-dd' }}</div>
        <div>{{ 'PDF.REF' | translate }}: {{ currentRef }}</div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section mb-3 p-3 rounded text-center">
      <div class="grid-container">
        <!-- Section 1 -->
        <div class="section">
          <div>
            <strong>{{ 'SYSTEMSETTINGS.DATERANGE' | translate }}:</strong>
            <span>
          {{ form.value.startDate ? (form.value.startDate | date:'yyyy-MM-dd') : '-' }} -
              {{ form.value.endDate ? (form.value.endDate | date:'yyyy-MM-dd') : '-' }}
        </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Report Table -->
    <table class="table table-bordered">
      <thead>
      <tr>
        <th>{{ 'ACCOUNT.ADDACCOUNTFORM.ACCOUNTNAME' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_DEBIT' | translate }}</th>
        <th>{{ 'REPORTS.TOTAL_CREDIT' | translate }}</th>
      </tr>
      </thead>
      <tbody>
      <tr *ngFor="let report of reportsList">
        <td>{{ report.accountName }}</td>
        <td>{{ report.totalDebitFromStartDateToEndDate | number: '1.2-2' }}</td>
        <td>{{ report.totalCreditFromStartDateToEndDate | number: '1.2-2' }}</td>
      </tr>
      </tbody>
    </table>
  </div>

  <!-- Export Button -->
  <div class="row mt-3 text-center">
    <app-export-pdf-button [tableElement]="pdfTable"></app-export-pdf-button>
  </div>

</div>
