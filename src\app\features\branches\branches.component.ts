import { Component, OnInit } from '@angular/core';
import { BranchService } from './branch.service';
import { branch } from '../../core/models/branch';
import { NzModalService } from 'ng-zorro-antd/modal';
import { TranslateService } from '@ngx-translate/core';
import { AddEditBranchComponent } from './add-edit-branch/add-edit-branch.component';

@Component({
  selector: 'app-branches',
  templateUrl: './branches.component.html',
  styleUrl: './branches.component.scss'
})
export class BranchesComponent implements OnInit {
  branchList: branch[];

  identityList: any;
  pageSize: number = 5;
  lang: string;
  searchValue: string = '';
  langDirection: 'ltr' | 'rtl' = 'ltr';
  constructor(private branch: BranchService, private modalService: NzModalService, private translate: TranslateService) {
    this.getDirection(this.translate.currentLang);
    this.langDirection = this.translate.currentLang == 'ar' ? 'rtl' : 'ltr'
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getDirection(this.lang);
    })
  }


  ngOnInit(): void {
    this.getBranches();
  }
  getDirection(lang: string) {
    if (this.lang === 'en') {
      this.langDirection = 'ltr';
    } else {
      this.langDirection = 'rtl';

    }
  }
  getBranches() {
    this.branch.getBranches().subscribe(res => {
      this.branchList = res.data;
    })
  }
  addBranch() {
    this.modalService.create({
      nzContent: AddEditBranchComponent,
      nzDirection: this.langDirection
    }).afterClose.subscribe((res) => {
      if (res) {

        this.getBranches();
      }
    })
  }
  delete(id: any) {
    this.branch.deleteBranch(id).subscribe(res => {
      this.getBranches();
    })
  }
  EditBranch(data: any) {
    this.modalService.create({
      nzContent: AddEditBranchComponent,
      nzDirection: this.langDirection,
      nzData: data
    }).afterClose.subscribe((res) => {
      if (res) {
        this.getBranches();
      }
    })
  }
}