import { Component } from '@angular/core';
import { BondsService } from '../services/bonds.service';
import {TranslateService} from "@ngx-translate/core";
import {formatDate} from "@angular/common";

@Component({
  selector: 'app-cash-receipt',
  templateUrl: './cash-receipt.component.html',
  styleUrl: './cash-receipt.component.scss'
})
export class CashReceiptComponent {
  type:string='2F53C77C-0571-4798-BA5B-B4B0EE118655';
  searchValue: string = '';
  pageSize: number = 5;
  currentPage: number = 1;
  bondsList: any;
  lang: string
  refNumber: number
  selectedBondToEdit: any;
  currentDate: Date = new Date()
  currentRef: string
  flattenedBonds: any[] = [];
  constructor(private bonds: BondsService, private translate: TranslateService) {
    this.translate.onLangChange.subscribe((langObject) => {
      this.lang = langObject.lang;
      this.getBonds()
    })
  }


  ngOnInit(): void {
    this.getBonds();
    const randomNum = Math.floor(Math.random() * 1000);
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split('T')[0].replace(/-/g, '');
    this.currentRef = `FIN-${formattedDate}-${randomNum}`;
  }
  getBonds() {
    this.bonds.getReports(this.type).subscribe(res => {
      this.bondsList = res.data
      this.bondsList = this.bondsList.map((item: any, index: any) => ({
        ...item,
        refNumber: index + 1
      }));
      this.bondsList.forEach((e:any) => {
        e.expand=false
      });
      this.flattenedBonds = this.flattenBondsData(res.data)
    })
  }

  flattenBondsData(bondsList: any[]): any[] {
    const flattenedData: any[] = [];

    bondsList.forEach((bond: any) => {
      // Add parent row
      flattenedData.push({
        isParent: true,
        date: formatDate(bond.docDate, 'dd/MM/yyyy', 'en'),
        amount: bond.amount,
        branchName: bond.branchName || '-'
      });

      // Add child rows
      bond.financialReportLines.forEach((line: any) => {
        flattenedData.push({
          isParent: false,
          date: line.accountName,
          amount: line.centerName,
          branchName: line.projectName
        });
      });
    });

    return flattenedData;
  }

  editNotPayable(row: any) {
    this.selectedBondToEdit = {
      id: row.id,
      refNumber: row.refNumber,
      amount: row.amount,
      checkNum: row.financialReportLines[0]?.checkNumber,
      checkDate: row.financialReportLines[0]?.checkDate,
      bankName: row.financialReportLines[0]?.bankName,
      currencyId: row.financialReportLines[0]?.currencyId,
      conversionFactor: row.conversionFactor, // if available
      movementDate: row.financialReportLines[0]?.movementDate,
      hijriDate: row.financialReportLines[0]?.hijriDate,
      debtors: row.financialReportLines.filter((l: any) => l.isDebitAccount == true),
      creditors: row.financialReportLines.filter((l: any) => l.isDebitAccount == false)
    };
  }

  onEditBond(id: string) {
    this.bonds.changeReportType(id).subscribe(res => {
      this.getBonds()
    })
  }


  onPageChange(pageIndex: number): void {
    this.currentPage = pageIndex;
  }
}
