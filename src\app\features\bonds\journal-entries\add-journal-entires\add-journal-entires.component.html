<div class="erp-card erp-shadow-end">
  <div class="row my-3 row px-3">
    <div [formGroup]="form">
      <div class="row">
        <div class="col-md-6">
          <label>{{'BONDS.DEBIT'|translate}}</label>
          <input formControlName="amountDebtor" (change)="disableCredit()" min="0" class="form-control"
                 type="number" />
        </div>
        <div class="col-md-6">
          <label>{{'BONDS.CREDIT'|translate}}</label>
          <input formControlName="amountCreditor" (change)="disableDebt()" min="0" class="form-control"
                 type="number" />
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label>{{ 'BONDS.ACCOUNTNUMBER'|translate }}</label>
          <app-reusable-dropdown
            [options]="accountsList"
            [displayKey]="'accountName - accountNumber'"
            [valueKey]="'id'"
            [formControl]="accountNameControl"
            [selectedId]="form?.get('accountNumber')?.value"
            (selectedValue)="onSelectedAccount($event)">
          </app-reusable-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'BONDS.PROJECT' | translate }}</label>
          <app-reusable-dropdown
            [options]="projectList"
            [displayKey]="'projectName - projectNumber'"
            [valueKey]="'projectNumber'"
            [formControl]="projectControl"
            [selectedId]="form?.get('projectNumber')?.value"
            (selectedValue)="onProjectSelected($event)">
          </app-reusable-dropdown>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <label class="form-label">{{'BONDS.TRANSACTIONDATE'|translate}}</label>
          <nz-date-picker (ngModelChange)="dateHandler()" formControlName="movementDate" class="datepicker"></nz-date-picker>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>{{'BONDS.TRANSACTIONHIJRIDATE'|translate}}</label>
            <br />
            <input [value]="form?.get('movementHijriDate')?.value |arabicNumerals" [attr.disabled]="true"
                   class="form-control" type="text" />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group">
            <label>{{ 'BONDS.CURRENCY' | translate }}</label>
            <app-reusable-dropdown
              [options]="currencyList"
              [displayKey]="lang == 'en' ? 'currencyNameEn - currencyCode' : lang == 'ar' ? 'currencyNameAr - currencyCode' : 'currencyNameAr - currencyCode'"
              [valueKey]="'id'"
              [formControl]="currencyIdControl"
              [selectedId]="form?.get('currencyId')?.value"
              (selectedValue)="onCurrencySelected($event)">
            </app-reusable-dropdown>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group">
            <label>{{'BONDS.CONVERSIONRATE'|translate}}</label>
            <input type="text" id="conversionFactor" class="form-control" formControlName="conversionFactor"
                   name="conversionFactor">
          </div>
        </div>

      </div>

      <div class="row mb-3">
        <div class="form-group">
          <label>{{ 'BONDS.COSTCENTER'|translate }}</label>
          <app-reusable-dropdown
            [options]="costList"
            [displayKey]="'costCenterName - costCenterNumber'"
            [valueKey]="'costCenterNumber'"
            [formControl]="costCenterControl"
            [selectedId]="form?.get('centerNumber')?.value"
            (selectedValue)="onCostCenterSelected($event)">
          </app-reusable-dropdown>

        </div>
      </div>
      <div>
        <div class="mb-3 col-md-12">
          <label class="form-label">{{'BONDS.EXPLANATIONAR'|translate}}</label>
          <textarea formControlName="descriptionAr" class="form-control"></textarea>
        </div>

        <div class="mb-3 col-md-12">
          <label class="form-label">{{'BONDS.EXPLANATIONEN'|translate}}</label>
          <textarea formControlName="descriptionEn" class="form-control"></textarea>
        </div>
      </div>
      <div class="d-flex justify-content-end">
        <button class="btn btn-primary" (click)="addRow()">{{'SHARED.ADDROW'|translate}}</button>
      </div>

    </div>
  </div>
  <nz-table #basicTable [nzPageSize]="pageSize" [nzData]="journalEntries$ | async" nzTableLayout="fixed">
    <thead>
    <tr>
      <th nzColumnKey="name">{{'BONDS.SEQUENCE'|translate}}</th>
      <th nzColumnKey="name">{{'BONDS.DEBIT'|translate}}</th>
      <th nzColumnKey="name">{{'BONDS.CREDIT'|translate}}</th>
      <th nzColumnKey="name">{{'BONDS.ACCOUNTNUMBER'|translate}}</th>
      <th nzColumnKey="name">{{'BONDS.PROJECT'|translate}}</th>
      <th nzColumnKey="name">{{'BONDS.EXPLANATIONAR'|translate}}
      </th>
      <th nzColumnKey="name">{{'BONDS.EXPLANATIONEN'|translate}}
      </th>
      <th nzColumnKey="name">{{'BONDS.TRANSACTIONDATE'|translate}}</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let data of basicTable.data">
      <td>{{ data.sequence }}</td>
      <td>{{ data.amountDebtor }}</td>
      <td>{{ data.amountCreditor }}</td>
      <td>{{ data.accountNumber}}</td>
      <!-- <td>{{ data.activityNumber}}</td> -->
      <td>{{ data.projectNumber}}</td>
      <td>{{ data.descriptionAr}}</td>
      <td>{{ data.descriptionEn}}</td>
      <td>{{ data.movementDate | date:'dd-MM-YYYY'}}</td>
    </tr>
    </tbody>
  </nz-table>
  <p *ngIf="showWarning" class="alert alert-warning">{{'BONDS.WARRNINGMSG'|translate}}</p>
  <div class="d-flex justify-content-end">
    <button class="btn btn-primary m-2" [disabled]="showWarning || tempArr.length==0"
            (click)="addReport()">{{'SHARED.SAVE'|translate}}</button>
  </div>
</div>
