<div class="erp-card erp-shadow-end">
    <div class="my-3 row px-3">
        <div class="col-sm-12 col-md-9">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    {{'SHARED.SHOW' |translate}}
                </div>
                <div class="mx-3">
                    <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-3">
            <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                name="search" [(ngModel)]="searchValue">
        </div>

    </div>
    <nz-table #basicTable #sortTable [nzPageSize]="pageSize"
        [nzData]="tenantsList | tablefilters: {name: searchValue, officialEmail:searchValue, hotLine:searchValue, isActive:searchValue}"
        nzTableLayout="fixed">
        <thead>
            <tr>
                <th nzColumnKey="name" [nzSortFn]="sortFnName">{{'TENANTS.CONTACTNAME'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnEmail">{{'TENANTS.EMAIL'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNumber">{{'TENANTS.HOTLINE'|translate}}</th>
                <th nzColumnKey="name" [nzSortFn]="sortFnNActive">{{'TENANTS.STATUS'|translate}}</th>
                <th nzColumnKey="name">{{'TENANTS.ACTIONS'|translate}}</th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let data of basicTable.data">
                <td>{{ data.name }}</td>
                <td>{{ data.officialEmail }}</td>
                <td>{{ data.hotLine}}</td>
                <td> <button *ngIf=" data.isActive" class="btn btn-primary btn-sm text-white" disabled>
                        {{'SHARED.ACTIVE'|translate}}</button>
                    <button *ngIf="!data.isActive" class="btn btn-danger btn-sm text-white"
                        disabled>{{'SHARED.INACTIVE'|translate}}</button>
                </td>
                <td><span data-bs-toggle="dropdown" aria-expanded="false"><svg xmlns="http://www.w3.org/2000/svg"
                            width="16" height="16" fill="currentColor" class="bi bi-three-dots-vertical"
                            viewBox="0 0 16 16">
                            <path
                                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0m0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0" />
                        </svg></span>
                    <ul class="dropdown-menu">
                        <li class="p-2"><button *ngIf="!data.isActive" (click)="changeActivation(data)"
                                class="erp-btn erp-btn-primary ">
                            {{'SHARED.ACTIVE'|translate}} </button>
                            <button *ngIf="data.isActive"  (click)="changeActivation(data)"
                                class="erp-btn erp-btn-danger">
                            {{'SHARED.INACTIVE'|translate}} </button>
                        </li>
                        <li class="p-2"><button (click)="editTenantModule(data)"
                                class="erp-btn erp-btn-babyblue">{{"TENANTS.EDITTENANTMODULE"|translate}}</button>
                        </li>
                    </ul>
                </td>
            </tr>
        </tbody>
    </nz-table>
</div>