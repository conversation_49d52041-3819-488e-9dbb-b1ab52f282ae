import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivityComponent } from './activity/activity.component';
import { AddEditActivityComponent } from './add-edit-activity/add-edit-activity.component';
import { SharedModule } from '../../shared/shared.module';
import { ActivityRoutingModule } from './activity/activity-routing.module';



@NgModule({
  declarations: [
    ActivityComponent,
    AddEditActivityComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    ActivityRoutingModule
  ]
})
export class ActivityModule { }
