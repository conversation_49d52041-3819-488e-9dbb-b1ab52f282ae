<div class="erp-card erp-shadow-end">
    <div class="my-3 row px-3">
        <div class="col-sm-12 col-md-6">
            <div class="d-flex align-items-center">
                <div class="mr-3">
                    {{'SHARED.SHOW' |translate}}
                </div>
                <div class="mx-3">
                    <select class="form-select" [(ngModel)]="pageSize" aria-label="Default select example">
                        <option value="5">5</option>
                        <option value="10">10</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="col-sm-12 col-md-3">
            <input type="text" id="search" class="form-control" placeholder="{{'SHARED.TYPEHERETOSEARCH'|translate}}"
                name="search" [(ngModel)]="searchValue">
        </div>
        <div class="col-xs-12 col-sm-12 col-md-3 md:text-center text-align-end"><button (click)="addIdentity()"
                class="btn btn-primary">+
                {{'IDENTITY.ADDIDENTITY' |translate}}</button></div>
    </div>
    <nz-table #basicTable #sortTable [nzPageSize]="pageSize"
        [nzData]="identityList | tablefilters: {identity_Code: searchValue, identity_Name:searchValue, identity_Name_Latin:searchValue}" nzTableLayout="fixed">
        <thead>
            <tr>
                <th nzColumnKey="name">{{'IDENTITY.IDENTITYCODE'|translate}}</th>
                <th nzColumnKey="name">{{'IDENTITY.IDENTITYNAMEEN'|translate}}</th>
                <th nzColumnKey="name">{{'IDENTITY.IDENTITYNAMEAR'|translate}}</th>
            </tr>
        </thead>
        <tbody>

            <tr *ngFor="let data of basicTable.data">
                <td>{{ data.identity_Code }}</td>
                <td>{{ data.identity_Name_Latin}}</td>
                <td>{{ data.identity_Name }}</td>

            </tr>
        </tbody>
    </nz-table>
</div>