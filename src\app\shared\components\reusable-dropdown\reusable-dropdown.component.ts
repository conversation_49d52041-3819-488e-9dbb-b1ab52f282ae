import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
import { map, Observable, startWith } from 'rxjs';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

@Component({
  selector: 'app-reusable-dropdown',
  templateUrl: './reusable-dropdown.component.html',
  styleUrl: './reusable-dropdown.component.scss',
  imports: [
    FormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    ReactiveFormsModule,
    AsyncPipe,
  ],
  standalone: true,
})
export class ReusableDropdownComponent implements OnInit {
  @Input() options: any[] = [];
  @Input() displayKey: string = 'label';
  @Input() valueKey: string = 'id';
  @Input() selectedId: any = null;
  @Input() formControl: FormControl;
  @Output() selectedValue = new EventEmitter<any>();

  myControl = new FormControl('');
  filteredOptions: Observable<any[]>;

  ngOnInit() {
    // Initialize filteredOptions with valueChanges
    this.filteredOptions = this.myControl.valueChanges.pipe(
      startWith(''),
      map((value) => this._filter(value))
    );

    // Listen to formControl value changes
    this.formControl.valueChanges.subscribe((value) => {
      if (value === '' || value === null || value === undefined) {
        this.myControl.setValue(''); // Reset myControl value
        this.selectedValue.emit(null); // Emit null when value is reset
      }
    });

    // Update selected value if selectedId is provided
    this.updateSelectedValue();
  }

  getDisplayValue(option: any): string {
    const keys = this.displayKey.split(' - ').map((key) => key.trim());
    return keys.map((key) => option[key]).join(' - ');
  }

  private updateSelectedValue(): void {
    if (this.selectedId) {
      const selectedOption = this.options?.find(
        (option) => option[this.valueKey] == this.selectedId
      );
      if (selectedOption) {
        this.myControl.setValue(this.getDisplayValue(selectedOption));
      }
    } else {
      this.myControl.setValue(''); // Reset myControl value if selectedId is null
      this.selectedValue?.emit(null);
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['options'] && this.options?.length > 0) {
      this.updateSelectedValue();
      this.filteredOptions = this.myControl.valueChanges.pipe(
        startWith(''),
        map((value) => this._filter(value || ''))
      );
    }

    if (changes['selectedId']) {
      this.updateSelectedValue();
    }
  }

  private _filter(value: string | null): any[] {
    const filterValue = (value || '').toLowerCase(); // Handle null values
    if (!this.options || this.options.length === 0) {
      return [];
    }

    return this.options.filter((option) => {
      const displayValue = this.getDisplayValue(option);
      return displayValue.toLowerCase().includes(filterValue);
    });
  }

  onOptionSelected(option: any): void {
    this.myControl.setValue(this.getDisplayValue(option));
    this.selectedValue.emit(option);
  }
}
