.navbar {
    width: 100%;
    background: linear-gradient(135deg, var(--color-primary) 0%, rgba(29, 76, 186, 0.95) 100%);
    border-radius: 1.5rem;
    color: white;
    padding: 2rem 1rem;
    font-size: 1.4rem;
    box-shadow: 0 8px 32px rgba(29, 76, 186, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
    }
}

.sub-navbar {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: auto;
    width: 85%;
    background: linear-gradient(135deg, var(--color-primary) 0%, rgba(29, 76, 186, 0.9) 100%);
    border-radius: 1.5rem;
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(29, 76, 186, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.tenant-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-weight: 500;
}

.tenant-icon {
    font-size: 1.8rem;
    opacity: 0.9;
}

.tenant-name {
    font-size: 1.4rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

// Date Section
.date-section {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 1.2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
}

.date-icon {
    font-size: 2.2rem;
    opacity: 0.9;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.date-content {
    .hijri-date {
        font-size: 1.4rem;
        font-weight: 600;
        margin-bottom: 0.3rem;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .gregorian-date {
        font-size: 1.1rem;
        opacity: 0.85;
        font-weight: 400;
    }
}

// User Info Section
.user-info {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 1.2rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;

    &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    }
}

.user-email {
    font-size: 1.3rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
.user-name{
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);    
}

.status-dot {
    width: 0.7rem;
    height: 0.7rem;
    background: #4ade80;
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(74, 222, 128, 0.6);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(0.95); }
}

// Profile Section
.profile-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.round-image-container {
    width: 8rem;
    height: 8rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;

    &:hover {
        transform: scale(1.05);
        border-color: rgba(255, 255, 255, 0.5);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    &:lang(en) {
        margin-right: 1rem;
    }

    &:lang(ar) {
        margin-left: 1rem;
    }
}

.round-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.profile-status {
    position: absolute;
    bottom: 0.3rem;
    right: 0.3rem;
    width: 1.8rem;
    height: 1.8rem;
    background: #4ade80;
    border: 2px solid white;
    border-radius: 50%;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

// RTL Support for Arabic
[dir="rtl"] {
    .date-section {
        text-align: right;
        flex-direction: row-reverse;
    }

    .date-content {
        text-align: right;
    }

    .user-info {
        text-align: right;
    }

    .user-status {
        flex-direction: row-reverse;
    }

    .profile-section {
        justify-content: flex-start;
    }

    .round-image-container {
        &:lang(ar) {
            margin-right: 1rem;
            margin-left: 0;
        }
    }

    .profile-status {
        right: auto;
        left: 0.3rem;
    }

    .tenant-info {
        flex-direction: row-reverse;
        text-align: right;
    }
}

// LTR Support for English
[dir="ltr"] {
    .date-section {
        text-align: left;
        flex-direction: row;
    }

    .date-content {
        text-align: left;
    }

    .user-info {
        text-align: center;
    }

    .user-status {
        flex-direction: row;
    }

    .profile-section {
        justify-content: flex-end;
    }

    .round-image-container {
        &:lang(en) {
            margin-left: 1rem;
            margin-right: 0;
        }
    }

    .profile-status {
        right: 0.3rem;
        left: auto;
    }

    .tenant-info {
        flex-direction: row;
        text-align: left;
    }
}

// Responsive Design
// Large tablets and small desktops
@media screen and (max-width: 992px) {
    .navbar {
        padding: 1.8rem 1rem;
    }

    .date-section,
    .user-info {
        padding: 1rem 1.3rem;
    }

    .round-image-container {
        width: 7.5rem;
        height: 7.5rem;
    }
}

// Tablets
@media screen and (max-width: 768px) {
    .navbar {
        padding: 1.5rem 0.8rem;
        border-radius: 1.2rem;
    }

    .sub-navbar {
        width: 95%;
        padding: 0.8rem 1.5rem;
        border-radius: 1.2rem;
        margin-bottom: 1.5rem;
    }

    .tenant-info {
        gap: 0.8rem;
    }

    .tenant-icon {
        font-size: 1.6rem;
    }

    .tenant-name {
        font-size: 1.2rem;
    }

    .navbar .row {
        margin: 0;
        gap: 1rem;
    }

    .navbar .col-md-4 {
        padding: 0 0.5rem;
        margin-bottom: 1rem;
    }

    .date-section,
    .user-info {
        padding: 1rem 1.2rem;
        border-radius: 1rem;
        width: 100%;
        max-width: none;
    }

    .date-section {
        gap: 1rem;
    }

    .date-icon {
        font-size: 2rem;
        flex-shrink: 0;
    }

    .date-content {
        .hijri-date {
            font-size: 1.2rem;
            margin-bottom: 0.2rem;
        }

        .gregorian-date {
            font-size: 1rem;
        }
    }

    .user-email {
        font-size: 1.2rem;
        margin-bottom: 0.4rem;
    }

    .user-status {
        font-size: 1rem;
    }

    .round-image-container {
        width: 7rem;
        height: 7rem;
        margin: 0 auto;
        position: relative;

        &:lang(en),
        &:lang(ar) {
            margin: 0 auto;
        }
    }

    .profile-section {
        justify-content: center;
    }
}

// Mobile devices
@media screen and (max-width: 600px) {
    .navbar {
        padding: 1.2rem 0.8rem;
        border-radius: 1rem;
    }

    .sub-navbar {
        width: 98%;
        padding: 0.8rem 1rem;
        margin-bottom: 1rem;
    }

    .tenant-name {
        font-size: 1.1rem;
    }

    .navbar .row {
        flex-direction: column;
        gap: 1.2rem;
        margin: 0;
    }

    .navbar .col-md-4 {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin-bottom: 0;
        display: flex;
        justify-content: center;
        text-align: center !important;
    }

    .date-section,
    .user-info {
        padding: 1rem;
        max-width: 90%;
        margin: 0 auto;
        text-align: center;
    }

    .date-section {
        flex-direction: column;
        gap: 0.8rem;
        align-items: center;
    }

    .date-icon {
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
    }

    .date-content {
        text-align: center;

        .hijri-date {
            font-size: 1.1rem;
            margin-bottom: 0.3rem;
        }

        .gregorian-date {
            font-size: 0.95rem;
        }
    }

    .user-info {
        text-align: center;
    }

    .user-email {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .user-status {
        font-size: 1rem;
        justify-content: center;
    }

    .round-image-container {
        width: 6.5rem;
        height: 6.5rem;
        position: relative;
        margin: 0 auto;

        &:lang(en),
        &:lang(ar) {
            margin: 0 auto;
        }
    }

    .profile-section {
        justify-content: center;
        width: 100%;
    }

    // RTL/LTR Mobile Adjustments
    [dir="rtl"]{
        .date-section {
            text-align: center;
            flex-direction: column;
        }

        .date-content {
            text-align: center;
        }

        .user-info {
            text-align: center;
        }

        .user-status {
            justify-content: center;
            flex-direction: row;
        }

        .profile-section {
            justify-content: center;
        }

        .round-image-container {
            margin: 0 auto;
        }

        .profile-status {
            right: auto;
            left: 0.3rem;
        }
    }

    [dir="ltr"] {
        .date-section {
            text-align: center;
            flex-direction: column;
        }

        .date-content {
            text-align: center;
        }

        .user-info {
            text-align: center;
        }

        .user-status {
            justify-content: center;
            flex-direction: row;
        }

        .profile-section {
            justify-content: center;
        }

        .round-image-container {
            margin: 0 auto;
        }

        .profile-status {
            right: 0.3rem;
            left: auto;
        }
    }
}

// Small mobile devices
@media screen and (max-width: 480px) {
    .sub-navbar {
        width: 100%;
        margin-bottom: 0.8rem;
        padding: 0.6rem 1rem;
        border-radius: 0.8rem;
    }

    .tenant-info {
        font-size: 1rem;
        gap: 0.6rem;
    }

    .tenant-icon {
        font-size: 1.3rem;
    }

    .tenant-name {
        font-size: 1rem;
    }

    .navbar {
        padding: 1rem 0.5rem;
        border-radius: 0.8rem;
    }

    .navbar .row {
        gap: 1rem;
    }

    .date-section,
    .user-info {
        padding: 0.8rem;
        max-width: 95%;
        border-radius: 0.8rem;
    }

    .date-icon {
        font-size: 2.2rem;
    }

    .date-content {
        .hijri-date {
            font-size: 1rem;
            margin-bottom: 0.2rem;
        }

        .gregorian-date {
            font-size: 0.85rem;
        }
    }

    .user-email {
        font-size: 1rem;
        margin-bottom: 0.4rem;
    }

    .user-status {
        font-size: 0.85rem;
    }

    .status-dot {
        width: 0.6rem;
        height: 0.6rem;
    }

    .round-image-container {
        width: 5.5rem;
        height: 5.5rem;
        border-width: 2px;
    }

    .profile-status {
        width: 1.5rem;
        height: 1.5rem;
        bottom: 0.2rem;
        right: 0.2rem;
    }

    [dir="rtl"] .profile-status {
        right: auto;
        left: 0.2rem;
    }

    // RTL/LTR Small Mobile Adjustments
    [dir="rtl"] {
        .tenant-info {
            flex-direction: row-reverse;
            text-align: center;
        }

        .date-section {
            text-align: center;
            flex-direction: column;
        }

        .user-status {
            justify-content: center;
        }
    }

    [dir="ltr"]  {
        .tenant-info {
            flex-direction: row;
            text-align: center;
        }

        .date-section {
            text-align: center;
            flex-direction: column;
        }

        .user-status {
            justify-content: center;
        }
    }
}

// Extra small devices
@media screen and (max-width: 360px) {
    .sub-navbar {
        padding: 0.5rem 0.8rem;
        margin-bottom: 0.6rem;
    }

    .tenant-info {
        font-size: 1rem;
        gap: 0.5rem;
    }

    .tenant-icon {
        font-size: 1.2rem;
    }

    .navbar {
        padding: 0.8rem 0.3rem;
    }

    .navbar .row {
        gap: 0.8rem;
    }

    .date-section,
    .user-info {
        padding: 0.6rem;
        max-width: 98%;
    }

    .date-icon {
        font-size: 2rem;
    }

    .date-content {
        .hijri-date {
            font-size: 1rem;
        }

        .gregorian-date {
            font-size: 0.8rem;
        }
    }

    .user-email {
        font-size: 1rem;
    }

    .user-status {
        font-size: 0.8rem;
        gap: 0.4rem;
    }

    .round-image-container {
        width: 5rem;
        height: 5rem;
    }

    .profile-status {
        width: 1.3rem;
        height: 1.3rem;
    }

    // RTL/LTR Extra Small Mobile Adjustments
    [dir="rtl"] {
        .tenant-info {
            flex-direction: row-reverse;
            text-align: center;
            justify-content: center;
        }

        .date-section {
            text-align: center;
            flex-direction: column;
            align-items: center;
        }

        .date-content {
            text-align: center;
        }

        .user-info {
            text-align: center;
        }

        .user-status {
            justify-content: center;
            flex-direction: row;
        }

        .profile-status {
            right: auto;
            left: 0.2rem;
        }
    }

    [dir="ltr"]  {
        .tenant-info {
            flex-direction: row;
            text-align: center;
            justify-content: center;
        }

        .date-section {
            text-align: center;
            flex-direction: column;
            align-items: center;
        }

        .date-content {
            text-align: center;
        }

        .user-info {
            text-align: center;
        }

        .user-status {
            justify-content: center;
            flex-direction: row;
        }

        .profile-status {
            right: 0.2rem;
            left: auto;
        }
    }
}
