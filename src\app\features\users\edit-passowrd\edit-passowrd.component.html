<div *nzModalTitle> {{'EditPASSWORD.EDITPASSWORD'|translate}}</div>
<form [formGroup]="form">
    <div class="container">
        <div class="form-group">
            <label>{{'EditPASSWORD.NEWPASWORD'|translate}}</label>

            <div class="input-group">
                <input [type]="inputType" id="newPassword" class="form-control" formControlName="newPassword"
                    name="newPassword" aria-label="Recipient's username" aria-describedby="basic-addon2">
                <span class="input-group-text" id="basic-addon2" (click)="toggleVisibility()">
                    <span nz-icon *ngIf="visible" nzType="eye" nzTheme="outline"></span>
                    <span nz-icon *ngIf="!visible" nzType="eye-invisible" nzTheme="outline"></span>
                </span>
            </div>
            <ng-container *ngIf="form && form?.get('newPassword')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('newPassword')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>
        <div class="form-group">
            <label>{{'EditPASSWORD.CONFIRMPASSWORD'|translate}}</label>
            <div class="input-group">
                <input [type]="inputTypeNewPassword" id="confirmPassword" class="form-control" formControlName="confirmPassword"
                    name="confirmPassword">
                <span class="input-group-text" id="basic-addon2" (click)="toggleVisibilityNewPassword()">
                    <span nz-icon *ngIf="visibleNewPassword" nzType="eye" nzTheme="outline"></span>
                    <span nz-icon *ngIf="!visibleNewPassword" nzType="eye-invisible" nzTheme="outline"></span>
                </span>
            </div>
            <ng-container *ngIf="form && form?.get('confirmPassword')?.dirty">
                <p class="text-danger text-error" *ngIf="form && form?.get('confirmPassword')?.hasError('required')  ">
                    {{'SHARED.THISFIELDISREQUIRED'|translate}}
                </p>
            </ng-container>
        </div>



    </div>
    <div *nzModalFooter>
        <button class="btn btn-danger text-white mx-3" (click)="destroyModal()">{{'SHARED.CANCEL'|translate}}</button>
        <button class="btn btn-primary" type="submit" (click)="updatePassword()">{{'SHARED.UPDATE'|translate}}</button>
    </div>
</form>