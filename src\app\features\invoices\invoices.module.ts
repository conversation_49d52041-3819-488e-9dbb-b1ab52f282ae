import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InvoicesComponent } from './invoices/invoices.component';
import { AddEditInvoiceComponent } from './add-edit-invoice/add-edit-invoice.component';
import { SharedModule } from '../../shared/shared.module';
import { InvoicesRoutingModule } from './invoice-routing.module';
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";



@NgModule({
  declarations: [
    InvoicesComponent,
    AddEditInvoiceComponent
  ],
    imports: [
        CommonModule,
        InvoicesRoutingModule,
        SharedModule,
        ReusableDropdownComponent
    ]
})
export class InvoicesModule { }
