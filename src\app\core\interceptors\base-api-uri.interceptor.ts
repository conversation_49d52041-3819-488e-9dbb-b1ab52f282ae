import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ClientHeaders } from '../enums/client-headers.enum';

@Injectable()
export class BaseApiUriInterceptor implements HttpInterceptor {
    excludedExtensionsUrls = ['.svg', '.json', 'format=json'];
    constructor() {

    }

    intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {

        if (request.headers.has(ClientHeaders.external) || request.headers.has(ClientHeaders.ignoreBaseUri) || this.checkExExtensionsUrls(request.url)) {
            return next.handle(request);
        }
        return next.handle(request.clone({
            url: environment.apiUrl + request.url
        }));
    }

    checkExExtensionsUrls(url: string): boolean {
        let listCheck: boolean[] = [];
        this.excludedExtensionsUrls.forEach((excludedUrl) => listCheck.push(url.endsWith(excludedUrl)));
        return listCheck.includes(true);
    }
}
