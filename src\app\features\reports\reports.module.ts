import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReportsRoutingModule } from './reports-routing.module';
import { ReportsComponent } from './reports.component';
import {SharedModule} from "../../shared/shared.module";
import {ReusableDropdownComponent} from "../../shared/components/reusable-dropdown/reusable-dropdown.component";
import { AnalyticalReportsComponent } from './analytical-reports/analytical-reports.component';
import {MatAnchor, MatButton} from "@angular/material/button";
import {MultiSelectModule} from "primeng/multiselect";
import { EstimatedBudgetReportComponent } from './estimated-budget-report/estimated-budget-report.component';
import { AllFinalReportsComponent } from './all-final-reports/all-final-reports.component';


@NgModule({
  declarations: [
    ReportsComponent,
    AnalyticalReportsComponent,
    EstimatedBudgetReportComponent,
    AllFinalReportsComponent
  ],
  imports: [
    CommonModule,
    ReportsRoutingModule,
    SharedModule,
    ReusableDropdownComponent,
    MatButton,
    MatAnchor,
    MultiSelectModule,
  ]
})
export class ReportsModule { }
